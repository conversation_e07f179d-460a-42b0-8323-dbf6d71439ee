package org.zstack.header.buildsystem;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
@RestRequest(
        path = "/appcenter/buildsystem/{uuid}/actions",
        isAction = true,
        responseClass = APIChangeAppBuildSystemStateEvent.class,
        method = HttpMethod.PUT
)
public class APIChangeAppBuildSystemStateMsg extends APIMessage {
    @APIParam(resourceType = AppBuildSystemVO.class)
    private String uuid;
    @APIParam(validValues = {"enable", "disable"})
    private String stateEvent;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getStateEvent() {
        return stateEvent;
    }

    public void setStateEvent(String stateEvent) {
        this.stateEvent = stateEvent;
    }

    public static APIChangeAppBuildSystemStateMsg __example__() {
        APIChangeAppBuildSystemStateMsg msg = new APIChangeAppBuildSystemStateMsg();
        msg.setUuid(uuid());
        msg.setStateEvent("disable");

        return msg;
    }
}
