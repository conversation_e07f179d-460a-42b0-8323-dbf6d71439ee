package org.zstack.header.buildsystem;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.zone.ZoneVO;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
@RestRequest(
        path = "/zones/{zoneUuid}/buildsystem/{buildSystemUuid}",
        method = HttpMethod.DELETE,
        responseClass = APIDetachAppBuildSystemToZoneEvent.class
)
public class APIDetachAppBuildSystemToZoneMsg extends APIMessage {
    @APIParam(resourceType = AppBuildSystemVO.class)
    private String buildSystemUuid;

    @APIParam(resourceType = ZoneVO.class)
    private String zoneUuid;

    public String getBuildSystemUuid() {
        return buildSystemUuid;
    }

    public void setBuildSystemUuid(String buildSystemUuid) {
        this.buildSystemUuid = buildSystemUuid;
    }

    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    public static APIDetachAppBuildSystemToZoneMsg __example__() {
        APIDetachAppBuildSystemToZoneMsg msg = new APIDetachAppBuildSystemToZoneMsg();
        msg.setBuildSystemUuid(uuid());
        msg.setZoneUuid(uuid());

        return msg;
    }
}
