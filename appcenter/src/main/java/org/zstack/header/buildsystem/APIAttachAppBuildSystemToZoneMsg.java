package org.zstack.header.buildsystem;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.zone.ZoneVO;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
@RestRequest(
        path = "/zones/{zoneUuid}/buildsystem/{buildSystemUuid}",
        method = HttpMethod.POST,
        parameterName = "params",
        responseClass = APIAttachAppBuildSystemToZoneEvent.class
)
public class APIAttachAppBuildSystemToZoneMsg extends APIMessage {
    @APIParam(resourceType = ZoneVO.class)
    private String zoneUuid;

    @APIParam(resourceType = AppBuildSystemVO.class)
    private String buildSystemUuid;

    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    public String getBuildSystemUuid() {
        return buildSystemUuid;
    }

    public void setBuildSystemUuid(String buildSystemUuid) {
        this.buildSystemUuid = buildSystemUuid;
    }

    public static APIAttachAppBuildSystemToZoneMsg __example__() {
        APIAttachAppBuildSystemToZoneMsg msg = new APIAttachAppBuildSystemToZoneMsg();
        msg.setZoneUuid(uuid());
        msg.setBuildSystemUuid(uuid());

        return msg;
    }
}
