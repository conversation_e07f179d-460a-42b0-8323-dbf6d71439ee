package org.zstack.header.buildsystem;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
@RestResponse(allTo = "inventory")
public class APIAttachAppBuildSystemToZoneEvent extends APIEvent {
    public APIAttachAppBuildSystemToZoneEvent() {
    }

    public APIAttachAppBuildSystemToZoneEvent(String apiId) {
        super(apiId);
    }

    private AppBuildSystemZoneRefInventory inventory;

    public AppBuildSystemZoneRefInventory getInventory() {
        return inventory;
    }

    public void setInventory(AppBuildSystemZoneRefInventory inventory) {
        this.inventory = inventory;
    }

    public static APIAttachAppBuildSystemToZoneEvent __example__() {
        APIAttachAppBuildSystemToZoneEvent event = new APIAttachAppBuildSystemToZoneEvent();
        AppBuildSystemZoneRefInventory inventory = new AppBuildSystemZoneRefInventory();
        inventory.setZoneUuid(uuid());
        inventory.setBuildSystemUuid(uuid());
        inventory.setId(1L);

        event.setInventory(inventory);

        return event;
    }
}
