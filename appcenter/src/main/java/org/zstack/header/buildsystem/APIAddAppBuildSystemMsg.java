package org.zstack.header.buildsystem;

import org.springframework.http.HttpMethod;
import org.zstack.appcenter.buildsystem.AppBuildSystemConstant;
import org.zstack.header.identity.Action;
import org.zstack.header.log.NoLogging;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;

import java.io.Serializable;

/**
 * Created by mingjian.deng on 2019/5/15.
 */
@Action(category = AppBuildSystemConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/appcenter/buildsystem",
        method = HttpMethod.POST,
        responseClass = APIAddAppBuildSystemEvent.class,
        parameterName = "params"
)
public class APIAddAppBuildSystemMsg extends APICreateMessage implements APIAuditor, Serializable {
    @APIParam(maxLength = 1024, emptyString = false)
    private String url;

    @APIParam(maxLength = 255, emptyString = false)
    private String name;

    @APIParam(required = false, maxLength = 2048)
    private String description;

    @APIParam(validValues = "localStorage", required = false)
    private String storageType;

    @APIParam(maxLength = 255)
    private String username;

    @APIParam(maxLength = 255, password = true)
    @NoLogging
    private String password;

    @APIParam(maxLength = 255, emptyString = false)
    private String hostname;

    @APIParam(numberRange = {1, 65535}, required = false)
    private Integer sshPort;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStorageType() {
        return storageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public Integer getSshPort() {
        return sshPort;
    }

    public void setSshPort(Integer sshPort) {
        this.sshPort = sshPort;
    }


    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(rsp.isSuccess() ? ((APIAddAppBuildSystemEvent)rsp).getInventory().getUuid() : "", AppBuildSystemVO.class);
    }

    public static APIAddAppBuildSystemMsg __example__() {
        APIAddAppBuildSystemMsg msg = new APIAddAppBuildSystemMsg();
        msg.setSshPort(22);
        msg.setHostname("127.0.0.1");
        msg.setPassword("password");
        msg.setUsername("root");
        msg.setUrl("/zstack_build");
        msg.setName("buildapp");
        msg.setDescription("description");

        return msg;
    }
}
