package org.zstack.header.buildsystem;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
@StaticMetamodel(AppBuildSystemZoneRefVO.class)
public class AppBuildSystemZoneRefVO_ {
    public static volatile SingularAttribute<AppBuildSystemZoneRefVO, Long> id;
    public static volatile SingularAttribute<AppBuildSystemZoneRefVO, String> buildSystemUuid;
    public static volatile SingularAttribute<AppBuildSystemZoneRefVO, String> zoneUuid;
}
