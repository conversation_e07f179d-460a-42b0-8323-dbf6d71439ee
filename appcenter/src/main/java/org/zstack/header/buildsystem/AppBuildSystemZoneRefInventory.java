package org.zstack.header.buildsystem;

import org.zstack.header.query.ExpandedQueries;
import org.zstack.header.query.ExpandedQuery;
import org.zstack.header.search.Inventory;
import org.zstack.header.zone.ZoneInventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
@Inventory(mappingVOClass = AppBuildSystemZoneRefVO.class)
@ExpandedQueries({
        @ExpandedQuery(expandedField = "zone", inventoryClass = ZoneInventory.class,
                foreignKey = "zoneUuid", expandedInventoryKey = "uuid"),
        @ExpandedQuery(expandedField = "buildSystem", inventoryClass = AppBuildSystemInventory.class,
                foreignKey = "buildSystemUuid", expandedInventoryKey = "uuid"),
})
public class AppBuildSystemZoneRefInventory implements Serializable {
    private Long id;
    private String buildSystemUuid;
    private String zoneUuid;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public AppBuildSystemZoneRefInventory() {
    }

    public static AppBuildSystemZoneRefInventory valueOf(AppBuildSystemZoneRefVO vo) {
        AppBuildSystemZoneRefInventory inv = new AppBuildSystemZoneRefInventory();
        inv.setId(vo.getId());
        inv.setBuildSystemUuid(vo.getBuildSystemUuid());
        inv.setZoneUuid(vo.getZoneUuid());
        inv.setCreateDate(vo.getCreateDate());
        inv.setLastOpDate(vo.getLastOpDate());
        return inv;
    }

    public static List<AppBuildSystemZoneRefInventory> valueOf(Collection<AppBuildSystemZoneRefVO> vos) {
        List<AppBuildSystemZoneRefInventory> invs = new ArrayList<>();
        for (AppBuildSystemZoneRefVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBuildSystemUuid() {
        return buildSystemUuid;
    }

    public void setBuildSystemUuid(String buildSystemUuid) {
        this.buildSystemUuid = buildSystemUuid;
    }

    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
