package org.zstack.header.buildsystem;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.utils.data.SizeUnit;

/**
 * Created by mingjian.deng on 2019/5/20.
 */
@RestResponse(allTo = "inventory")
public class APIReconnectAppBuildSystemEvent extends APIEvent {
    private AppBuildSystemInventory inventory;

    public APIReconnectAppBuildSystemEvent() {
    }

    public APIReconnectAppBuildSystemEvent(String apiId) {
        super(apiId);
    }

    public AppBuildSystemInventory getInventory() {
        return inventory;
    }

    public void setInventory(AppBuildSystemInventory inventory) {
        this.inventory = inventory;
    }

    public static APIReconnectAppBuildSystemEvent __example__() {
        APIReconnectAppBuildSystemEvent event = new APIReconnectAppBuildSystemEvent();
        AppBuildSystemInventory inventory = new AppBuildSystemInventory();
        inventory.setUuid(uuid());
        inventory.setTotalCapacity(SizeUnit.GIGABYTE.toByte(20));
        inventory.setAvailableCapacity(SizeUnit.GIGABYTE.toByte(18));
        inventory.setUrl("/zstack_build");
        inventory.setStorageType("localStorage");
        inventory.setSshPort(22);
        inventory.setPassword("******");
        inventory.setUsername("root");
        inventory.setHostname("127.0.0.1");
        inventory.setState("Enabled");
        inventory.setStatus("Connected");

        event.setInventory(inventory);
        return event;
    }
}
