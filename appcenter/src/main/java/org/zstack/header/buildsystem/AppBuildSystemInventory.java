package org.zstack.header.buildsystem;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.message.GsonTransient;
import org.zstack.header.message.NoJsonSchema;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by mingjian.deng on 2019/5/15.
 */
@Inventory(mappingVOClass = AppBuildSystemVO.class)
@PythonClassInventory
public class AppBuildSystemInventory implements Serializable {
    private String uuid;
    private String name;
    private String description;
    private String storageType;
    private String url;
    private String hostname;
    private String username;
    @GsonTransient
    @APINoSee
    @NoJsonSchema
    private String password;
    private int sshPort;
    private String status;
    private String state;
    private long totalCapacity;
    private long availableCapacity;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStorageType() {
        return storageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getSshPort() {
        return sshPort;
    }

    public void setSshPort(int sshPort) {
        this.sshPort = sshPort;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public long getTotalCapacity() {
        return totalCapacity;
    }

    public void setTotalCapacity(long totalCapacity) {
        this.totalCapacity = totalCapacity;
    }

    public long getAvailableCapacity() {
        return availableCapacity;
    }

    public void setAvailableCapacity(long availableCapacity) {
        this.availableCapacity = availableCapacity;
    }

    public AppBuildSystemInventory() {
    }

    public static AppBuildSystemInventory valueOf(AppBuildSystemVO vo) {
        AppBuildSystemInventory inv = new AppBuildSystemInventory();
        inv.setUuid(vo.getUuid());
        inv.setName(vo.getName());
        inv.setDescription(vo.getDescription());
        inv.setHostname(vo.getHostname());
        inv.setUsername(vo.getUsername());
        inv.setPassword(vo.getPassword());
        inv.setSshPort(vo.getSshPort());
        inv.setState(vo.getState().toString());
        inv.setStatus(vo.getStatus().toString());
        inv.setStorageType(vo.getStorageType().toString());
        inv.setUrl(vo.getUrl());
        inv.setAvailableCapacity(vo.getAvailableCapacity());
        inv.setTotalCapacity(vo.getTotalCapacity());
        inv.setCreateDate(vo.getCreateDate());
        inv.setLastOpDate(vo.getLastOpDate());
        return inv;
    }

    public static List<AppBuildSystemInventory> valueOf(Collection<AppBuildSystemVO> vos) {
        List<AppBuildSystemInventory> invs = new ArrayList<>();
        for (AppBuildSystemVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }
}
