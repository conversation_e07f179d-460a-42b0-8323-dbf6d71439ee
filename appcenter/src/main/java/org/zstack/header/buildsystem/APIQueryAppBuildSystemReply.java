package org.zstack.header.buildsystem;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;
import org.zstack.utils.data.SizeUnit;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 2019/5/16.
 */
@RestResponse(allTo = "inventories")
public class APIQueryAppBuildSystemReply extends APIQueryReply {
    private List<AppBuildSystemInventory> inventories = new ArrayList<>();

    public List<AppBuildSystemInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<AppBuildSystemInventory> inventories) {
        this.inventories = inventories;
    }

    public static APIQueryAppBuildSystemReply __example__() {
        APIQueryAppBuildSystemReply reply = new APIQueryAppBuildSystemReply();
        AppBuildSystemInventory inventory = new AppBuildSystemInventory();
        inventory.setUuid(uuid());
        inventory.setTotalCapacity(SizeUnit.GIGABYTE.toByte(20));
        inventory.setAvailableCapacity(SizeUnit.GIGABYTE.toByte(18));
        inventory.setUrl("/zstack_build");
        inventory.setStorageType("localStorage");
        inventory.setSshPort(22);
        inventory.setPassword("******");
        inventory.setUsername("root");
        inventory.setHostname("127.0.0.1");
        inventory.setState("Enabled");
        inventory.setStatus("Connected");

        reply.getInventories().add(inventory);
        return reply;
    }
}
