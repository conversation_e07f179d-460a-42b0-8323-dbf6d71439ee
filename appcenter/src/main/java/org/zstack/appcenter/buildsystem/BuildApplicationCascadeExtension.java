package org.zstack.appcenter.buildsystem;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.buildapp.BuildAppDeleteMsg;
import org.zstack.header.buildapp.BuildApplicationInventory;
import org.zstack.header.buildapp.BuildApplicationVO;
import org.zstack.header.buildapp.BuildApplicationVO_;
import org.zstack.header.buildsystem.AppBuildSystemInventory;
import org.zstack.header.buildsystem.AppBuildSystemVO;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by mingjian.deng on 2019/5/27.
 */
public class BuildApplicationCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(BuildApplicationCascadeExtension.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    private static final String NAME = BuildApplicationVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            completion.success();
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            completion.success();
        } else {
            completion.success();
        }
    }

    private void handleDeletion(final CascadeAction action, final Completion completion) {
        final List<BuildApplicationInventory> appinvs = buildAppFromAction(action);
        if (appinvs == null) {
            completion.success();
            return;
        }

        ErrorCodeList errs = new ErrorCodeList();
        new While<>(appinvs).all((appinv, com) -> {
            BuildAppDeleteMsg dmsg = new BuildAppDeleteMsg();
            dmsg.setAppUuid(appinv.getUuid());
            String bsUuid = appinv.getBuildSystemUuid();

            dmsg.setBuildSystemUuid(bsUuid);
            bus.makeTargetServiceIdByResourceUuid(dmsg, AppBuildSystemConstant.SERVICE_ID, appinv.getUuid());
            bus.send(dmsg, new CloudBusCallBack(com) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("delete build application failed, because: %s", reply.getError().getDetails()));
                        errs.getCauses().add(reply.getError());
                    } else {
                        dbf.removeByPrimaryKey(appinv.getUuid(), BuildApplicationVO.class);
                        logger.debug(String.format("deleted build application[uuid:%s, name:%s]", appinv.getUuid(), appinv.getName()));
                    }
                    com.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errs.getCauses().isEmpty()) {
                    completion.fail(errs.getCauses().get(0));
                } else {
                    completion.success();
                }
            }
        });
    }

    @Override
    public List<String> getEdgeNames() {
        return CollectionDSL.list(AppBuildSystemVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Transactional(readOnly = true)
    private List<BuildApplicationInventory> getFromBuildSystem(List<AppBuildSystemInventory> invs) {
        List<String> bsUuids = invs.stream().map(AppBuildSystemInventory::getUuid).collect(Collectors.toList());

        List<BuildApplicationVO> apps = Q.New(BuildApplicationVO.class).in(BuildApplicationVO_.buildSystemUuid, bsUuids).list();

        if (apps.isEmpty()) {
            return null;
        }
        return BuildApplicationInventory.valueOf(apps);
    }

    private List<BuildApplicationInventory> buildAppFromAction(CascadeAction action) {
        if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        } else if (AppBuildSystemVO.class.getSimpleName().equals(action.getParentIssuer())) {
            return getFromBuildSystem((List<AppBuildSystemInventory>)action.getParentIssuerContext());
        } else {
            return null;
        }
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<BuildApplicationInventory> ctx = buildAppFromAction(action);
            if (ctx != null && !ctx.isEmpty()) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}
