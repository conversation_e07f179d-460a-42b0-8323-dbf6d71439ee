package org.zstack.appcenter.buildsystem;

import com.google.gson.annotations.SerializedName;
import org.zstack.appcenter.AppCenterGlobalProperty;
import org.zstack.core.ansible.AbstractAnsibleAgentDeployArguments;

public class AppBuildSystemDeployArguments extends AbstractAnsibleAgentDeployArguments {
    @SerializedName("pkg_appbuildsystemagent")
    private final String packageName = AppCenterGlobalProperty.BUILD_SYSTEM_PACKAGE_NAME;

    @Override
    public String getPackageName() {
        return packageName;
    }
}
