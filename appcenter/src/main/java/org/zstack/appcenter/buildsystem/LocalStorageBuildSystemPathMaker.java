package org.zstack.appcenter.buildsystem;

import org.zstack.utils.TimeUtils;
import org.zstack.utils.path.PathUtil;

/**
 * Created by mingjian.deng on 2019/5/23.
 */
public class LocalStorageBuildSystemPathMaker {
    private String url;
    public static String buildPath = "builds";
    public static String exportPath = "exports";
    public static String rawappPath = "rawapps";
    public static String downloadPath = "downloads";

    public LocalStorageBuildSystemPathMaker(String url) {
        this.url = url;
    }

    public String makeBuildPath(String appUuid) {
        return PathUtil.join(url, buildPath, appUuid);
    }

    public String makeExportPath(String appUuid) {
        return makeExportPath(appUuid, true);
    }

    public String makeExportPath(String appUuid, boolean time) {
        if (time) {
            return PathUtil.join(url, exportPath, appUuid, TimeUtils.getCurrentTimeStamp("yyyyMMddHHmmss"));
        } else {
            return PathUtil.join(url, exportPath, appUuid);
        }
    }

    public String makeTemplateContextPath(String dstPath) {
        return PathUtil.join(dstPath, AppBuildSystemConstant.rawTemplateFileName);
    }

    public String makeApplicationDescPath(String dstPath) {
        return PathUtil.join(dstPath, AppBuildSystemConstant.applicationMetaFileName);
    }

    public String makeVmRelationShipPath(String dstPath) {
        return PathUtil.join(dstPath, AppBuildSystemConstant.vmRelationShip);
    }

    public String makeRawAppPath(String appUuid) {
        return PathUtil.join(url, rawappPath, appUuid);
    }

    public String makeDownloadAppPath(String appUuid) {
        return PathUtil.join(url, downloadPath, appUuid);
    }
}
