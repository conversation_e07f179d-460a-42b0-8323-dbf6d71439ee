package org.zstack.appcenter.buildsystem;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.web.util.UriComponentsBuilder;
import org.zstack.appcenter.AppCenterGlobalProperty;
import org.zstack.appcenter.utils.AppCenterUtils;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.buildapp.*;
import org.zstack.header.buildsystem.*;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NoErrorCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.image.*;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.rest.JsonAsyncRESTCallback;
import org.zstack.header.rest.RESTFacade;
import org.zstack.image.ImageSystemTags;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageInventory;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageVO;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.path.PathUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.zstack.core.Platform.operr;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * Created by mingjian.deng on 2019/5/16.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class LocalStorageBuildSystemBase implements AppBuildSystem {
    private static final CLogger logger = Utils.getLogger(LocalStorageBuildSystemBase.class);
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private RESTFacade restf;
    @Autowired
    protected AppBuildSystemPingTracker tracker;

    private AppBuildSystemVO self;
    private LocalStorageBuildSystemPathMaker pathMaker;
    private String baseUrl;

    public static final String ECHO_BUILDSYSTEM_PATH = "/appcenter/buildsystem/echo";
    public static final String PING_BUILDSYSTEM_PATH = "/appcenter/buildsystem/ping";
    public static final String CONNECT_BUILDSYSTEM_PATH = "/appcenter/buildsystem/connect";
    public static final String CHECK_BUILDSYSTEM_PATH = "/appcenter/buildsystem/checkpath";
    public static final String CREATE_APPLICATION_PATH = "/appcenter/buildsystem/createapp";
    public static final String DELETE_APPLICATION_PATH = "/appcenter/buildsystem/deleteapp";
    public static final String DELETE_EXPORT_APPLICATION_PATH = "/appcenter/buildsystem/deleteexportapp";
    public static final String EXPORT_APPLICATION_PATH = "/appcenter/buildsystem/exportapp";
    public static final String UNZIP_BUILDAPP = "/appcenter/rawapp/unzip";
    public static final String DOWNLOAD_BUILDAPP = "/appcenter/rawapp/download";
    public static final String DELETE_DOWNLOAD_BUILDAPP = "/appcenter/rawapp/deletedownload";
    public static final String CLEAN_UNZIP_BUILDAPP = "/appcenter/rawapp/cleanunzip";


    public LocalStorageBuildSystemBase() {
    }

    public LocalStorageBuildSystemBase(AppBuildSystemVO vo) {
        this.self = vo;

        UriComponentsBuilder ub = UriComponentsBuilder.newInstance();
        ub.scheme("http");
        ub.host(vo.getHostname());
        ub.port(AppCenterGlobalProperty.BUILD_SYSTEM_AGENT_PORT);
        baseUrl = ub.build().toUriString();

        pathMaker = new LocalStorageBuildSystemPathMaker(vo.getUrl());
    }

    private void updateSelf() {
        self = dbf.updateAndRefresh(self);
    }

    private void checkBuildSystemStatus() {
        if (self == null) {
            return;
        }
        if (AppBuildSystemStatus.Connected != self.getStatus()) {
            throw new OperationFailureException(operr("unable to do the operation because the build system is in status of %s", self.getStatus()));
        }
    }

    private BuildApplicationInventory getBuildApp(String appUuid) {
        BuildApplicationVO avo = dbf.findByUuid(appUuid, BuildApplicationVO.class);
        return avo == null ? null : BuildApplicationInventory.valueOf(avo);
    }

    private void checkBuildAppStatus(final BuildAppMessage msg) {
        if (msg.getBuildAppUuid() != null && !msg.allowedStatus().isEmpty()) {
            BuildApplicationInventory buildapp = getBuildApp(msg.getBuildAppUuid());
            if (buildapp == null) {
                throw new OperationFailureException(operr("cannot find the build app by uuid[%s]", msg.getBuildAppUuid()));
            }

            BuildApplicationStatus status = BuildApplicationStatus.valueOf(buildapp.getStatus());
            if (!msg.allowedStatus().contains(status)) {
                throw new OperationFailureException(operr("build app is in %s status, which can not support the current operation.", status.toString()));
            }
        }
    }

    private String buildUrl(String subPath) {
        UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(subPath);
        return ub.build().toUriString();
    }

    public <T> void httpCall(final String path, final Object cmd, final Class<T> retClass, final ReturnValueCompletion<T> completion) {
        httpCall(path, cmd, retClass, completion, null, 0);
    }

    public <T> void httpCall(final String path, final Object cmd, final Class<T> retClass, final ReturnValueCompletion<T> completion, TimeUnit unit, long timeout) {
        JsonAsyncRESTCallback<T> callback = new JsonAsyncRESTCallback<T>(completion) {
            @Override
            public void fail(ErrorCode err) {
                completion.fail(err);
            }

            @Override
            public void success(T ret) {
                if (ret instanceof LocalStorageBuildSystemCommands.AgentResponse) {
                    LocalStorageBuildSystemCommands.AgentResponse rsp = (LocalStorageBuildSystemCommands.AgentResponse)ret;
                    if (rsp.success) {
                        updateCapacity(rsp);
                        completion.success(ret);
                    } else {
                        completion.fail(operr("rest call %s failed, because: %s", buildUrl(path), rsp.error));
                    }

                } else {
                    completion.success(ret);
                }
            }

            @Override
            public Class<T> getReturnClass() {
                return retClass;
            }
        };

        if (unit == null) {
            restf.asyncJsonPost(buildUrl(path), cmd, callback);
        } else {
            restf.asyncJsonPost(buildUrl(path), cmd, callback, unit, timeout);
        }
    }

    protected void updateCapacity(LocalStorageBuildSystemCommands.AgentResponse rsp) {
        if (self == null) {
            return;
        }
        if (rsp.totalCapacity == null || rsp.availableCapacity == null) {
            return;
        }

        if (rsp.availableCapacity == self.getAvailableCapacity() && rsp.totalCapacity == self.getTotalCapacity()) {
            return;
        }

        logger.debug(String.format("storage capacity of appcenter buildsystem [%s] has been changed, \n" +
                "availableCapacity from %s to %s,\n" +
                "totalCapacity from %s to %s", self.getUuid(), self.getAvailableCapacity(),
                rsp.availableCapacity, self.getTotalCapacity(), rsp.totalCapacity));

        self.setTotalCapacity(rsp.totalCapacity);
        self.setAvailableCapacity(rsp.availableCapacity);
        updateSelf();
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof BuildAppMessage) {
            checkBuildSystemStatus();
            checkBuildAppStatus((BuildAppMessage) msg);
        }

        if (msg instanceof ConnectAppBuildSystemMsg) {
            handle((ConnectAppBuildSystemMsg) msg);
        } else if (msg instanceof BuildSystemDeleteMsg) {
            handle((BuildSystemDeleteMsg) msg);
        } else if (msg instanceof CreateBuildAppMsg) {
            handle((CreateBuildAppMsg) msg);
        } else if (msg instanceof BuildAppDeleteMsg) {
            handle((BuildAppDeleteMsg) msg);
        } else if (msg instanceof DeleteBuildAppHistoryMsg) {
            handle((DeleteBuildAppHistoryMsg) msg);
        } else if (msg instanceof ExportBuildAppMsg) {
            handle((ExportBuildAppMsg) msg);
        } else if (msg instanceof PingAppBuildSystemMsg) {
            handle((PingAppBuildSystemMsg) msg);
        } else if (msg instanceof AddBuildAppMsg) {
            handle((AddBuildAppMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(final PingAppBuildSystemMsg msg) {
        MessageReply reply = new MessageReply();

        restf.echo(buildUrl(ECHO_BUILDSYSTEM_PATH), new Completion(msg) {
            @Override
            public void success() {
                LocalStorageBuildSystemCommands.PingCommand cmd = new LocalStorageBuildSystemCommands.PingCommand();
                cmd.url = self.getUrl();
                cmd.uuid = self.getUuid();
                httpCall(PING_BUILDSYSTEM_PATH, cmd,
                        LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                            @Override
                            public void success(LocalStorageBuildSystemCommands.AgentResponse rsp) {
                                dbf.reload(self);
                                if (self.getStatus() != AppBuildSystemStatus.Connected) {
                                    self.setStatus(AppBuildSystemStatus.Connected);
                                    dbf.updateAndRefresh(self);
                                }
                                bus.reply(msg, reply);
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                dbf.reload(self);
                                if (self.getStatus() != AppBuildSystemStatus.DisConnected) {
                                    self.setStatus(AppBuildSystemStatus.DisConnected);
                                    dbf.updateAndRefresh(self);
                                }
                                bus.reply(msg, reply);
                            }
                        });
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(final ExportBuildAppMsg msg) {
        ExportBuildAppReply reply = new ExportBuildAppReply();
        LocalStorageBuildSystemCommands.AppExportCommand cmd = new LocalStorageBuildSystemCommands.AppExportCommand();

        cmd.uuid = msg.getBuildSystemUuid();
        cmd.exportDir = pathMaker.makeExportPath(msg.getAppUuid());
        cmd.url = self.getUrl();
        cmd.exportCtx = msg.getMetaCtx();
        cmd.appDir = msg.getInstallPath();

        httpCall(EXPORT_APPLICATION_PATH, cmd,
                LocalStorageBuildSystemCommands.AppExportRsp.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AppExportRsp>(msg){
                    @Override
                    public void success(LocalStorageBuildSystemCommands.AppExportRsp rsp) {
                        reply.setMd5sum(rsp.md5Sum);
                        reply.setPath(rsp.exportPath);
                        reply.setSize(rsp.size);
                        bus.reply(msg, reply);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                    }
                });
    }

    private void handle(final DeleteBuildAppHistoryMsg msg) {
        MessageReply reply = new MessageReply();
        LocalStorageBuildSystemCommands.DeleteAppExportCommand cmd = new LocalStorageBuildSystemCommands.DeleteAppExportCommand();

        cmd.uuid = self.getUuid();
        cmd.url = self.getUrl();
        cmd.exportPath = pathMaker.makeExportPath(msg.getAppUuid(), false);
        httpCall(DELETE_EXPORT_APPLICATION_PATH, cmd,
                LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                    @Override
                    public void success(LocalStorageBuildSystemCommands.AgentResponse rsp) {
                        tracker.untrack(self.getUuid());
                        bus.reply(msg, reply);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                    }
                });
    }

    private List<CreateImageStruct> buildImage(String ctx, String dstDir) {
        if (ctx == null) {
            return new ArrayList<>();
        }
        List<CreateImageStruct> images = JSONObjectUtil.toCollection(ctx, ArrayList.class, CreateImageStruct.class);
        for (CreateImageStruct image: images) {
            image.setUrl(PathUtil.join(dstDir, image.getUrl()));
        }
        return images;
    }

    private void addImageToRef(ImageInventory image, String buildAppUuid, String backupStorageUuid, String localName) {
        BuildAppImageRefVO ref = new BuildAppImageRefVO();
        ref.setImageUuid(image.getUuid());
        ref.setBuildAppUuid(buildAppUuid);
        ref.setImageName(localName);
        ref.setBackupStorageUuid(backupStorageUuid);

        dbf.persistAndRefresh(ref);

        SystemTagCreator creator = ImageSystemTags.APPCENTER_BUILD.newSystemTagCreator(image.getUuid());
        creator.setTagByTokens(map(e(ImageSystemTags.APPCENTER_BUILD_TOKEN, buildAppUuid)));
        creator.inherent = true;
        creator.create();
        AppCenterUtils.attachSystemTag(image.getUuid());
    }

    private void validateBuildAppMeta(final BuildAppStruct struct) {

    }

    private void handle(final CreateBuildAppMsg msg) {
        CreateBuildApplicationReply sreply = new CreateBuildApplicationReply();
        if (AppBuildSystemState.Disabled == self.getState()) {
            sreply.setError(operr("build application is disabled because build system is in 'Disabled' state"));
            bus.reply(msg, sreply);
            return;
        }

        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-build-application-%s", msg.getApp().getUuid()));
        chain.then(new ShareFlow() {
            BuildApplicationVO app = dbf.findByUuid(msg.getApp().getUuid(), BuildApplicationVO.class);
            AppImageInfo imageInfo = new AppImageInfo();
            List<ImageInventory> addImages = new ArrayList<>();
            String copyDstPath;
            String destInfo;
            String template;
            String logo;
            List<String> thumbs = new ArrayList<>();
            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = String.format("check-src-dir-and-dst-dir-for-app-%s", app.getUuid());
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        LocalStorageBuildSystemCommands.CheckPathCommand cmd = new LocalStorageBuildSystemCommands.CheckPathCommand();
                        cmd.url = self.getUrl();
                        cmd.srcPath = msg.getDataPath();
                        cmd.dstPath = pathMaker.makeBuildPath(app.getUuid());
                        cmd.uuid = self.getUuid();
                        httpCall(CHECK_BUILDSYSTEM_PATH, cmd,
                                LocalStorageBuildSystemCommands.CheckPathRsp.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.CheckPathRsp>(msg){
                                    @Override
                                    public void success(LocalStorageBuildSystemCommands.CheckPathRsp rsp) {
                                        imageInfo.setUploadPath(msg.getDataPath());
                                        imageInfo.setUploadSize(rsp.srcSize);
                                        imageInfo.setUploadImages(rsp.imageInfos);
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        trigger.fail(errorCode);
                                    }
                                });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("create-image-meta-for-app-%s", app.getUuid());
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        LocalStorageBuildSystemCommands.CreateAppCommand cmd = new LocalStorageBuildSystemCommands.CreateAppCommand();
                        cmd.srcPath = msg.getDataPath();
                        cmd.dstPath = pathMaker.makeBuildPath(app.getUuid());
                        cmd.url = self.getUrl();
                        cmd.uuid = self.getUuid();
                        httpCall(CREATE_APPLICATION_PATH, cmd,
                                LocalStorageBuildSystemCommands.CreateAppRsp.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.CreateAppRsp>(msg){
                                    @Override
                                    public void success(LocalStorageBuildSystemCommands.CreateAppRsp rsp) {
                                        imageInfo.setBuildPath(pathMaker.makeBuildPath(app.getUuid()));
                                        imageInfo.setBuildSize(rsp.dstSize);
                                        imageInfo.setBuildImages(buildImage(rsp.imageInfos, rsp.dstPath));
                                        copyDstPath = rsp.dstPath;
                                        destInfo = rsp.dstInfo;
                                        template = rsp.template;
                                        thumbs.addAll(rsp.thumbs);
                                        logo = rsp.logo;
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        trigger.fail(errorCode);
                                    }
                                });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("generate-image-for-app-%s", app.getUuid());
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (imageInfo.getBuildImages().isEmpty()) {
                            trigger.next();
                            return;
                        }

                        ErrorCodeList errList = new ErrorCodeList();
                        new While<>(imageInfo.getBuildImages()).step((struct, comp) -> {
                            AddImageMsg amsg = new AddImageMsg();
                            amsg.setBackupStorageUuids(CollectionDSL.list(msg.getBackupStorageUuid()));
                            amsg.setDescription(struct.getDescription());
                            amsg.setName(struct.getName());
                            amsg.setFormat(struct.getFormat());
                            amsg.setGuestOsType(struct.getGuestOSType());
                            amsg.setMediaType(struct.getMediaType());
                            amsg.setPlatform(struct.getPlatform());
                            amsg.setType(ImageConstant.ZSTACK_IMAGE_TYPE);
                            String imageUrl = String.format("%s/%s/images/%s", pathMaker.makeBuildPath(msg.getApp().getUuid()), PathUtil.fileName(msg.getDataPath()), PathUtil.fileName(struct.getUrl()));
                            amsg.setUrl(imageUrl);
                            amsg.setSystem(struct.isSystem());
                            amsg.setSession(msg.getSession());

                            bus.makeTargetServiceIdByResourceUuid(amsg, ImageConstant.SERVICE_ID, msg.getBackupStorageUuid());
                            bus.send(amsg, new CloudBusCallBack(comp) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        errList.getCauses().add(reply.getError());
                                        comp.allDone();
                                    } else {
                                        AddImageReply rly = reply.castReply();
                                        addImages.add(rly.getInventory());

                                        addImageToRef(rly.getInventory(), msg.getApp().getUuid(), msg.getBackupStorageUuid(), PathUtil.fileName(struct.getUrl()));
                                        comp.done();
                                    }
                                }
                            });

                        }, 1).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (errList.getCauses().isEmpty()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(errList.getCauses().get(0));
                                }
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        addImages.forEach(image -> {
                            ImageDeletionMsg dmsg = new ImageDeletionMsg();
                            dmsg.setImageUuid(image.getUuid());
                            dmsg.setBackupStorageUuids(CollectionDSL.list(msg.getBackupStorageUuid()));
                            dmsg.setDeletionPolicy(ImageDeletionPolicyManager.ImageDeletionPolicy.Direct.toString());
                            bus.makeTargetServiceIdByResourceUuid(dmsg, ImageConstant.SERVICE_ID, msg.getBackupStorageUuid());
                            bus.send(dmsg);
                        });
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("update-db-for-app-%s", app.getUuid());
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        app.setTemplateContent(template);
//                        String appMetaData = AppCenterUtils.getStringContentFromFile(pathMaker.makeApplicationDescPath(copyDstPath));
                        BuildAppStruct meta = JSONObjectUtil.toObject(destInfo, BuildAppStruct.class);
                        if (checkIfAppIdExisted(meta.getAppId(), meta.getVersion().getVersion())) {
                            trigger.fail(operr("create BuildApp failed, because appId[%s: %s] is duplicated by another BuildApp", meta.getAppId(), meta.getVersion().getVersion()));
                            return;
                        }
                        meta.setLogo(logo);
                        meta.setThumbs(thumbs);

                        validateBuildAppMeta(meta);
                        meta.setImageMeta(imageInfo);
                        String appMetaData = JSONObjectUtil.toJsonString(meta);
                        app.setInstallPath(copyDstPath);
                        app.setDescription(meta.getDescription());
                        app.setName(meta.getName());
                        app.setAppId(meta.getAppId());
                        app.setVersion(meta.getVersion().getVersion());

                        app.setAppMetaData(appMetaData);
                        app = dbf.updateAndRefresh(app);

                        trigger.next();
                    }
                });

                done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        sreply.setApp(BuildApplicationInventory.valueOf(app));
                        bus.reply(msg, sreply);
                    }
                });

                error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        sreply.setError(errCode);
                        bus.reply(msg, sreply);
                    }
                });
            }
        }).start();
    }

    private void handle(final BuildAppDeleteMsg msg) {
        MessageReply reply = new MessageReply();
        List<String> images = Q.New(BuildAppImageRefVO.class).select(BuildAppImageRefVO_.imageUuid).
                eq(BuildAppImageRefVO_.buildAppUuid, msg.getAppUuid()).listValues();
        if (images.isEmpty()) {
            bus.reply(msg, reply);
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("delete build app");

        chain.then(new NoRollbackFlow() {
            String __name__ = "delete images from buildapp";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                ErrorCodeList errList = new ErrorCodeList();
                new While<>(images).step((imageUuid, comp) -> {
                    ImageDeletionMsg dmsg = new ImageDeletionMsg();
                    dmsg.setImageUuid(imageUuid);
                    List<String> bsUuids = Q.New(ImageBackupStorageRefVO.class).select(ImageBackupStorageRefVO_.backupStorageUuid).eq(ImageBackupStorageRefVO_.imageUuid, imageUuid).listValues();
                    dmsg.setBackupStorageUuids(bsUuids);
                    dmsg.setDeletionPolicy(ImageDeletionPolicyManager.ImageDeletionPolicy.Direct.toString());
                    bus.makeTargetServiceIdByResourceUuid(dmsg, ImageConstant.SERVICE_ID, imageUuid);
                    bus.send(dmsg, new CloudBusCallBack(comp) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                comp.done();
                            } else {
                                errList.getCauses().add(reply.getError());
                                comp.allDone();
                            }
                        }
                    });
                }, 1).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errList.getCauses().isEmpty()) {
                            trigger.fail(errList.getCauses().get(0));
                        }
                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "delete local data from buildapp";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (self == null) {
                    trigger.next();
                    return;
                }
                LocalStorageBuildSystemCommands.DeleteAppCommand cmd = new LocalStorageBuildSystemCommands.DeleteAppCommand();
                cmd.appPath = pathMaker.makeBuildPath(msg.getAppUuid());
                cmd.url = self.getUrl();
                cmd.uuid = self.getUuid();
                httpCall(DELETE_APPLICATION_PATH, cmd,
                        LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                            @Override
                            public void success(LocalStorageBuildSystemCommands.AgentResponse rsp) {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                bus.reply(msg, reply);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(msg, reply);
            }
        }).start();


    }

    private void handle(final BuildSystemDeleteMsg msg) {
        MessageReply reply = new MessageReply();
        dbf.remove(self);
        bus.reply(msg, reply);
    }

    private void handle(final ConnectAppBuildSystemMsg msg) {
        ConnectAppBuildSystemReply reply = new ConnectAppBuildSystemReply();
        restf.echo(buildUrl(ECHO_BUILDSYSTEM_PATH), new Completion(msg) {
            @Override
            public void success() {
                LocalStorageBuildSystemCommands.ConnectCommand cmd = new LocalStorageBuildSystemCommands.ConnectCommand();
                cmd.url = self.getUrl();
                cmd.uuid = self.getUuid();
                httpCall(CONNECT_BUILDSYSTEM_PATH, cmd,
                        LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                            @Override
                            public void success(LocalStorageBuildSystemCommands.AgentResponse rsp) {
                                logger.debug(String.format("connected to build system[uuid:%s, name:%s, total capacity:%sG, available capacity: %sG",
                                        self.getUuid(), self.getName(), rsp.totalCapacity, rsp.availableCapacity));
                                tracker.track(self.getUuid());
                                bus.reply(msg, reply);
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                reply.setError(operr("unable to connect to localstorage build system[url:%s], because %s", buildUrl(CONNECT_BUILDSYSTEM_PATH), errorCode));
                                bus.reply(msg, reply);
                            }
                        });
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void buildImageStoreBaseUrl(ImageStoreBackupStorageInventory imageStore) {
        UriComponentsBuilder ub = UriComponentsBuilder.newInstance();
        ub.scheme("http");
        ub.host(imageStore.getHostname());
        ub.port(AppCenterGlobalProperty.BUILD_SYSTEM_AGENT_PORT);
        baseUrl = ub.build().toUriString();
    }

    private void handle(final AddBuildAppMsg msg) {
        MessageReply sreply = new MessageReply();
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-build-app-%s-from-build-app-path-%s", msg.getBuildAppUuid(), msg.getUrl()));

        ImageStoreBackupStorageVO bs = dbf.findByUuid(msg.getBackupStorageUuid(), ImageStoreBackupStorageVO.class);
        pathMaker = new LocalStorageBuildSystemPathMaker(bs.getUrl());
        buildImageStoreBaseUrl(ImageStoreBackupStorageInventory.valueOf(bs));

        chain.then(new ShareFlow() {
            boolean unzip = false;
            boolean download = false;
            String downloadUrl = null;
            List<CreateImageStruct> imageInfos;
            List<ImageInventory> addImages = new ArrayList<>();
            BuildApplicationVO app = dbf.findByUuid(msg.getBuildAppUuid(), BuildApplicationVO.class);
            @Override
            public void setup() {
                if (msg.getUrl().startsWith("http://") || msg.getUrl().startsWith("https://")) {
                    flow(new Flow() {
                        String __name__ = "download build-app from remote";
                        @Override
                        public void run(FlowTrigger trigger, Map data) {
                            LocalStorageBuildSystemCommands.DownloadBuildAppCommand cmd = new LocalStorageBuildSystemCommands.DownloadBuildAppCommand();
                            cmd.srcUrl = msg.getUrl();
                            downloadUrl = PathUtil.join(pathMaker.makeDownloadAppPath(msg.getBuildAppUuid()), msg.getUrl().substring(msg.getUrl().lastIndexOf("/") + 1));
                            cmd.downloadPath = downloadUrl;
                            httpCall(DOWNLOAD_BUILDAPP, cmd,
                                    LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                                        @Override
                                        public void success(LocalStorageBuildSystemCommands.AgentResponse rsp) {
                                            download = true;
                                            trigger.next();
                                        }

                                        @Override
                                        public void fail(ErrorCode errorCode) {
                                            trigger.fail(errorCode);
                                        }
                                    });
                        }

                        @Override
                        public void rollback(FlowRollback trigger, Map data) {
                            if (download) {
                                LocalStorageBuildSystemCommands.DeleteDownloadAppCommand cmd = new LocalStorageBuildSystemCommands.DeleteDownloadAppCommand();
                                cmd.downloadPath = downloadUrl;
                                httpCall(DELETE_DOWNLOAD_BUILDAPP, cmd,
                                        LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                                            @Override
                                            public void success(LocalStorageBuildSystemCommands.AgentResponse rsp) {
                                                trigger.rollback();
                                            }

                                            @Override
                                            public void fail(ErrorCode errorCode) {
                                                trigger.rollback();
                                            }
                                        });
                            } else {
                                trigger.rollback();
                            }
                        }
                    });
                }

                flow(new Flow() {
                    String __name__ = "unzip build-app to local";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        LocalStorageBuildSystemCommands.UnzipBuildAppCommand cmd = new LocalStorageBuildSystemCommands.UnzipBuildAppCommand();
                        cmd.srcUrl = downloadUrl != null ? downloadUrl : msg.getUrl();
                        cmd.rawPath = pathMaker.makeRawAppPath(msg.getBuildAppUuid());
                        httpCall(UNZIP_BUILDAPP, cmd,
                                LocalStorageBuildSystemCommands.UnzipBuildAppRsp.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.UnzipBuildAppRsp>(msg){
                                    @Override
                                    public void success(LocalStorageBuildSystemCommands.UnzipBuildAppRsp rsp) {
                                        unzip = true;
                                        BuildAppStruct struct = JSONObjectUtil.toObject(new String(Base64.decodeBase64(rsp.appCtx)), BuildAppStruct.class);

                                        if (checkIfAppIdExisted(struct.getAppId(), struct.getVersion().getVersion())) {
                                            trigger.fail(operr("add BuildApp failed, because appId[%s:%s] is duplicated by another BuildApp", struct.getAppId(), struct.getVersion().getVersion()));
                                            return;
                                        }

                                        app.setTemplateContent(struct.getTemplate());
                                        VersionMeta version = struct.getVersion();
                                        app.setVersion(version.getVersion());
                                        app.setAppId(struct.getAppId());
                                        app.setDescription(struct.getDescription());
                                        app.setName(struct.getName());

                                        imageInfos = struct.getImageMeta().getBuildImages();

                                        // delete template from appmetadata
                                        struct.setTemplate(null);
                                        app.setAppMetaData(JSONObjectUtil.toJsonString(struct));
                                        app = dbf.updateAndRefresh(app);
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        trigger.fail(errorCode);
                                    }
                                });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (unzip) {
                            LocalStorageBuildSystemCommands.CleanRawAppCommand cmd = new LocalStorageBuildSystemCommands.CleanRawAppCommand();
                            cmd.url = msg.getUrl();
                            cmd.rawPath = pathMaker.makeRawAppPath(msg.getBuildAppUuid());
                            httpCall(CLEAN_UNZIP_BUILDAPP, cmd,
                                    LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                                        @Override
                                        public void success(LocalStorageBuildSystemCommands.AgentResponse returnValue) {
                                            //TODO: should add a gc job
                                            trigger.rollback();
                                        }

                                        @Override
                                        public void fail(ErrorCode errorCode) {
                                            trigger.rollback();
                                        }
                                    });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "upload-images-to-bs";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (imageInfos.isEmpty()) {
                            trigger.next();
                            return;
                        }

                        ErrorCodeList errList = new ErrorCodeList();
                        new While<>(imageInfos).step((struct, comp) -> {
                            AddImageMsg amsg = new AddImageMsg();
                            amsg.setBackupStorageUuids(CollectionDSL.list(msg.getBackupStorageUuid()));
                            amsg.setDescription(struct.getDescription());
                            amsg.setName(struct.getName());
                            amsg.setFormat(struct.getFormat());
                            amsg.setGuestOsType(struct.getGuestOSType());
                            amsg.setMediaType(struct.getMediaType());
                            amsg.setPlatform(struct.getPlatform());
                            amsg.setType(ImageConstant.ZSTACK_IMAGE_TYPE);
                            amsg.setArchitecture(struct.getArchitecture());
                            String imageUrl = String.format("%s/images/%s", pathMaker.makeRawAppPath(msg.getBuildAppUuid()), PathUtil.fileName(struct.getUrl()));
                            amsg.setUrl(imageUrl);
                            amsg.setSystem(struct.isSystem());
                            amsg.setSession(msg.getSession());

                            bus.makeTargetServiceIdByResourceUuid(amsg, ImageConstant.SERVICE_ID, msg.getBackupStorageUuid());
                            bus.send(amsg, new CloudBusCallBack(comp) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        errList.getCauses().add(reply.getError());
                                        comp.allDone();
                                    } else {
                                        AddImageReply rly = reply.castReply();
                                        addImages.add(rly.getInventory());

                                        addImageToRef(rly.getInventory(), msg.getBuildAppUuid(), msg.getBackupStorageUuid(), PathUtil.fileName(struct.getUrl()));
                                        comp.done();
                                    }
                                }
                            });

                        }, 1).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (errList.getCauses().isEmpty()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(errList.getCauses().get(0));
                                }
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        addImages.forEach(image -> {
                            ImageDeletionMsg dmsg = new ImageDeletionMsg();
                            dmsg.setImageUuid(image.getUuid());
                            dmsg.setBackupStorageUuids(CollectionDSL.list(msg.getBackupStorageUuid()));
                            dmsg.setDeletionPolicy(ImageDeletionPolicyManager.ImageDeletionPolicy.Direct.toString());
                            bus.makeTargetServiceIdByResourceUuid(dmsg, ImageConstant.SERVICE_ID, msg.getBackupStorageUuid());
                            bus.send(dmsg);
                        });
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "clean up unzip path";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        LocalStorageBuildSystemCommands.CleanRawAppCommand cmd = new LocalStorageBuildSystemCommands.CleanRawAppCommand();
                        cmd.rawPath = pathMaker.makeRawAppPath(msg.getBuildAppUuid());
                        if (downloadUrl != null) {
                            cmd.downloadPath = pathMaker.makeDownloadAppPath(msg.getBuildAppUuid());
                        }
                        httpCall(CLEAN_UNZIP_BUILDAPP, cmd,
                                LocalStorageBuildSystemCommands.AgentResponse.class, new ReturnValueCompletion<LocalStorageBuildSystemCommands.AgentResponse>(msg){
                                    @Override
                                    public void success(LocalStorageBuildSystemCommands.AgentResponse returnValue) {
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        //TODO: should add a gc job
                                        trigger.next();
                                    }
                                });
                    }
                });

                done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        bus.reply(msg, sreply);
                    }
                });

                error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        sreply.setError(errCode);
                        bus.reply(msg, sreply);
                    }
                });
            }
        }).start();
    }

    private boolean checkIfAppIdExisted(String appId, String version) {
        return Q.New(BuildApplicationVO.class).eq(BuildApplicationVO_.appId, appId).eq(BuildApplicationVO_.version, version).isExists();
    }
}
