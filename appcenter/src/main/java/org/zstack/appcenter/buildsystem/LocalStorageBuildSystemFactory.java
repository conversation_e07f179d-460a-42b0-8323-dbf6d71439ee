package org.zstack.appcenter.buildsystem;

import org.zstack.header.buildsystem.AppBuildStorageType;
import org.zstack.header.buildsystem.AppBuildSystem;
import org.zstack.header.buildsystem.AppBuildSystemVO;
import org.zstack.header.buildsystem.BuildSystemFactory;

/**
 * Created by mingjian.deng on 2019/5/16.
 */
public class LocalStorageBuildSystemFactory implements BuildSystemFactory {
    @Override
    public AppBuildSystem getAppBuildSystem() {
        return new LocalStorageBuildSystemBase();
    }

    @Override
    public AppBuildSystem getAppBuildSystem(AppBuildSystemVO vo) {
        return new LocalStorageBuildSystemBase(vo);
    }
    @Override
    public AppBuildStorageType getBuildStorageType() {
        return AppBuildStorageType.localStorage;
    }
}
