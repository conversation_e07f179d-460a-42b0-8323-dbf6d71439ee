package org.zstack.appcenter.buildsystem;

import org.zstack.header.log.NoLogging;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
public class LocalStorageBuildSystemCommands {
    public static class AgentCommand {
        public String url;
        public String uuid;
    }

    public static class AgentResponse {
        public boolean success = true;
        public String error;
        public Long totalCapacity;
        public Long availableCapacity;
    }

    public static class PingCommand extends AgentCommand {
    }

    public static class ConnectCommand extends AgentCommand {
    }

    public static class CheckPathCommand extends AgentCommand {
        public String srcPath;
        public String dstPath;
    }

    public static class CheckPathRsp extends AgentResponse {
        public Long srcSize;
        public Map<String, String> imageInfos = new HashMap<>();
    }

    public static class CreateAppCommand extends AgentCommand {
        public String srcPath;
        public String dstPath;
    }

    public static class CreateAppRsp extends AgentResponse implements Serializable {
        public Long dstSize;
        public String dstPath;
        public String imageInfos;
        public String dstInfo;
        public String template;
        @NoLogging
        public String logo;
        @NoLogging
        public List<String> thumbs = new ArrayList<>();
    }

    public static class DeleteAppCommand extends AgentCommand {
        public String appPath;
    }

    public static class DeleteAppExportCommand extends AgentCommand {
        public String exportPath;
    }

    public static class AppExportCommand extends AgentCommand {
        public String exportDir;
        public String exportCtx;
        public String appDir;
    }

    public static class AppExportRsp extends AgentResponse {
        public String exportPath;
        public String md5Sum;
        public Long size;
    }

    public static class UnzipBuildAppCommand extends AgentCommand {
        public String srcUrl;
        public String rawPath;
    }

    public static class UnzipBuildAppRsp extends AgentResponse {
        public Long totalSize;
        public String appCtx;
    }

    public static class CleanRawAppCommand extends AgentCommand {
        public String rawPath;
        public String downloadPath;
    }

    public static class DownloadBuildAppCommand extends AgentCommand {
        public String srcUrl;
        public String downloadPath;
    }

    public static class DeleteDownloadAppCommand extends AgentCommand {
        public String downloadPath;
    }
}
