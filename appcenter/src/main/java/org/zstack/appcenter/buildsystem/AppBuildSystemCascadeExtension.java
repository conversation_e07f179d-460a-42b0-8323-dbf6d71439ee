package org.zstack.appcenter.buildsystem;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.buildsystem.AppBuildSystemInventory;
import org.zstack.header.buildsystem.AppBuildSystemVO;
import org.zstack.header.buildsystem.BuildSystemDeleteMsg;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.List;

/**
 * Created by mingjian.deng on 2019/5/17.
 */
public class AppBuildSystemCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(AppBuildSystemCascadeExtension.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    private static final String NAME = AppBuildSystemVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            completion.success();
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            completion.success();
        } else {
            completion.success();
        }
    }

    private void handleDeletion(final CascadeAction action, final Completion completion) {
        final List<AppBuildSystemInventory> bsinvs = buildSystemFromAction(action);
        if (bsinvs == null) {
            completion.success();
            return;
        }

        ErrorCodeList errs = new ErrorCodeList();
        new While<>(bsinvs).all((bsinv, com) -> {
            BuildSystemDeleteMsg dmsg = new BuildSystemDeleteMsg();
            dmsg.setUuid(bsinv.getUuid());
            bus.makeTargetServiceIdByResourceUuid(dmsg, AppBuildSystemConstant.SERVICE_ID, bsinv.getUuid());
            bus.send(dmsg, new CloudBusCallBack(com) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("delete build system failed, because: %s", reply.getError().getDetails()));
                        errs.getCauses().add(reply.getError());
                    } else {
                        logger.debug(String.format("deleted build system[uuid:%s, name:%s]", bsinv.getUuid(), bsinv.getName()));
                    }
                    com.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errs.getCauses().isEmpty()) {
                    completion.fail(errs.getCauses().get(0));
                } else {
                    completion.success();
                }
            }
        });
    }

    @Override
    public List<String> getEdgeNames() {
        return CollectionDSL.list();
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    private List<AppBuildSystemInventory> buildSystemFromAction(CascadeAction action) {
        if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        } else {
            return null;
        }
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<AppBuildSystemInventory> ctx = buildSystemFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}
