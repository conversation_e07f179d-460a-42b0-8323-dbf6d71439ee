package org.zstack.appcenter.buildsystem;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.appcenter.AppCenterGlobalConfig;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.config.GlobalConfig;
import org.zstack.core.config.GlobalConfigUpdateExtensionPoint;
import org.zstack.core.db.Q;
import org.zstack.core.tracker.PingTracker;
import org.zstack.header.buildsystem.AppBuildSystemVO;
import org.zstack.header.buildsystem.AppBuildSystemVO_;
import org.zstack.header.buildsystem.PingAppBuildSystemMsg;
import org.zstack.header.managementnode.ManagementNodeChangeListener;
import org.zstack.header.managementnode.ManagementNodeInventory;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.message.MessageReply;
import org.zstack.header.message.NeedReplyMessage;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by mingjian.deng on 2019/5/27.
 */
public class AppBuildSystemPingTracker extends PingTracker implements ManagementNodeReadyExtensionPoint, ManagementNodeChangeListener {
    private final static CLogger logger = Utils.getLogger(AppBuildSystemPingTracker.class);

    @Autowired
    private ResourceDestinationMaker destMaker;

    @Override
    public String getResourceName() {
        return "app center build system";
    }

    @Override
    public NeedReplyMessage getPingMessage(String resUuid) {
        PingAppBuildSystemMsg msg = new PingAppBuildSystemMsg();
        msg.setBuildSystemUuid(resUuid);
        bus.makeTargetServiceIdByResourceUuid(msg, AppBuildSystemConstant.SERVICE_ID, resUuid);
        return msg;
    }

    @Override
    protected void startHook() {
        AppCenterGlobalConfig.PING_INTERVAL.installUpdateExtension(new GlobalConfigUpdateExtensionPoint() {
            @Override
            public void updateGlobalConfig(GlobalConfig oldConfig, GlobalConfig newConfig) {
                pingIntervalChanged();
            }
        });
    }

    @Override
    public int getPingInterval() {
        return AppCenterGlobalConfig.PING_INTERVAL.value(Integer.class);
    }

    @Override
    public int getParallelismDegree() {
        return AppCenterGlobalConfig.PING_PARALLELISM_DEGREE.value(Integer.class);
    }

    @Override
    public void handleReply(String resourceUuid, MessageReply reply) {

    }

    @Override
    public void nodeJoin(ManagementNodeInventory inv) {
        reScanBuidSystem();
    }

    @Override
    public void nodeLeft(ManagementNodeInventory inv) {
        reScanBuidSystem();
    }

    @Override
    public void iAmDead(ManagementNodeInventory inv) {

    }

    @Override
    public void iJoin(ManagementNodeInventory inv) {
    }

    @Override
    public void managementNodeReady() {
        reScanBuidSystem();
    }

    private void reScanBuidSystem() {
        untrackAll();

        List<String> bsUuids = Q.New(AppBuildSystemVO.class).select(AppBuildSystemVO_.uuid).listValues();
        List<String> byUs = bsUuids.stream().filter(bsUuid -> destMaker.isManagedByUs(bsUuid)).collect(Collectors.toList());
        track(byUs);
    }
}
