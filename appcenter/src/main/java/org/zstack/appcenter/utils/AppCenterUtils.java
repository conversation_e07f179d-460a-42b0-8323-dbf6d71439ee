package org.zstack.appcenter.utils;

import org.apache.commons.lang.math.NumberUtils;
import org.zstack.appcenter.AppCenterSystemTags;
import org.zstack.core.Platform;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;

import static org.zstack.core.Platform.operr;

/**
 * Created by mingjian.deng on 2019/5/22.
 */
public class AppCenterUtils {
    private static final CLogger logger = Utils.getLogger(AppCenterUtils.class);

    public static boolean versionFormatMatched(String versionFormat, String version) {
        String[] f = versionFormat.split("\\.");
        String[] v = version.split("\\.");

        if (f.length != v.length) {
            return false;
        }

        for (String s: v) {
            if (!NumberUtils.isNumber(s)) {
                return false;
            }
        }

        return true;
    }

    public static String getStringContentFromFile(String file) {
        return getStringContentFromFile(file, true);
    }

    public static String getStringContentFromFile(String file, boolean exceptionOnNone) {
        File template = new File(file);
        if (!template.exists()) {
            if (exceptionOnNone) {
                throw new OperationFailureException(Platform.operr("cannot find raw-template json file at: %s", file));
            } else {
                return null;
            }
        }
        try (BufferedReader input = new BufferedReader(new FileReader(template))) {
            StringBuilder contents = new StringBuilder();
            String line;
            while ((line = input.readLine()) != null) {
                contents.append(line);
            }
            return contents.toString();
        } catch (Exception e) {
            throw new OperationFailureException(operr("Unable to create json template", e));
        }
    }

    public static void attachSystemTag(String resourceUuid) {
        if (!AppCenterSystemTags.CRAETE_BY_APPCENTER.hasTag(resourceUuid)) {
            SystemTagCreator creator = AppCenterSystemTags.CRAETE_BY_APPCENTER.newSystemTagCreator(resourceUuid);
            creator.inherent = true;
            creator.recreate = false;
            creator.create();
        }
    }
}
