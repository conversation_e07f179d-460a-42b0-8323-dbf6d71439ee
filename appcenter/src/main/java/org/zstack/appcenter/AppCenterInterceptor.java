package org.zstack.appcenter;

import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.appcenter.APIPublishAppMsg;
import org.zstack.header.message.APIMessage;

/**
 * Created by mingjian.deng on 2019/6/3.
 */
public class AppCenterInterceptor implements ApiMessageInterceptor {
    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIPublishAppMsg) {
            validate((APIPublishAppMsg) msg);
        }
        return msg;
    }

    private void validate(final APIPublishAppMsg msg) {

    }
}
