package org.zstack.appcenter;

import org.zstack.header.appcenter.PublishAppVO;
import org.zstack.header.cloudformation.ResourceStackVO;
import org.zstack.header.cloudformation.StackTemplateVO;
import org.zstack.header.tag.TagDefinition;
import org.zstack.tag.PatternedSystemTag;

/**
 * Created by ming<PERSON><PERSON>.deng on 2019/11/14.
 */
@TagDefinition
public class AppCenterSystemTags {
    public static final String BUILD_APP_UUID_TOKEN = "buildappUuid";
    public static PatternedSystemTag BUILD_APP_UUID = new PatternedSystemTag(String.format("buildapp::{%s}",
            BUILD_APP_UUID_TOKEN), PublishAppVO.class);

    public static final String APPCENTER_TEMPLATE_UUID_TOKEN = "publishAppUuid";
    public static final String APPCENTER_TEMPLATE_NAME_TOKEN = "publishAppName";
    public static PatternedSystemTag APPCENTER_TEMPLATE = new PatternedSystemTag(String.format("appUuid::{%s}::appName::{%s}",
            APPCENTER_TEMPLATE_UUID_TOKEN, APPCENTER_TEMPLATE_NAME_TOKEN), ResourceStackVO.class);

    public static PatternedSystemTag APPCENTER_SOURCE = new PatternedSystemTag("source::appcenter", ResourceStackVO.class);

    public static final String CRAETE_BY_APPCENTER_TOKEN = "appcenter::autotag";
    public static PatternedSystemTag CRAETE_BY_APPCENTER = new PatternedSystemTag(CRAETE_BY_APPCENTER_TOKEN, StackTemplateVO.class);
}
