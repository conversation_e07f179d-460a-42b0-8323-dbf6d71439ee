package org.zstack.appcenter;

import com.google.gson.JsonParser;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.cloudformation.template.CloudFormationDecoder;
import org.zstack.cloudformation.template.decoder.ResourceDecoder;
import org.zstack.cloudformation.template.struct.CfnResults;
import org.zstack.cloudformation.template.struct.PreParameterStruct;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.appcenter.PublishAppDeleteMessage;
import org.zstack.header.appcenter.PublishAppMsg;
import org.zstack.header.appcenter.PublishAppResourceRefVO;
import org.zstack.header.appcenter.PublishAppVO;
import org.zstack.header.buildapp.BuildAppImageRefVO;
import org.zstack.header.buildapp.BuildAppImageRefVO_;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.vo.ResourceVO_;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by mingjian.deng on 2019/6/3.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class ZStackAppCenterBase implements AppCenter {
    private static final CLogger logger = Utils.getLogger(ZStackAppCenterBase.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;

    private PublishAppVO self;

    public ZStackAppCenterBase(PublishAppVO self) {
        this.self = self;
    }

    private void updateSelf() {
        self = dbf.updateAndRefresh(self);
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof PublishAppMsg) {
            handle((PublishAppMsg) msg);
        } else if (msg instanceof PublishAppDeleteMessage) {
            handle((PublishAppDeleteMessage) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private String getResourceType(String resourceType) {
        if (resourceType == null) {
            return null;
        }
        if (resourceType.endsWith("VO") || resourceType.endsWith("EO")) {
            return resourceType;
        } else {
            return resourceType + "VO";
        }
    }

    private void handle(final PublishAppMsg msg) {
        MessageReply reply = new MessageReply();
        CloudFormationDecoder decoder = new CloudFormationDecoder();
        CfnResults results = decoder.decodeFromContent(self.getTemplateContent(), msg.getParams(), false);
        ResourceDecoder rDecoder = new ResourceDecoder();
        Map<String, String> resourceTypes = rDecoder.getResourceParametersType(new JsonParser().parse(self.getTemplateContent()));

        results.getPreparams().forEach(p -> {
            String resourceType = getResourceType(resourceTypes.get(p.getParamName()));
            if ("ImageVO".equals(resourceType)) {
                if (p.getDefaultValue() != null) {
                    String imageUuid = Q.New(BuildAppImageRefVO.class).eq(BuildAppImageRefVO_.buildAppUuid, msg.getBuildAppUuid()).eq(BuildAppImageRefVO_.imageName, p.getDefaultValue())
                            .select(BuildAppImageRefVO_.imageUuid).findValue();
                    if (imageUuid != null) {
                        p.setDefaultValue(imageUuid);
                        p.setValue(imageUuid);
                    }
                }
            }
            if (resourceType != null) {
                boolean found = Q.New(ResourceVO.class).eq(ResourceVO_.uuid, p.getValue()).isExists();
                if (found) {
                    PublishAppResourceRefVO ref = new PublishAppResourceRefVO();
                    ref.setAppUuid(self.getUuid());
                    ref.setResourceType(resourceType);
                    ref.setResourceUuid((String)p.getValue());
                    dbf.persistAndRefresh(ref);
                }
            }
        });

        ErrorCode err = checkPreParams(results.getPreparams());
        if (err != null) {
            reply.setError(err);
            bus.reply(msg, reply);
            return;
        }

        if (!results.getPreparams().isEmpty()) {
            self.setPreParams(forPreParams(results.getPreparams()));
            dbf.updateAndRefresh(self);
        }

        bus.reply(msg, reply);
    }

    private ErrorCode checkPreParams(List<PreParameterStruct> params) {
        for (PreParameterStruct struct: params) {
            if (struct.getValue() == null) {
                return Platform.operr("%s is in preParameters, but not be set", struct.getParamName());
            }
            if (struct.getType().equalsIgnoreCase("Number")) {
                if (!(struct.getValue() instanceof Number)) {
                    return Platform.operr("%s need Number value, but got wrong type");
                }
            }
            if (struct.getType().equalsIgnoreCase("Boolean")) {
                if (!(struct.getValue() instanceof Boolean)) {
                    return Platform.operr("%s need Boolean value, but got wrong type");
                }
            }
            if (struct.getType().equalsIgnoreCase("String")) {
                if (struct.getValue() instanceof Number || struct.getValue() instanceof Boolean) {
                    return Platform.operr("%s need String value, but got wrong type");
                }
            }
        }
        return null;
    }

    private String forPreParams(List<PreParameterStruct> original) {
        Map<String, Object> result = new HashMap<>();
        for (PreParameterStruct struct: original) {
            if (struct.getValue() instanceof Number) {
                Number n = (Number) struct.getValue();
                result.put(struct.getParamName(), n.longValue());
            } else {
                result.put(struct.getParamName(), struct.getValue());
            }
        }
        return JSONObjectUtil.toJsonString(result);
    }

    private void handle(final PublishAppDeleteMessage msg) {
        MessageReply reply = new MessageReply();
        bus.reply(msg, reply);
    }

}
