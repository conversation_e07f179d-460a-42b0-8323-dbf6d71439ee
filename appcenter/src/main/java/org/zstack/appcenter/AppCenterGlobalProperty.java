package org.zstack.appcenter;

import org.zstack.core.GlobalProperty;
import org.zstack.core.GlobalPropertyDefinition;

/**
 * Created by mingjian.deng on 2019/5/16.
 */
@GlobalPropertyDefinition
public class AppCenterGlobalProperty {
    @GlobalProperty(name="AppBuildSystem.serverPort", defaultValue = "8009")
    public static int BUILD_SYSTEM_SERVER_PORT;
    @GlobalProperty(name="AppCenter.BuildSystem.agentPackageName", defaultValue = "appbuildsystem-5.4.0.tar.gz")
    public static String BUILD_SYSTEM_PACKAGE_NAME;
    @GlobalProperty(name="AppCenter.BuildSystem.ansiblePlaybook", defaultValue = "appbuild.py")
    public static String BUILD_SYSTEM_PLAYBOOK_NAME;
    @GlobalProperty(name="AppCenter.BuildSystem.ansibleModulePath", defaultValue = "ansible/appbuild")
    public static String BUILD_SYSTEM_MODULE_PATH;
    @GlobalProperty(name="AppBuildSystem.agentPort", defaultValue = "7079")
    public static int BUILD_SYSTEM_AGENT_PORT;
}
