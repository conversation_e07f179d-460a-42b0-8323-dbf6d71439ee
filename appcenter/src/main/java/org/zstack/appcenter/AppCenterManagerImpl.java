package org.zstack.appcenter;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.cloudformation.*;
import org.zstack.cloudformation.template.CloudFormationCreator;
import org.zstack.cloudformation.template.CloudFormationDecoder;
import org.zstack.cloudformation.template.struct.CfnActions;
import org.zstack.cloudformation.template.struct.CfnResults;
import org.zstack.cloudformation.template.struct.ResultStruct;
import org.zstack.cloudformation.template.struct.StackData;
import org.zstack.core.Platform;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.AbstractService;
import org.zstack.header.appcenter.*;
import org.zstack.header.buildapp.BuildAppStruct;
import org.zstack.header.buildapp.BuildAppType;
import org.zstack.header.buildapp.BuildApplicationVO;
import org.zstack.header.cloudformation.*;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.query.QueryBelongFilter;
import org.zstack.identity.AccountManager;
import org.zstack.image.ImageSystemTags;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.TagManager;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.*;

import static org.zstack.core.Platform.*;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * Created by mingjian.deng on 2019/6/3.
 */
public class AppCenterManagerImpl extends AbstractService implements AppCenterManager, ClousFormationTemplateExtensionPoint, QueryBelongFilter, CloudFormationExtensionPoint {
    private static final CLogger logger = Utils.getLogger(AppCenterManagerImpl.class);
    private Map<String, AppCenterFactory> appcenterFactories = Collections.synchronizedMap(new HashMap<>());

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private ThreadFacade thdf;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    private AccountManager acmgr;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private TagManager tagMgr;


    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIPublishAppMsg) {
            handle((APIPublishAppMsg) msg);
        } else if (msg instanceof APIUpdatePublishAppMsg) {
            handle((APIUpdatePublishAppMsg) msg);
        } else if (msg instanceof APIDeletePublishAppMsg) {
            handle((APIDeletePublishAppMsg) msg);
        } else if (msg instanceof APIGetResourceFromPublishAppMsg) {
            handle((APIGetResourceFromPublishAppMsg) msg);
        } else if (msg instanceof APICreateResourceStackFromAppMsg) {
            handle((APICreateResourceStackFromAppMsg) msg);
        } else if (msg instanceof APIPreviewResourceFromAppMsg) {
            handle((APIPreviewResourceFromAppMsg) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof AppCenterAppMessage) {
            passThrough((AppCenterAppMessage) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void passThrough(final AppCenterAppMessage msg) {
        PublishAppVO vo = dbf.findByUuid(msg.getAppUuid(), PublishAppVO.class);

        if (vo == null) {
            String err = String.format("Cannot find app[uuid:%s], it may have been deleted", msg.getAppUuid());
            bus.replyErrorByMessageType((Message)msg, err);
            return;
        }

        AppCenterFactory factory = getBuildSystemFactory(vo.getType());
        AppCenter ss = New(()-> factory.getAppCenter(vo));
        ss.handleMessage((Message) msg);
    }

    private void publishApp(final APIPublishAppMsg msg, final ReturnValueCompletion<PublishAppInventory> completion) {
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("publish-build-app-%s", msg.getBuildAppUuid()));

        chain.then(new ShareFlow() {
            PublishAppVO app;
            BuildApplicationVO build = dbf.findByUuid(msg.getBuildAppUuid(), BuildApplicationVO.class);
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "create app in db";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        app = new PublishAppVO();
                        if (msg.getResourceUuid() != null) {
                            app.setUuid(msg.getResourceUuid());
                        } else {
                            app.setUuid(Platform.getUuid());
                        }
                        app.setAccountUuid(msg.getSession().getAccountUuid());
                        app.setName(msg.getName());
                        app.setDescription(msg.getDescription());
                        app.setStatus(PublishAppStatus.Creating);
                        app.setAppId(build.getAppId());
                        app.setVersion(build.getVersion());
                        app.setAppMetaData(build.getAppMetaData());
                        app.setType(BuildAppType.zstack);
                        app.setBuildAppUuid(build.getUuid());
                        app.setTemplateContent(build.getTemplateContent());

                        BuildAppStruct meta = JSONObjectUtil.toObject(build.getAppMetaData(), BuildAppStruct.class);
                        app.setVmRelationShip(meta.getVmRelationShip());
                        dbf.persistAndRefresh(app);

                        tagMgr.createTagsFromAPICreateMessage(msg, app.getUuid(), PublishAppVO.class.getSimpleName());

                        SystemTagCreator creator = AppCenterSystemTags.BUILD_APP_UUID.newSystemTagCreator(app.getUuid());
                        creator.setTagByTokens(map(e(AppCenterSystemTags.BUILD_APP_UUID_TOKEN, build.getUuid())));
                        creator.inherent = true;
                        creator.create();
                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (app != null && app.getUuid() != null) {
                            dbf.removeByPrimaryKey(app.getUuid(), PublishAppVO.class);
                        }
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "create app's template";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        PublishAppMsg pmsg = new PublishAppMsg();
                        pmsg.setAppUuid(app.getUuid());
                        pmsg.setBuildAppUuid(build.getUuid());
                        pmsg.setParams(msg.getParameters());
                        bus.makeTargetServiceIdByResourceUuid(pmsg, AppCenterConstant.SERVICE_ID, app.getUuid());
                        bus.send(pmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (reply.isSuccess()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(reply.getError());
                                }
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        app = dbf.reload(app);
                        app.setStatus(PublishAppStatus.Ready);
                        app = dbf.updateAndRefresh(app);

                        completion.success(PublishAppInventory.valueOf(app));
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private void handle(final APIPublishAppMsg msg) {
        APIPublishAppEvent evt = new APIPublishAppEvent(msg.getId());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                publishApp(msg, new ReturnValueCompletion<PublishAppInventory>(taskChain) {
                    @Override
                    public void success(PublishAppInventory inventory) {
                        // appMetaData is too long, we try to ignore it in return value
                        inventory.setAppMetaData(null);
                        evt.setInventory(inventory);
                        bus.publish(evt);
                        taskChain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        taskChain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("publish-app-from-buildapp-%s", msg.getBuildAppUuid());
            }
        });
    }

    private void handle(final APIUpdatePublishAppMsg msg) {
        APIUpdatePublishAppEvent evt = new APIUpdatePublishAppEvent(msg.getId());
        PublishAppVO vo = dbf.findByUuid(msg.getUuid(), PublishAppVO.class);
        if (msg.getDescription() != null) {
            vo.setDescription(msg.getDescription());
        }

        if (msg.getName() != null) {
            vo.setName(msg.getName());
        }

        vo = dbf.updateAndRefresh(vo);
        evt.setInventory(PublishAppInventory.valueOf(vo));

        bus.publish(evt);
    }

    private void deletePublishApp(String appUuid, final Completion completion) {
        final String issuer = PublishAppVO.class.getSimpleName();
        PublishAppVO vo = dbf.findByUuid(appUuid, PublishAppVO.class);
        if (vo == null) {
            logger.debug(String.format("publish app: [uuid: %s] has been deleted.", appUuid));
            completion.success();
            return;
        }
        vo.setStatus(PublishAppStatus.Deleting);
        vo = dbf.updateAndRefresh(vo);

        final List<PublishAppInventory> ctx = PublishAppInventory.valueOf(CollectionDSL.list(vo));

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-app-%s", appUuid));
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        chain.then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void handle(final APIDeletePublishAppMsg msg) {
        APIDeletePublishAppEvent evt = new APIDeletePublishAppEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getUuid();
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                deletePublishApp(msg.getUuid(), new Completion(taskChain){
                    @Override
                    public void success() {
                        bus.publish(evt);
                        taskChain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(err(SysErrors.DELETE_RESOURCE_ERROR, errorCode, errorCode.getDetails()));
                        bus.publish(evt);
                        taskChain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("delete-app-%s", msg.getUuid());
            }
        });
    }

    private void handle(final APIGetResourceFromPublishAppMsg msg) {
        APIGetResourceFromPublishAppReply reply = new APIGetResourceFromPublishAppReply();
        List<PublishAppResourceRefVO> refs;
        if (msg.getUuid() != null) {
            refs = Q.New(PublishAppResourceRefVO.class).eq(PublishAppResourceRefVO_.appUuid, msg.getUuid()).list();
        } else {
            refs = Q.New(PublishAppResourceRefVO.class).list();
        }

        for (PublishAppResourceRefVO ref: refs) {
            PublishAppResourceStruct r = new PublishAppResourceStruct();
            r.setAppUuid(msg.getUuid());
            r.setResourceUuid(ref.getResourceUuid());
            r.setResourceType(ref.getResourceType());
            reply.getResources().add(r);
        }
        bus.reply(msg, reply);
    }

    private void createApp(final APICreateResourceStackFromAppMsg msg, ReturnValueCompletion<ResourceStackInventory> completion) {
        PublishAppVO vo = dbf.findByUuid(msg.getAppUuid(), PublishAppVO.class);
        CreateStackResourceMsg cmsg = new CreateStackResourceMsg();
        cmsg.setUuid(msg.getResourceUuid() != null ? msg.getResourceUuid() : getUuid());
        cmsg.setType(vo.getType().toString());
        cmsg.setTimeout(msg.getTimeout());
        cmsg.setSession(msg.getSession());
        cmsg.setTemplateContent(vo.getTemplateContent());
        cmsg.setParameters(msg.getParameters());
        cmsg.setName(msg.getName());
        cmsg.setDescription(msg.getDescription());
        cmsg.setRollback(msg.getRollback());
        cmsg.setPreParameters(vo.getPreParams());
        cmsg.setSource(AppCenterConstant.SOURCE_TYPE);

        bus.makeLocalServiceId(cmsg, CloudFormationConstant.SERVICE_ID);
        bus.send(cmsg, new CloudBusCallBack(msg, completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    CreateStackResourceReply r = reply.castReply();

                    if (!msg.isWithoutAppInfo()) {
                        SystemTagCreator creator = AppCenterSystemTags.APPCENTER_TEMPLATE.newSystemTagCreator(cmsg.getUuid());
                        creator.setTagByTokens(map(e(AppCenterSystemTags.APPCENTER_TEMPLATE_UUID_TOKEN, vo.getUuid()),
                                e(AppCenterSystemTags.APPCENTER_TEMPLATE_NAME_TOKEN, vo.getName())));
                        creator.inherent = true;
                        creator.recreate = true;
                        creator.create();
                    }
                    completion.success(r.getInventory());
                } else {
                    if (!msg.isWithoutAppInfo()) {
                        SystemTagCreator creator = AppCenterSystemTags.APPCENTER_TEMPLATE.newSystemTagCreator(cmsg.getUuid());
                        creator.setTagByTokens(map(e(AppCenterSystemTags.APPCENTER_TEMPLATE_UUID_TOKEN, vo.getUuid()),
                                e(AppCenterSystemTags.APPCENTER_TEMPLATE_NAME_TOKEN, vo.getName())));
                        creator.inherent = true;
                        creator.recreate = true;
                        creator.create();
                    }
                    completion.fail(reply.getError());
                }
            }
        });
    }

    private void handle(final APICreateResourceStackFromAppMsg msg) {
        APICreateResourceStackEvent evt = new APICreateResourceStackEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                createApp(msg, new ReturnValueCompletion<ResourceStackInventory>(taskChain) {
                    @Override
                    public void success(ResourceStackInventory inventory) {
                        evt.setInventory(inventory);
                        bus.publish(evt);
                        taskChain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        taskChain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("create-app-from-publishapp-%s", msg.getAppUuid());
            }
        });
    }

    private void handle(final APIPreviewResourceFromAppMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                APIPreviewResourceStackReply reply = new APIPreviewResourceStackReply();
                PublishAppVO vo = dbf.findByUuid(msg.getAppUuid(), PublishAppVO.class);
                if (vo == null) {
                    reply.setError(operr("PublishAppVO[uuid: %s] is not existed", msg.getAppUuid()));
                    bus.reply(msg, reply);
                    taskChain.next();
                    return;
                }
                CloudFormationDecoder decoder = new CloudFormationDecoder();

                CfnResults result = decoder.decodeFromContent(vo.getTemplateContent(), msg.getParameters(), vo.getPreParams(), true);

                CloudFormationCreator creator = new CloudFormationCreator();
                PreviewResourceStruct restruct = new PreviewResourceStruct();
                CfnActions actions = creator.dryRun(result);

                actions.getActions().forEach(action -> {
                    restruct.getActions().add(action);
                });
                restruct.setConditions(result.getConditionParams());


                reply.setPreview(restruct);
                bus.reply(msg, reply);
                taskChain.next();
            }

            @Override
            public String getName() {
                return String.format("create-app-from-publishapp-%s", msg.getAppUuid());
            }
        });
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(AppCenterConstant.SERVICE_ID);
    }

    @Override
    public boolean start() {
        populate();
        return true;
    }



    private AppCenterFactory getBuildSystemFactory(BuildAppType type) {
        AppCenterFactory factory = appcenterFactories.get(type.toString());
        if (factory == null) {
            throw new CloudRuntimeException(String.format("No AppCenterFactory for type: %s found", type));
        }
        return factory;
    }

    private void populate() {
        for (AppCenterFactory factory : pluginRgty.getExtensionList(AppCenterFactory.class)) {
            AppCenterFactory old = appcenterFactories.get(factory.getBuildAppType().toString());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate AppCenterFactory[%s, %s] for type[%s]",
                        factory.getClass().getName(), old.getClass().getName(), old.getBuildAppType().toString()));
            }
            appcenterFactories.put(factory.getBuildAppType().toString(), factory);
        }
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public String getPreParameters(ResourceStackInventory stack) {
        String appUuid = AppCenterSystemTags.APPCENTER_TEMPLATE.getTokenByResourceUuid(stack.getUuid(), ResourceStackVO.class, AppCenterSystemTags.APPCENTER_TEMPLATE_UUID_TOKEN);
        if (appUuid != null) {
            PublishAppVO vo = dbf.findByUuid(appUuid, PublishAppVO.class);
            return vo.getPreParams();
        } else {
            return null;
        }
    }

    @Override
    public void afterCreateResourceStack(ResourceStackInventory stack, String source) {
        if (AppCenterConstant.SOURCE_TYPE.equals(source)) {
            SystemTagCreator creator = AppCenterSystemTags.APPCENTER_SOURCE.newSystemTagCreator(stack.getUuid());
            creator.inherent = true;
            creator.recreate = false;
            creator.create();
        }
    }

    @Override
    public String filterName() {
        return "appcenter";
    }

    @Override
    public String convertFilterNameToZQL(String filterName) {
        String[] ss = filterName.split(":");
        if ("true".equals(ss[1])) {
            return "has ('appcenter::autotag')";
        } else if ("false".equals(ss[1])) {
            return "not has ('appcenter::autotag')";
        } else {
            throw new OperationFailureException(argerr("[appcenter] filterName must be appcenter:true or appcenter:false"));
        }
    }

    @Override
    public void afterGetResourceStackFromResource(Map<String, String> stack) {
        if (stack.get("stackUuid") == null) {
            return;
        }
        String publishAppUuid = AppCenterSystemTags.APPCENTER_TEMPLATE.getTokenByResourceUuid(stack.get("stackUuid"), AppCenterSystemTags.APPCENTER_TEMPLATE_UUID_TOKEN);
        if (publishAppUuid != null) {
            stack.put("publishAppUuid", publishAppUuid);
            String buildAppUuid = AppCenterSystemTags.BUILD_APP_UUID.getTokenByResourceUuid(publishAppUuid, AppCenterSystemTags.BUILD_APP_UUID_TOKEN);
            if (buildAppUuid != null) {
                stack.put("buildAppUuid", buildAppUuid);
            }
        }
    }

    @Override
    public void afterDeleteResourceStack(ResourceStackInventory stack) {
        if (AppCenterSystemTags.APPCENTER_TEMPLATE.hasTag(stack.getUuid())) {
            String publishAppUuid = AppCenterSystemTags.APPCENTER_TEMPLATE.getTokenByResourceUuid(stack.getUuid(), AppCenterSystemTags.APPCENTER_TEMPLATE_UUID_TOKEN);
            deletePublishApp(publishAppUuid, new Completion(null) {
                @Override
                public void success() {
                    logger.debug(String.format("delete publish app: %s success", publishAppUuid));
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    logger.debug(String.format("delete publish app: %s failed, cause: %s", publishAppUuid, errorCode.getDetails()));
                }
            });
        }
    }

    @Override
    public void beforeCloudFormationAction(Object action) {

    }

    @Override
    public void afterCloudFormationAction(Object action, Object result, StackData stackData) {
        if (AppCenterSystemTags.APPCENTER_SOURCE.hasTag(stackData.getStackUuid())) {
            CloudFormationUtils.attachSystemTagToResources(action, result, AppCenterSystemTags.CRAETE_BY_APPCENTER);
        }
    }
}
