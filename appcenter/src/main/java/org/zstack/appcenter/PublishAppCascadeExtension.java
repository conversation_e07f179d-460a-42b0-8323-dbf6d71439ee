package org.zstack.appcenter;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.appcenter.PublishAppDeleteMessage;
import org.zstack.header.appcenter.PublishAppInventory;
import org.zstack.header.appcenter.PublishAppVO;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.List;

/**
 * Created by mingjian.deng on 2019/6/4.
 */
public class PublishAppCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(PublishAppCascadeExtension.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    private static final String NAME = PublishAppVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            completion.success();
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            completion.success();
        } else {
            completion.success();
        }
    }

    private void handleDeletion(final CascadeAction action, final Completion completion) {
        final List<PublishAppInventory> appinvs = appFromAction(action);
        if (appinvs == null) {
            completion.success();
            return;
        }

        ErrorCodeList errs = new ErrorCodeList();
        new While<>(appinvs).all((appinv, com) -> {
            PublishAppDeleteMessage dmsg = new PublishAppDeleteMessage();
            dmsg.setAppUuid(appinv.getUuid());
            bus.makeTargetServiceIdByResourceUuid(dmsg, AppCenterConstant.SERVICE_ID, appinv.getUuid());
            bus.send(dmsg, new CloudBusCallBack(com) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("delete publish application failed, because: %s", reply.getError().getDetails()));
                        errs.getCauses().add(reply.getError());
                    } else {
                        dbf.removeByPrimaryKey(appinv.getUuid(), PublishAppVO.class);
                        logger.debug(String.format("deleted publish application[uuid:%s, name:%s]", appinv.getUuid(), appinv.getName()));
                    }
                    com.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errs.getCauses().isEmpty()) {
                    completion.fail(errs.getCauses().get(0));
                } else {
                    completion.success();
                }
            }
        });
    }

    @Override
    public List<String> getEdgeNames() {
        return CollectionDSL.list();
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    private List<PublishAppInventory> appFromAction(CascadeAction action) {
        if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        } else {
            return null;
        }
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<PublishAppInventory> ctx = appFromAction(action);
            if (ctx != null && !ctx.isEmpty()) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}
