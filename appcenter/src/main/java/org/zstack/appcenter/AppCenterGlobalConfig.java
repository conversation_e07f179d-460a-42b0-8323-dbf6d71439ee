package org.zstack.appcenter;

import org.zstack.core.config.GlobalConfig;
import org.zstack.core.config.GlobalConfigDefinition;
import org.zstack.core.config.GlobalConfigValidation;

/**
 * Created by mingjian.deng on 2019/5/27.
 */
@GlobalConfigDefinition
public class AppCenterGlobalConfig {
    public static final String CATEGORY = "appcenter";

    @GlobalConfigValidation(numberGreaterThan = 1)
    public static GlobalConfig PING_INTERVAL = new GlobalConfig(CATEGORY, "buildsystem.ping.interval");

    @GlobalConfigValidation(numberGreaterThan = 0)
    public static GlobalConfig PING_PARALLELISM_DEGREE = new GlobalConfig(CATEGORY, "buildsystem.ping.parallelismDegree");
}
