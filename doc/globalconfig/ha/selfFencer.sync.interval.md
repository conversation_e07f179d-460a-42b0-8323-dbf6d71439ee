
## Name

```
selfFencer.sync.interval(##中文名-必填##)
```

### Description

```
Interval time to sync fencer state, also an interval time to setup/cancel self fencer on failed host
```

### 含义

```
同步 fencer 状态的间隔时间, 也是重试配置/退出 fencer 的间隔时间.
```

### Type

```
java.lang.Integer
```

### Category

```
ha
```

### 取值范围

```
[1, 9223372036854775807]
```

### 取值范围补充说明

```
无
```

### DefaultValue

```
60
```

### 默认值补充说明

```
无
```

### 支持的资源级配置



### 资源粒度说明

```
无
```

### 背景信息

```
见 ZSTAC-71704
```

### UI暴露

```
不需要
```

### CLI手册暴露

```
需要
```

## 注意事项

```
无
```