package org.zstack.header.host

import org.zstack.header.host.APIDeleteHostNetworkServiceTypeEvent

doc {
    title "DeleteHostNetworkServiceType"

    category "host"

    desc """在这里填写API描述"""

    rest {
        request {
			url "DELETE /v1/hosts/service-types/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeleteHostNetworkServiceTypeMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "5.2.1"
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc "删除模式(Permissive / Enforcing，Permissive)"
					location "body"
					type "String"
					optional true
					since "5.2.1"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.2.1"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.2.1"
				}
			}
        }

        response {
            clz APIDeleteHostNetworkServiceTypeEvent.class
        }
    }
}