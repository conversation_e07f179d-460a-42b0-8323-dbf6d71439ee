package org.zstack.network.huawei.imaster;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.tag.AutoDeleteTag;
import org.zstack.header.vo.ForeignKey;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.vo.ToInventory;
import org.zstack.sdnController.header.SdnControllerVO;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Huawei iMaster System Information Value Object
 */
@Entity
@Table
@AutoDeleteTag
public class HuaweiIMasterSystemInfoVO extends ResourceVO implements ToInventory, OwnedByAccount {

    @Column
    @ForeignKey(parentEntityClass = SdnControllerVO.class, parentKey = "uuid", onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String sdnControllerUuid;

    @Column
    private String systemDescription;

    @Column
    private String objectId;

    @Column
    private String systemUpTime;

    @Column
    private String deviceEsn;

    @Column
    private String platformName;

    @Column
    private String platformVersion;

    @Column
    private String productName;

    @Column
    private String productVersion;

    @Column
    private String adminIp;

    @Column
    private String vendorName;

    @Column
    private String machine;

    @Column
    private String osName;

    @Column
    private String osRelease;

    @Column
    private String osVersion;

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;
    
    @Transient
    private String accountUuid;
    
    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public String getSdnControllerUuid() {
        return sdnControllerUuid;
    }

    public void setSdnControllerUuid(String sdnControllerUuid) {
        this.sdnControllerUuid = sdnControllerUuid;
    }

    public String getSystemDescription() {
        return systemDescription;
    }

    public void setSystemDescription(String systemDescription) {
        this.systemDescription = systemDescription;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getSystemUpTime() {
        return systemUpTime;
    }

    public void setSystemUpTime(String systemUpTime) {
        this.systemUpTime = systemUpTime;
    }

    public String getDeviceEsn() {
        return deviceEsn;
    }

    public void setDeviceEsn(String deviceEsn) {
        this.deviceEsn = deviceEsn;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getPlatformVersion() {
        return platformVersion;
    }

    public void setPlatformVersion(String platformVersion) {
        this.platformVersion = platformVersion;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductVersion() {
        return productVersion;
    }

    public void setProductVersion(String productVersion) {
        this.productVersion = productVersion;
    }

    public String getAdminIp() {
        return adminIp;
    }

    public void setAdminIp(String adminIp) {
        this.adminIp = adminIp;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getMachine() {
        return machine;
    }

    public void setMachine(String machine) {
        this.machine = machine;
    }

    public String getOsName() {
        return osName;
    }

    public void setOsName(String osName) {
        this.osName = osName;
    }

    public String getOsRelease() {
        return osRelease;
    }

    public void setOsRelease(String osRelease) {
        this.osRelease = osRelease;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    @Override
    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }
}
