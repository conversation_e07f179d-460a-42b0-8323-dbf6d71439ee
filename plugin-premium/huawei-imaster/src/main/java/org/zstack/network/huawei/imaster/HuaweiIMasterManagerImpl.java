package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.header.AbstractService;
import org.zstack.header.core.Completion;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.network.NetworkException;
import org.zstack.header.network.l2.APICreateL2NetworkMsg;
import org.zstack.header.network.l2.L2NetworkCreateExtensionPoint;
import org.zstack.header.network.l2.L2NetworkInventory;
import org.zstack.header.network.service.*;
import org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkConstant;
import org.zstack.network.l2.vxlan.vxlanNetworkPool.VniRangeVO;
import org.zstack.sdnController.header.HardwareL2VxlanNetworkPoolVO;
import org.zstack.sdnController.header.SdnControllerMessage;
import org.zstack.sdnController.header.SdnControllerVO;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;


/**
 * Huawei iMaster Controller Manager Implementation
 */
public class HuaweiIMasterManagerImpl extends AbstractService implements
        HuaweiIMasterManager, L2NetworkCreateExtensionPoint {
    
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterManagerImpl.class);

    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private HuaweiIMasterSdnControllerFactory factory;

    @Override
    public boolean start() {
        logger.info("Starting Huawei iMaster Controller Manager");
        return true;
    }

    @Override
    public boolean stop() {
        logger.info("Stopping Huawei iMaster Controller Manager");
        return true;
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof SdnControllerMessage) {
            handleHuaweiIMasterMsg((SdnControllerMessage)msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handleHuaweiIMasterMsg(SdnControllerMessage msg) {
        SdnControllerVO vo = dbf.findByUuid(msg.getSdnControllerUuid(), SdnControllerVO.class);
        if (vo == null) {
            bus.dealWithUnknownMessage((APIMessage)msg);
            return;
        }

        HuaweiIMasterSdnController controller = new HuaweiIMasterSdnController(vo);
        controller.handleMessage(msg);
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(HuaweiIMasterConstant.SERVICE_ID);
    }

    @Override
    public void beforeCreateL2Network(APICreateL2NetworkMsg msg) throws NetworkException {

    }

    @Override
    public void postCreateL2Network(L2NetworkInventory l2Network, APICreateL2NetworkMsg msg, Completion completion) {
        L2NetworkCreateExtensionPoint.super.postCreateL2Network(l2Network, msg, completion);
    }

    @Override
    public void afterCreateL2Network(L2NetworkInventory l2Network) {
        HardwareL2VxlanNetworkPoolVO vxlanNetworkPoolVO = dbf.findByUuid(l2Network.getUuid(), HardwareL2VxlanNetworkPoolVO.class);
        if (vxlanNetworkPoolVO == null) {
            return;
        }

        SdnControllerVO sdnControllerVO = dbf.findByUuid(vxlanNetworkPoolVO.getSdnControllerUuid(), SdnControllerVO.class);
        if (!sdnControllerVO.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }

        // create a default vni range
        VniRangeVO vo = new VniRangeVO();
        vo.setUuid(Platform.getUuid());
        vo.setName(l2Network.getName());
        vo.setDescription(l2Network.getDescription());
        vo.setStartVni(VxlanNetworkConstant.MIN_VNI);
        vo.setEndVni(VxlanNetworkConstant.MAX_VNI);
        vo.setL2NetworkUuid(l2Network.getUuid());
        vo = dbf.persistAndRefresh(vo);
        logger.debug(String.format("create default vni range [%s] for vxlan pool", JSONObjectUtil.toJsonString(vo)));
    }
}
