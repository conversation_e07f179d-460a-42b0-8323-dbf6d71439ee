package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.network.l3.SdnControllerL3;
import org.zstack.network.securitygroup.SecurityGroupSdnBackend;
import org.zstack.sdnController.SdnController;
import org.zstack.sdnController.SdnControllerFactory;
import org.zstack.sdnController.SdnControllerL2;
import org.zstack.sdnController.SdnControllerType;
import org.zstack.sdnController.header.SdnControllerVO;

public class HuaweiIMasterSdnControllerFactory implements SdnControllerFactory {
    private static final  SdnControllerType huaweiIMasterSdn = new SdnControllerType(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE);

    @Autowired
    private DatabaseFacade dbf;

    @Override
    public SdnControllerType getVendorType() {
        return huaweiIMasterSdn;
    }

    @Override
    public SdnControllerVO persistSdnController(SdnControllerVO vo) {
        vo = dbf.persistAndRefresh(vo);
        return vo;
    }

    @Override
    public SdnController getSdnController(SdnControllerVO vo) {
        return new HuaweiIMasterSdnController(vo);
    }

    @Override
    public SdnControllerL2 getSdnControllerL2(SdnControllerVO vo) {
        return new HuaweiIMasterSdnController(vo);
    }

    @Override
    public SecurityGroupSdnBackend getSdnControllerSecurityGroup(SdnControllerVO vo) {
        return null;
    }

    @Override
    public SdnControllerL3 getSdnControllerL3(SdnControllerVO vo) {
        return new HuaweiIMasterSdnController(vo);
    }
}
