package org.zstack.network.huawei.imaster;

import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.tag.TagDefinition;
import org.zstack.sdnController.header.SdnControllerVO;
import org.zstack.tag.PatternedSystemTag;

/**
 * Huawei iMaster System Tags
 */
@TagDefinition
public class HuaweiIMasterSystemTags {
    public static String L2_NETWORK_HUAWEI_VPC_UUID_TOKEN = "huaweiVpcUuid";
    public static PatternedSystemTag L2_NETWORK_HUAWEI_VPC_UUID = new PatternedSystemTag(String.format("huaweiVpcUuid::{%s}",
            L2_NETWORK_HUAWEI_VPC_UUID_TOKEN), L2NetworkVO.class);

    public static String L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN = "huaweiTenantUuid";
    public static PatternedSystemTag L2_NETWORK_HUAWEI_TENANT_UUID = new PatternedSystemTag(String.format("huaweiTenantUuid::{%s}",
            L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN), L2NetworkVO.class);

    public static String HUAWEI_START_VNI_TOKEN = "startVni";
    public static String HUAWEI_END_VNI_TOKEN = "endVni";
    public static PatternedSystemTag HUAWEI_VNI_RANGE = new PatternedSystemTag(String.format("startVni::{%s}::endVni::{%s}",
            HUAWEI_START_VNI_TOKEN, HUAWEI_END_VNI_TOKEN), SdnControllerVO.class);

    public static String HUAWEI_START_VLAN_TOKEN = "startVlan";
    public static String HUAWEI_END_VLAN_TOKEN = "endVlan";
    public static PatternedSystemTag HUAWEI_VLAN_RANGE = new PatternedSystemTag(String.format("startVlan::{%s}::endVlan::{%s}",
            HUAWEI_START_VLAN_TOKEN, HUAWEI_END_VLAN_TOKEN), SdnControllerVO.class);

}
