package org.zstack.portMirror;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.compute.vm.VmInstanceManager;
import org.zstack.compute.vm.VmNicQosConfigBackend;
import org.zstack.compute.vm.VmNicQosStruct;
import org.zstack.core.db.Q;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.configuration.InstanceOfferingVO;
import org.zstack.header.message.APIMessage;
import org.zstack.header.network.l2.L2NetworkClusterRefVO;
import org.zstack.header.network.l2.L2NetworkClusterRefVO_;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO_;
import org.zstack.header.portMirror.*;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmInstanceVO_;
import org.zstack.header.vm.VmNicVO;
import org.zstack.header.vm.VmNicVO_;
import org.zstack.kvm.KVMConstant;
import org.zstack.mevoco.MevocoSystemTags;

import java.util.Arrays;
import java.util.List;

import static org.zstack.core.Platform.argerr;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-09
 **/
public class PortMirrorApiInterceptor implements ApiMessageInterceptor {
    @Autowired
    VmInstanceManager vmMgr;

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreatePortMirrorMsg) {
            validate((APICreatePortMirrorMsg) msg);
        } else if (msg instanceof APIUpdatePortMirrorMsg) {
            validate((APIUpdatePortMirrorMsg) msg);
        } else if (msg instanceof APICreatePortMirrorSessionMsg) {
            validate((APICreatePortMirrorSessionMsg) msg);
        }
        return msg;
    }

    private void validate(APICreatePortMirrorMsg msg) {
        /*the l3 uuid is not the mirror network type*/
        L3NetworkVO mirrorNetwork = Q.New(L3NetworkVO.class).eq(L3NetworkVO_.uuid, msg.getMirrorNetworkUuid()).find();
        if (mirrorNetwork == null || !mirrorNetwork.isSystem() ||
                !PortMirrorSystemTags.PORT_MIRROR_NETWORK.hasTag(mirrorNetwork.getUuid())) {
            throw new ApiMessageInterceptionException(argerr("Invalid parameter [%s], make sure it's PortMirror Network",
                    msg.getMirrorNetworkUuid()));
        }

        /*
        mirror network 1:1 PortMirrorVO
         */
        if (Q.New(PortMirrorVO.class).eq(PortMirrorVO_.mirrorNetworkUuid, msg.getMirrorNetworkUuid()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("The network[%s] has been attached with a PortMirror service",
                    msg.getMirrorNetworkUuid()));
        }

    }

    private void validate(APIUpdatePortMirrorMsg msg) {
        if (!Q.New(PortMirrorVO.class).eq(PortMirrorVO_.uuid, msg.getUuid()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("The PortMirror service[%s] has not been created",
                    msg.getUuid()));
        }
    }

    private void validate(APICreatePortMirrorSessionMsg msg) {
        /*
        * 1. source vnic just assign to one portmirror, one nic can't be as source&dest in different sessions
        * 2. source vnic just belongs to uservm
        * 3. dst vnic can't be default
        * 4. dst vnic just belongs to uservm
        * 5. src&dst instance must be in kvm host
        * 6. vnic can't configure qos that uses also the TC.
        * */
        PortMirrorSessionVO mirror = Q.New(PortMirrorSessionVO.class).in(PortMirrorSessionVO_.srcEndPoint, Arrays.asList(msg.getSrcEndPoint(), msg.getDstEndPoint())).find();
        if (mirror != null) {
            throw new ApiMessageInterceptionException(argerr("The nic[%s, %s] has been mirrored by service[%s]",
                    msg.getSrcEndPoint(), msg.getDstEndPoint(), mirror.getUuid()));
        }

        mirror = Q.New(PortMirrorSessionVO.class).eq(PortMirrorSessionVO_.dstEndPoint, msg.getSrcEndPoint()).find();
        if (mirror != null) {
            throw new ApiMessageInterceptionException(argerr("The nic[%s] can't been mirrored for service[%s] using",
                    msg.getSrcEndPoint(), mirror.getUuid()));
        }

        VmNicVO src = Q.New(VmNicVO.class).eq(VmNicVO_.uuid, msg.getSrcEndPoint()).find();
        if (src == null || src.getMetaData() != null) {
            throw new ApiMessageInterceptionException(argerr("The PortMirror service doesn't support to mirror the nic[%s]",
                    msg.getSrcEndPoint()));
        }

        if (!KVMConstant.KVM_HYPERVISOR_TYPE.equals(src.getHypervisorType())) {
            throw new ApiMessageInterceptionException(argerr("The PortMirror service doesn't support the nic[%s] because of its hypervisor type",
                    msg.getSrcEndPoint()));
        }

        VmNicVO dst = Q.New(VmNicVO.class).eq(VmNicVO_.uuid, msg.getDstEndPoint()).find();
        if (dst == null || dst.getMetaData() != null) {
            throw new ApiMessageInterceptionException(argerr("The PortMirror service doesn't support to mirror the nic[%s]",
                    msg.getSrcEndPoint()));
        }

        if (!KVMConstant.KVM_HYPERVISOR_TYPE.equals(dst.getHypervisorType())) {
            throw new ApiMessageInterceptionException(argerr("The PortMirror service doesn't support the nic[%s] because of its hypervisor type",
                    msg.getDstEndPoint()));
        }

        VmInstanceVO dstVm = Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, dst.getVmInstanceUuid()).find();
        if (dstVm == null || dst.getL3NetworkUuid().equals(dstVm.getDefaultL3NetworkUuid())) {
            throw new ApiMessageInterceptionException(
                    argerr("The PortMirror service can't mirror to the nic[%s] that is not a non-default interface of a vm",
                            msg.getDstEndPoint()));
        }

        VmInstanceVO srcVm = Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, src.getVmInstanceUuid()).find();
        if (srcVm == null ) {
            throw new ApiMessageInterceptionException(
                    argerr("The PortMirror service can't mirror the nic[%s] that is not an interface of any vm",
                            msg.getSrcEndPoint()));
        }

        PortMirrorVO vo = Q.New(PortMirrorVO.class).eq(PortMirrorVO_.uuid, msg.getPortMirrorUuid()).find();
        String l2NetworkUuid = Q.New(L3NetworkVO.class).select(L3NetworkVO_.l2NetworkUuid).eq(L3NetworkVO_.uuid, vo.getMirrorNetworkUuid()).findValue();
        List<String> clusterUuids = Q.New(L2NetworkClusterRefVO.class).select(L2NetworkClusterRefVO_.clusterUuid).eq(L2NetworkClusterRefVO_.l2NetworkUuid, l2NetworkUuid).listValues();

        if (clusterUuids.isEmpty() || !clusterUuids.contains(dstVm.getClusterUuid()) || !clusterUuids.contains(srcVm.getClusterUuid())) {
            throw new ApiMessageInterceptionException(
                    argerr("The PortMirror service can't mirror the nic[%s]  to nic[%s] because the mirror network[%s] can't setup the mirror tunnel",
                            msg.getSrcEndPoint(), msg.getDstEndPoint(), vo.getMirrorNetworkUuid()));
        }

        if (src.getUuid().equals(dst.getUuid())) {
            throw new ApiMessageInterceptionException(
                    argerr("The PortMirror service can't mirror the nic[%s] to itself", msg.getSrcEndPoint()));
        }

        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(srcVm.getType());
        VmNicQosStruct srcStruct = backend.getNicQos(srcVm.getUuid(), src.getUuid());
        VmNicQosStruct dstStruct = backend.getNicQos(dstVm.getUuid(), dst.getUuid());
        if (srcStruct.inboundBandwidth != -1L || dstStruct.inboundBandwidth != -1L ||
                srcStruct.outboundBandwidth != -1L || dstStruct.outboundBandwidth != -1L) {
            throw new ApiMessageInterceptionException(
                    argerr("The PortMirror service can't work at the nic with configured Qos"));
        }
    }
}
