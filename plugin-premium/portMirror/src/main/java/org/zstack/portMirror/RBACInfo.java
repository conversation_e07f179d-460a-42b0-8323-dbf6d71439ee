package org.zstack.portMirror;

import org.zstack.header.identity.rbac.RBACDescription;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-09
 **/
public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("portMirror")
                .adminOnlyAPIs("org.zstack.header.portMirror.**")
                .build();
    }

    @Override
    public void contributeToRoles() {
    }

    @Override
    public void roles() {

    }

    @Override
    public void globalReadableResources() {

    }
}
