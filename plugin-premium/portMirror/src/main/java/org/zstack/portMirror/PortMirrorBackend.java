package org.zstack.portMirror;

import org.zstack.header.core.Completion;
import org.zstack.header.portMirror.PortMirrorCommand;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-10
 **/
public interface PortMirrorBackend {
    void applyMirror(PortMirrorStruct struct, PortMirrorCommand cmd, Completion completion);

    void revokeMirror(PortMirrorStruct struct, PortMirrorCommand cmd, Completion completion);

    String getHypervisorType();
}
