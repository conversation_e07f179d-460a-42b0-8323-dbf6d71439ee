package org.zstack.portMirror;

import org.zstack.header.message.NeedReplyMessage;

/**
 * @author: zhanyong.miao
 * @date: 2019-10-14
 **/
public class ReleasePortMirrorSessionMsg extends NeedReplyMessage {
    private String sessionUuid;
    private String srcHostUuid;
    private String dstHostUuid;

    public String getSessionUuid() {
        return sessionUuid;
    }

    public void setSessionUuid(String sessionUuid) {
        this.sessionUuid = sessionUuid;
    }

    public String getSrcHostUuid() {
        return srcHostUuid;
    }

    public void setSrcHostUuid(String srcHostUuid) {
        this.srcHostUuid = srcHostUuid;
    }

    public String getDstHostUuid() {
        return dstHostUuid;
    }

    public void setDstHostUuid(String dstHostUuid) {
        this.dstHostUuid = dstHostUuid;
    }
}