package org.zstack.portMirror;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.header.message.NeedReplyMessage;
import org.zstack.header.network.l3.IpRangeInventory;
import org.zstack.header.network.l3.IpRangeVO;
import org.zstack.header.portMirror.*;
import org.zstack.header.vm.*;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-18
 **/
public class PortMirrorSessionCascadeExtension extends AbstractAsyncCascadeExtension {
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;

    private static final String NAME = PortMirrorSessionVO.class.getSimpleName();
    private static final CLogger logger = Utils.getLogger(PortMirrorSessionCascadeExtension.class);

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        completion.success();
    }

    private List<PortMirrorSessionInventory> PortMirrorSessionFromAction(CascadeAction action) {
        if (PortMirrorVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<PortMirrorInventory> invs = action.getParentIssuerContext();
            List<PortMirrorSessionVO> sessions = null;
            if (invs != null && !invs.isEmpty()) {
                List<String> mirrorUuids = invs.stream().map(PortMirrorInventory::getUuid).collect(Collectors.toList());
                sessions = Q.New(PortMirrorSessionVO.class).in(PortMirrorSessionVO_.portMirrorUuid, mirrorUuids).list();
            }

            if (sessions != null && !sessions.isEmpty()) {
                return PortMirrorSessionInventory.valueOf(sessions);
            }

        } else if (VmInstanceVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<VmDeletionStruct> structs = action.getParentIssuerContext();
            List<PortMirrorSessionVO> sessions = null;
            if (structs != null && !structs.isEmpty()) {
                List<String> nicUuids = new ArrayList<>();
                List<VmInstanceInventory> invs = structs.stream().map(VmDeletionStruct::getInventory).collect(Collectors.toList());
                if (invs != null && !invs.isEmpty()) {
                    invs.stream().forEach(vm -> nicUuids.addAll(vm.getVmNics().stream().map(VmNicInventory::getUuid).collect(Collectors.toList())));

                    if ( nicUuids.isEmpty()) {
                        return null;
                    }
                    sessions = Q.New(PortMirrorSessionVO.class).in(PortMirrorSessionVO_.srcEndPoint, nicUuids).list();
                    sessions.addAll(Q.New(PortMirrorSessionVO.class).in(PortMirrorSessionVO_.dstEndPoint, nicUuids).list());
                }
            }

            if (sessions != null && !sessions.isEmpty()) {
                return PortMirrorSessionInventory.valueOf(sessions);
            }

        } else if (IpRangeVO.class.getSimpleName().equals(action.getParentIssuer())) {
            final List<String> ipruuids = CollectionUtils.transformToList((List<IpRangeInventory>) action.getParentIssuerContext(), new Function<String, IpRangeInventory>() {
                @Override
                public String call(IpRangeInventory arg) {
                    return arg.getUuid();
                }
            });

            List<PortMirrorSessionVO> sessionVos = new Callable<List<PortMirrorSessionVO>>() {
                @Override
                @Transactional(readOnly = true)
                public List<PortMirrorSessionVO> call() {
                    String sql = "select distinct session from PortMirrorSessionVO session, MirrorNetworkUsedIpVO mirrorIp, PortMirrorSessionMirrorNetworkRefVO ref," +
                            " UsedIpVO ip where session.uuid = ref.sessionUuid and (ref.srcTunnelUuid = mirrorIp.uuid or ref.dstTunnelUuid = mirrorIp.uuid)" +
                            " and mirrorIp.uuid = ip.uuid and ip.ipRangeUuid in (:uuids)";
                    TypedQuery<PortMirrorSessionVO> q = dbf.getEntityManager().createQuery(sql, PortMirrorSessionVO.class);
                    q.setParameter("uuids", ipruuids);
                    return q.getResultList();
                }
            }.call();

            if (!sessionVos.isEmpty()) {
                return PortMirrorSessionInventory.valueOf(sessionVos);
            }

        } else if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }
        return null;
    }


    private void handleDeletion(final CascadeAction action, final Completion completion) {
        final List<PortMirrorSessionInventory> invs = PortMirrorSessionFromAction(action);
        List<String> uuids = new ArrayList<>();
        if (invs != null && !invs.isEmpty()) {
            for (PortMirrorSessionInventory inv : invs) {
                logger.debug(String.format("delete Port Mirror session[uuid:%s]", inv.getUuid()));
                uuids.add(inv.getUuid());
            }
        }

        if (uuids.isEmpty()) {
            completion.success();
            return;
        }

        ErrorCodeList errorCodes = new ErrorCodeList();
        new While<>(invs).all((inv, whileCompletion) -> {
            List<String> vmUuids = Q.New(VmNicVO.class).select(VmNicVO_.vmInstanceUuid)
                                    .in(VmNicVO_.uuid, Arrays.asList(inv.getSrcEndPoint(), inv.getDstEndPoint())).listValues();
            DeletePortMirrorSessionMsg rmsg = new DeletePortMirrorSessionMsg();
            rmsg.setUuid(inv.getUuid());
            bus.makeTargetServiceIdByResourceUuid(rmsg, PortMirrorConstant.SERVICE_ID, inv.getUuid());
            NeedReplyMessage cmsg = rmsg;
            if ( !vmUuids.isEmpty()) {
                vmUuids = vmUuids.stream().distinct().sorted().collect(Collectors.toList());
                for (String vmUuid : vmUuids) {
                    RefreshPortMirrorSessionOverlapMsg overlapMsg = new RefreshPortMirrorSessionOverlapMsg();
                    overlapMsg.setVmInstanceUuid(vmUuid);
                    overlapMsg.setMessage(cmsg);
                    bus.makeTargetServiceIdByResourceUuid(overlapMsg, VmInstanceConstant.SERVICE_ID, vmUuid);
                    cmsg = overlapMsg;
                }
            }
            bus.send(cmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(reply.getError().toString());
                        errorCodes.getCauses().add(reply.getError());
                        whileCompletion.done();
                    } else {
                        logger.debug(String.format("delete port mirror session[uuid:%s] success", rmsg.getUuid()));
                        whileCompletion.done();
                    }
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodes.getCauses().isEmpty()) {
                    /*delete vms concurrently, the sessions maybe deleted multi-times, http://jira.zstack.io/browse/ZSTAC-24997*/
                    List<PortMirrorSessionVO> sessionVOS = dbf.listByPrimaryKeys(uuids, PortMirrorSessionVO.class);
                    if (!sessionVOS.isEmpty()) {
                        logger.error(String.format("failed to delete portMirror Sessions: %d", errorCodes.getCauses().size()));
                        completion.fail(errorCodes.getCauses().get(0));
                        return;
                    }
                }

                if (IpRangeVO.class.getSimpleName().equals(action.getParentIssuer())) {
                    final List<String> ipruuids = CollectionUtils.transformToList((List<IpRangeInventory>) action.getParentIssuerContext(), new Function<String, IpRangeInventory>() {
                        @Override
                        public String call(IpRangeInventory arg) {
                            return arg.getUuid();
                        }
                    });
                    List<MirrorNetworkUsedIpVO> usedIpVos = new Callable<List<MirrorNetworkUsedIpVO>>() {
                        @Override
                        @Transactional(readOnly = true)
                        public List<MirrorNetworkUsedIpVO> call() {
                            String sql = "select distinct mirrorIp from MirrorNetworkUsedIpVO mirrorIp, UsedIpVO ip where" +
                                    " mirrorIp.uuid = ip.uuid and ip.ipRangeUuid in (:uuids)";
                            TypedQuery<MirrorNetworkUsedIpVO> q = dbf.getEntityManager().createQuery(sql, MirrorNetworkUsedIpVO.class);
                            q.setParameter("uuids", ipruuids);
                            return q.getResultList();
                        }
                    }.call();

                    if (!usedIpVos.isEmpty()) {
                        dbf.removeCollection(usedIpVos, MirrorNetworkUsedIpVO.class);
                    }
                }

                logger.debug(String.format("remove port mirror sessions[uuid:%s] success", uuids));
                completion.success();

            }
        });
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(
                IpRangeVO.class.getSimpleName(),
                PortMirrorVO.class.getSimpleName(),
                VmInstanceVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<PortMirrorSessionInventory> ctx = PortMirrorSessionFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}
