package org.zstack.portMirror;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.errorcode.ErrorFacade;
import org.zstack.core.upgrade.GrayVersion;
import org.zstack.header.core.Completion;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.host.HostConstant;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.L2NetworkInventory;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l3.NormalIpRangeVO;
import org.zstack.header.network.l3.IpRangeVO_;
import org.zstack.header.network.l3.NormalIpRangeVO_;
import org.zstack.header.portMirror.PortMirrorCommand;
import org.zstack.header.portMirror.PortMirrorSystemTags;
import org.zstack.header.portMirror.SessionType;
import org.zstack.kvm.*;

import javax.persistence.TypedQuery;

import static org.zstack.core.Platform.operr;
/**
 * @author: zhanyong.miao
 * @date: 2019-09-17
 **/
public class PortMirrorKvmBackend implements PortMirrorBackend {
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    @Qualifier("KVMHostFactory")
    private KVMHostFactory factory;
    @Autowired
    protected ErrorFacade errf;

    public static final String APPLY_MIRROR_SOURCE = "/portmirror/apply/source";
    public static final String APPLY_MIRROR_DEST = "/portmirror/apply/dest";
    public static final String RELEASE_MIRROR_SROURCE = "/portmirror/release/source";
    public static final String RELEASE_MIRROR_DEST = "/portmirror/release/dest";

    class Tunnel {
        public String dev;
        public String localIp;
        public String remoteIp;
        public String gw;
        public Integer prefix;
        public Long key;
        public String uuid;
    }

    class Mirror {
        public SessionType type;
        public String snic;
        public String dnic;
        public String bridge;
        public String mName;
    }

    public static class PortMirrorApplyCmd extends KVMAgentCommands.AgentCommand {
        @GrayVersion(value = "5.0.0")
        public Tunnel tunnel;
        @GrayVersion(value = "5.0.0")
        public Mirror mirror;
        @GrayVersion(value = "5.0.0")
        public Boolean isLocal;
    }

    public static class PortMirrorApplyRsp extends KVMAgentCommands.AgentResponse {

    }

    public static class PortMirrorReleaseCmd extends KVMAgentCommands.AgentCommand {
        @GrayVersion(value = "5.0.0")
        public Tunnel tunnel;
        @GrayVersion(value = "5.0.0")
        public Mirror mirror;
        @GrayVersion(value = "5.0.0")
        public Boolean isLocal;
    }

    public static class PortMirrorReleaseRsp extends KVMAgentCommands.AgentResponse {

    }

    @Transactional
    private L2NetworkInventory getL2NetworkTypeFromL3NetworkUuid(String l3NetworkUuid) {
        String sql = "select l2 from L2NetworkVO l2 where l2.uuid = (select l3.l2NetworkUuid from L3NetworkVO l3 where l3.uuid = :l3NetworkUuid)";
        TypedQuery<L2NetworkVO> query = dbf.getEntityManager().createQuery(sql, L2NetworkVO.class);
        query.setParameter("l3NetworkUuid", l3NetworkUuid);
        L2NetworkVO l2vo = query.getSingleResult();
        return L2NetworkInventory.valueOf(l2vo);
    }

    private String getPhysicalInterfaceName(String L3Uuid) {
        String bridgeName;
        L2NetworkInventory l2inv = getL2NetworkTypeFromL3NetworkUuid(L3Uuid);
        bridgeName = KVMSystemTags.L2_BRIDGE_NAME.getTokenByResourceUuid(l2inv.getUuid(), KVMSystemTags.L2_BRIDGE_NAME_TOKEN);

        if (bridgeName == null) {
            /*vxlan lazy-attach mode, to be done*/

        }
        return bridgeName;
    }

    private Tunnel generateTunnelInfor(PortMirrorStruct struct, PortMirrorCommand cmd) {
        Tunnel tunnel = new Tunnel();
        tunnel.dev = getPhysicalInterfaceName(struct.getSrcTunnelIp().getL3NetworkUuid());
        if (PortMirrorCommand.APPLY_TO_SRC.equals(cmd)) {
            tunnel.localIp = struct.getSrcTunnelIp().getUsedIpInventory().getIp();
            tunnel.remoteIp = struct.getDstTunnelIp().getUsedIpInventory().getIp();
        } else {
            tunnel.remoteIp = struct.getSrcTunnelIp().getUsedIpInventory().getIp();
            tunnel.localIp = struct.getDstTunnelIp().getUsedIpInventory().getIp();
        }
        tunnel.gw = struct.getSrcTunnelIp().getUsedIpInventory().getGateway();
        tunnel.prefix = Q.New(NormalIpRangeVO.class).select(NormalIpRangeVO_.prefixLen).eq(NormalIpRangeVO_.uuid, struct.getSrcTunnelIp().getUsedIpInventory().getIpRangeUuid()).findValue();
        tunnel.key = struct.getInternalId();
        /*use the uuid to check if the gre tunnel is changed in agent*/
        tunnel.uuid = String.format("uuid:%s src/dst:%s/%s l3Uuid:%s", struct.getSession().getUuid(), struct.getSrcTunnelIp().getHostUuid(), struct.getDstTunnelIp().getHostUuid(), struct.getSrcTunnelIp().getL3NetworkUuid());

        return tunnel;
    }

    private String buildMirrorDeviceName(PortMirrorStruct struct) {
        String sequence = PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE.getTokenByResourceUuid(struct.getSrcTunnelIp().getHostUuid(), PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE_TOKEN);
        if (sequence == null) {
            throw new OperationFailureException(errf.instantiateErrorCode(SysErrors.RESOURCE_NOT_FOUND,
                    String.format("the host[%s] sequence systag resource not found nic name[%s]",
                            struct.getSrcTunnelIp().getHostUuid(), struct.getSrcNic().getInternalName())
            ));
        }
        String[] strings = struct.getSrcNic().getInternalName().split("\\.");
        if ( strings.length != 2 || !StringUtils.isNumeric(strings[1])) {
            throw new OperationFailureException(errf.instantiateErrorCode(SysErrors.INVALID_ARGUMENT_ERROR,
                    String.format("the source nic name[%s] is invalid",
                            struct.getSrcNic().getInternalName())
            ));
        }

        return struct.getSrcNic().getInternalName().replace("vnic","");
        //return String.format("%s%c%s",strings[0], (char)(Integer.parseInt(strings[1] + (int)'a')), sequence.substring(sequence.length()-5)).replace("vnic", "r");
    }

    private Mirror generateMirrorInfor(PortMirrorStruct struct, PortMirrorCommand cmd) {
        Mirror mirror = new Mirror();
        mirror.type = struct.getSession().getType();
        mirror.snic = struct.getSrcNic().getInternalName();
        mirror.dnic = struct.getDstNic().getInternalName();
        mirror.bridge = getPhysicalInterfaceName(struct.getDstNic().getL3NetworkUuid());
        mirror.mName = buildMirrorDeviceName(struct);

        return mirror;
    }

    @Override
    public void applyMirror(PortMirrorStruct struct, PortMirrorCommand cmd, Completion completion) {
        /*
        * 1. generate the cmd parameter
        * 2. send cmd (config tunnel, config tc mirror)
        * */
        PortMirrorApplyCmd acmd = new PortMirrorApplyCmd();
        String hostUuid = struct.getSrcTunnelIp().getHostUuid();
        if (PortMirrorCommand.APPLY_TO_DST.equals(cmd)) {
            hostUuid = struct.getDstTunnelIp().getHostUuid();
        }

        acmd.isLocal = struct.getDstTunnelIp().equals(struct.getSrcTunnelIp());
        acmd.mirror = generateMirrorInfor(struct, cmd);
        acmd.tunnel = generateTunnelInfor(struct, cmd);
        KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
        msg.setHostUuid(hostUuid);
        msg.setCommand(acmd);
        if (PortMirrorCommand.APPLY_TO_DST.equals(cmd)) {
            msg.setPath(APPLY_MIRROR_DEST);
        } else {
            msg.setPath(APPLY_MIRROR_SOURCE);
        }
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                KVMHostAsyncHttpCallReply r = reply.castReply();
                PortMirrorApplyRsp rsp = r.toResponse(PortMirrorApplyRsp.class);
                if (!rsp.isSuccess()) {
                    completion.fail(operr("operation error, because:%s", rsp.getError()));
                    return;
                }

                completion.success();
            }
        });
    }

    @Override
    public void revokeMirror(PortMirrorStruct struct, PortMirrorCommand cmd, Completion completion) {
        PortMirrorReleaseCmd acmd = new PortMirrorReleaseCmd();
        String hostUuid = struct.getSrcTunnelIp().getHostUuid();
        if (PortMirrorCommand.APPLY_TO_DST.equals(cmd)) {
            hostUuid = struct.getDstTunnelIp().getHostUuid();
        }

        acmd.isLocal = struct.getDstTunnelIp().equals(struct.getSrcTunnelIp());
        acmd.mirror = generateMirrorInfor(struct, cmd);
        acmd.tunnel = generateTunnelInfor(struct, cmd);

        KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
        msg.setHostUuid(hostUuid);
        msg.setCommand(acmd);
        if (PortMirrorCommand.APPLY_TO_DST.equals(cmd)) {
            msg.setPath(RELEASE_MIRROR_DEST);
        } else {
            msg.setPath(RELEASE_MIRROR_SROURCE);
        }
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                KVMHostAsyncHttpCallReply r = reply.castReply();
                PortMirrorApplyRsp rsp = r.toResponse(PortMirrorApplyRsp.class);
                if (!rsp.isSuccess()) {
                    completion.fail(operr("operation error, because:%s", rsp.getError()));
                    return;
                }

                completion.success();
            }
        });
    }

    @Override
    public String getHypervisorType() {
        return KVMConstant.KVM_HYPERVISOR_TYPE;
    }
}
