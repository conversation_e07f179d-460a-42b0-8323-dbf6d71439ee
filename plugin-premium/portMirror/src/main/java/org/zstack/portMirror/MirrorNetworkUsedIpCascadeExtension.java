package org.zstack.portMirror;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l3.*;
import org.zstack.header.portMirror.MirrorNetworkUsedIpInventory;
import org.zstack.header.portMirror.MirrorNetworkUsedIpVO;
import org.zstack.header.portMirror.PortMirrorSessionMirrorNetworkRefVO;
import org.zstack.header.portMirror.PortMirrorSessionMirrorNetworkRefVO_;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * @author: zhanyong.miao
 * @date: 2019-10-31
 **/
public class MirrorNetworkUsedIpCascadeExtension extends AbstractAsyncCascadeExtension {
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;

    private static final String NAME = MirrorNetworkUsedIpVO.class.getSimpleName();
    private static final CLogger logger = Utils.getLogger(MirrorNetworkUsedIpCascadeExtension.class);

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        completion.success();
    }

    private List<MirrorNetworkUsedIpInventory> PortMirrorUsedIpFromAction(CascadeAction action) {
        if (IpRangeVO.class.getSimpleName().equals(action.getParentIssuer())) {
            final List<String> ipruuids = CollectionUtils.transformToList((List<IpRangeInventory>) action.getParentIssuerContext(), new Function<String, IpRangeInventory>() {
                @Override
                public String call(IpRangeInventory arg) {
                    return arg.getUuid();
                }
            });

            List<MirrorNetworkUsedIpVO> vos = new Callable<List<MirrorNetworkUsedIpVO>>() {
                @Override
                @Transactional(readOnly = true)
                public List<MirrorNetworkUsedIpVO> call() {
                    String sql = "select distinct mirrorIp from MirrorNetworkUsedIpVO mirrorIp, " +
                            " UsedIpVO ip where mirrorIp.uuid = ip.uuid and ip.ipRangeUuid in (:uuids)";
                    TypedQuery<MirrorNetworkUsedIpVO> q = dbf.getEntityManager().createQuery(sql, MirrorNetworkUsedIpVO.class);
                    q.setParameter("uuids", ipruuids);
                    return q.getResultList();
                }
            }.call();

            if (!vos.isEmpty()) {
                return MirrorNetworkUsedIpInventory.valueOf(vos);
            }

        } else if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }
        return null;
    }


    private void handleDeletion(final CascadeAction action, final Completion completion) {
        final List<MirrorNetworkUsedIpInventory> invs = PortMirrorUsedIpFromAction(action);
        List<String> uuids = new ArrayList<>();
        if (invs != null && !invs.isEmpty()) {
            for (MirrorNetworkUsedIpInventory inv : invs) {
                uuids.add(inv.getUuid());
            }
        }

        if (!uuids.isEmpty()) {
            List<String> srcUuids = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).select(PortMirrorSessionMirrorNetworkRefVO_.srcTunnelUuid)
                    .in(PortMirrorSessionMirrorNetworkRefVO_.srcTunnelUuid, uuids).listValues();
            List<String> dstUuids = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).select(PortMirrorSessionMirrorNetworkRefVO_.dstTunnelUuid)
                                    .in(PortMirrorSessionMirrorNetworkRefVO_.dstTunnelUuid, uuids).listValues();
            uuids.removeAll(srcUuids);
            uuids.removeAll(dstUuids);
        }

        if (uuids.isEmpty()) {
            completion.success();
            return;
        }

        List<UsedIpVO> usedIps = Q.New(UsedIpVO.class).in(UsedIpVO_.uuid, uuids).list();

        ErrorCodeList errorCodes = new ErrorCodeList();
        new While<>(usedIps).all((usedIp, whileCompletion) -> {
            ReturnIpMsg rmsg = new ReturnIpMsg();
            rmsg.setL3NetworkUuid(usedIp.getL3NetworkUuid());
            rmsg.setUsedIpUuid(usedIp.getUuid());
            bus.makeTargetServiceIdByResourceUuid(rmsg, L3NetworkConstant.SERVICE_ID, usedIp.getL3NetworkUuid());
            bus.send(rmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        dbf.removeByPrimaryKey(usedIp.getUuid(), MirrorNetworkUsedIpVO.class);
                    } else {
                        errorCodes.getCauses().add(reply.getError());
                    }
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodes.getCauses().isEmpty()) {
                    logger.error(String.format("failed to free mirror usedIp: %d", errorCodes.getCauses().size()));
                    completion.fail(errorCodes.getCauses().get(0));
                } else {
                    completion.success();
                }
            }
        });
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(
                IpRangeVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<MirrorNetworkUsedIpInventory> ctx = PortMirrorUsedIpFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}
