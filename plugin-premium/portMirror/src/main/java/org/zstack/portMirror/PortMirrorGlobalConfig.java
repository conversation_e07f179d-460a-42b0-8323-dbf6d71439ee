package org.zstack.portMirror;

import org.zstack.core.config.GlobalConfig;
import org.zstack.core.config.GlobalConfigDefinition;
import org.zstack.core.config.GlobalConfigValidation;

/**
 * @author: zhanyong.miao
 * @date: 2019-10-16
 **/
@GlobalConfigDefinition
public class PortMirrorGlobalConfig {
    public static final String CATEGORY = "portmirror";

    @GlobalConfigValidation(numberGreaterThan = 0)
    public static GlobalConfig SESSION_HEALTH_CHECK_INTERVAL = new GlobalConfig(CATEGORY, "portMirrorSession.healthCheck.interval");

}
