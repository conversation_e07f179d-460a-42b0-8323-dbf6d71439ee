package org.zstack.portMirror;

import org.zstack.header.core.Completion;
import org.zstack.header.portMirror.PortMirrorSessionInventory;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-09
 **/
public interface PortMirrorManager {
    PortMirrorBackend getPortMirrorBackend(String hypervisorType);

    void startPortMirrorSession(PortMirrorSessionInventory inv, Completion completion);

    void stopPortMirrorSession(PortMirrorSessionInventory inv, Completion completion);

    PortMirrorStruct generatePortMirrorStruct(PortMirrorSessionInventory inv);
}
