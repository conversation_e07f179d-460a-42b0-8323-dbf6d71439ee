package org.zstack.portMirror;

import com.fasterxml.uuid.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.header.vm.VmNicChangeStateExtensionPoint;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.*;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SimpleQuery;
import org.zstack.core.errorcode.ErrorFacade;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.PeriodicTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.AbstractService;
import org.zstack.header.Component;
import org.zstack.header.configuration.InstanceOfferingVO;
import org.zstack.header.core.AsyncBackup;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NoErrorCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.HostAfterConnectedExtensionPoint;
import org.zstack.header.host.HostInventory;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.message.*;
import org.zstack.header.network.l2.L2NetworkClusterRefVO;
import org.zstack.header.network.l2.L2NetworkClusterRefVO_;
import org.zstack.header.network.l3.*;
import org.zstack.header.portMirror.*;
import org.zstack.header.tag.SystemTagVO;
import org.zstack.header.tag.SystemTagVO_;
import org.zstack.header.tag.TagType;
import org.zstack.header.vm.*;
import org.zstack.kvm.KVMConstant;
import org.zstack.mevoco.MevocoSystemTags;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.TagManager;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import javax.persistence.Tuple;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.err;
import static org.zstack.core.Platform.operr;
import static org.zstack.utils.CollectionDSL.*;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-09
 **/
public class PortMirrorManagerImpl extends AbstractService
        implements PortMirrorManager, Component,
                   VmDetachNicExtensionPoint, VmInstanceStopExtensionPoint,
                   HostAfterConnectedExtensionPoint, VmNicChangeStateExtensionPoint {
    private static CLogger logger = Utils.getLogger(PortMirrorManagerImpl.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private EventFacade evf;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    private ErrorFacade errf;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    protected ThreadFacade thdf;

    private Map<String, PortMirrorBackend> backends = new HashMap<>();
    private Future portMirrorSessionTracker;

    @Override
    public PortMirrorBackend getPortMirrorBackend(String hypervisorType) {
        PortMirrorBackend bkd = backends.get(hypervisorType);
        if (bkd == null) {
            throw new CloudRuntimeException(String.format("cannot find PortMirrorBackend for type[%s]", hypervisorType));
        }

        return bkd;
    }

    @Override
    public void startPortMirrorSession(PortMirrorSessionInventory inv, Completion completion) {

        /*
        * step:
        * 1.alloc mirror Network IP for hypervisor both source & dest port
        * 2.apply the session to source
        * 3.apply the session to dest
        * */
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-portmirror-%s-vmNic-%s-to-%s", inv.getUuid(), inv.getSrcEndPoint(), inv.getDstEndPoint()));
        chain.then(new ShareFlow() {
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "apply-source-tunnel-ip-for-port-mirror";
                    PortMirrorStruct struct;
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        struct = generatePortMirrorStruct(inv);
                        PortMirrorBackend bkd = getPortMirrorBackend(struct.getSrcNic().getHypervisorType());
                        bkd.applyMirror(struct, PortMirrorCommand.APPLY_TO_SRC, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        PortMirrorBackend bkd = getPortMirrorBackend(struct.getSrcNic().getHypervisorType());
                        bkd.revokeMirror(struct, PortMirrorCommand.APPLY_TO_SRC, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.rollback();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.rollback();
                            }
                        });
                    }
                });

                flow(new Flow() {
                    String __name__ = "apply-dest-tunnel-ip-for-port-mirror";
                    PortMirrorStruct struct;
                    @Override
                    public boolean skip(Map data) {
                        struct = generatePortMirrorStruct(inv);
                        return struct.getSrcTunnelIp().getUuid().equals(struct.getDstTunnelIp().getUuid());
                    }
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        PortMirrorBackend bkd = getPortMirrorBackend(struct.getDstNic().getHypervisorType());
                        bkd.applyMirror(struct, PortMirrorCommand.APPLY_TO_DST, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        PortMirrorBackend bkd = getPortMirrorBackend(struct.getDstNic().getHypervisorType());
                        bkd.revokeMirror(struct, PortMirrorCommand.APPLY_TO_DST, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.rollback();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.rollback();
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success();
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    @Override
    public void stopPortMirrorSession(PortMirrorSessionInventory inv, Completion completion) {
        /*
         * step:
         * 1.clean the session from source
         * 2.clean the session from dest
         * 3.revoke mirror Network IP for hypervisor both source & dest port if need
         * */
        PortMirrorStruct struct = generatePortMirrorStruct(inv);
        if (struct == null) {
            PortMirrorSessionMirrorNetworkRefVO refVO = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).eq(PortMirrorSessionMirrorNetworkRefVO_.sessionUuid, inv.getUuid()).find();
            if (refVO != null) {
                dbf.removeByPrimaryKey(refVO.getUuid(), PortMirrorSessionMirrorNetworkRefVO.class);
            }
            completion.success();
            return;
        }

        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("clean-portmirror-%s-vmNic-%s-to-%s", inv.getUuid(), inv.getSrcEndPoint(), inv.getDstEndPoint()));
        chain.then(new ShareFlow() {
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "clean-source-tunnel-ip-for-port-mirror";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        PortMirrorBackend bkd = getPortMirrorBackend(struct.getSrcNic().getHypervisorType());
                        bkd.revokeMirror(struct, PortMirrorCommand.APPLY_TO_SRC, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        trigger.rollback();
                    }
                });

                flow(new Flow() {
                    String __name__ = "clean-dest-tunnel-ip-for-port-mirror";

                    @Override
                    public boolean skip(Map data) {
                        return struct.getSrcTunnelIp().getUuid().equals(struct.getDstTunnelIp().getUuid());
                    }
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        PortMirrorBackend bkd = getPortMirrorBackend(struct.getDstNic().getHypervisorType());
                        bkd.revokeMirror(struct, PortMirrorCommand.APPLY_TO_DST, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        trigger.rollback();
                    }
                });

                flow(new Flow() {
                    String __name__ = "clear-DB-for-port-mirror";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        PortMirrorSessionMirrorNetworkRefVO refVO = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).eq(PortMirrorSessionMirrorNetworkRefVO_.sessionUuid, inv.getUuid()).find();
                        if (refVO != null) {
                            dbf.removeByPrimaryKey(refVO.getUuid(), PortMirrorSessionMirrorNetworkRefVO.class);
                        }
                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        trigger.rollback();
                    }
                });

                flow(new Flow() {
                    String __name__ = "revoke-source-tunnel-ip-for-port-mirror";

                    @Override
                    public boolean skip(Map data) {
                        return Q.New(PortMirrorSessionMirrorNetworkRefVO.class)
                                .eq(PortMirrorSessionMirrorNetworkRefVO_.srcTunnelUuid, struct.getSrcTunnelIp().getUuid()).count() > 0 ||
                                Q.New(PortMirrorSessionMirrorNetworkRefVO.class)
                                 .eq(PortMirrorSessionMirrorNetworkRefVO_.dstTunnelUuid, struct.getSrcTunnelIp().getUuid()).count() > 0;

                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        MirrorNetworkUsedIpInventory srcTunnelIp = struct.getSrcTunnelIp();
                        ReturnIpMsg msg = new ReturnIpMsg();
                        msg.setL3NetworkUuid(srcTunnelIp.getL3NetworkUuid());
                        msg.setUsedIpUuid(srcTunnelIp.getUsedIpInventory().getUuid());
                        bus.makeTargetServiceIdByResourceUuid(msg, L3NetworkConstant.SERVICE_ID, srcTunnelIp.getL3NetworkUuid());
                        bus.send(msg, new CloudBusCallBack(trigger){
                            @Override
                            public void run(MessageReply reply) {
                                dbf.removeByPrimaryKey(srcTunnelIp.getUuid(), MirrorNetworkUsedIpVO.class);
                                trigger.next();
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        trigger.rollback();
                    }
                });

                flow(new Flow() {
                    String __name__ = "revoke-dest-tunnel-ip-for-port-mirror";

                    @Override
                    public boolean skip(Map data) {
                        return Q.New(PortMirrorSessionMirrorNetworkRefVO.class)
                                .eq(PortMirrorSessionMirrorNetworkRefVO_.srcTunnelUuid, struct.getDstTunnelIp().getUuid()).count() > 0 ||
                                Q.New(PortMirrorSessionMirrorNetworkRefVO.class)
                                 .eq(PortMirrorSessionMirrorNetworkRefVO_.dstTunnelUuid, struct.getDstTunnelIp().getUuid()).count() > 0;
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        MirrorNetworkUsedIpInventory dstTunnelIp = struct.getDstTunnelIp();
                        ReturnIpMsg msg = new ReturnIpMsg();
                        msg.setL3NetworkUuid(dstTunnelIp.getL3NetworkUuid());
                        msg.setUsedIpUuid(dstTunnelIp.getUsedIpInventory().getUuid());
                        bus.makeTargetServiceIdByResourceUuid(msg, L3NetworkConstant.SERVICE_ID, dstTunnelIp.getL3NetworkUuid());
                        bus.send(msg, new CloudBusCallBack(trigger){
                            @Override
                            public void run(MessageReply reply) {
                                dbf.removeByPrimaryKey(dstTunnelIp.getUuid(), MirrorNetworkUsedIpVO.class);
                                trigger.next();
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        trigger.rollback();
                    }
                });


                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success();
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    @Override
    public PortMirrorStruct generatePortMirrorStruct(PortMirrorSessionInventory inv) {
        VmNicVO srcNic = dbf.findByUuid(inv.getSrcEndPoint(), VmNicVO.class);
        VmNicVO dstNic = dbf.findByUuid(inv.getDstEndPoint(), VmNicVO.class);

        if (srcNic == null || dstNic == null) {
            return null;
        }

        VmInstanceVO srcVm = Q.New(VmInstanceVO.class)
                              .eq(VmInstanceVO_.uuid, srcNic.getVmInstanceUuid()).find();
        VmInstanceVO dstVm = Q.New(VmInstanceVO.class)
                              .eq(VmInstanceVO_.uuid, dstNic.getVmInstanceUuid()).find();
        if ( srcVm == null || srcVm.getHostUuid() == null || dstVm == null || dstVm.getHostUuid() == null) {
            return null;
        }

        return generatePortMirrorStructWithHost(inv, srcVm.getHostUuid(), dstVm.getHostUuid());
    }

    private void directAllocateMirrorIp( AllocateMirrorNetworkIpMsg msg, NoErrorCompletion completion) {
        String mirrorNetwork = msg.getL3NetworkUuid();
        String hostUuid = msg.getHostUuid();

        AllocateMirrorNetworkIpReply reply = new AllocateMirrorNetworkIpReply();
        MirrorNetworkUsedIpVO ipVO = Q.New(MirrorNetworkUsedIpVO.class)
                                             .eq(MirrorNetworkUsedIpVO_.hostUuid, hostUuid)
                                             .eq(MirrorNetworkUsedIpVO_.l3NetworkUuid, mirrorNetwork).find();
        if (ipVO != null) {
            reply.setIpInventory(ipVO.toInventory());
            bus.reply(msg, reply);
            completion.done();
            return;
        }
        AllocateIpMsg amsg = new AllocateIpMsg();
        amsg.setL3NetworkUuid(mirrorNetwork);
        bus.makeTargetServiceIdByResourceUuid(amsg, L3NetworkConstant.SERVICE_ID, mirrorNetwork);
        MessageReply areply = bus.call(amsg);
        if (!areply.isSuccess()) {
            reply.setError(areply.getError());
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        AllocateIpReply r = areply.castReply();
        String clusterUuid = Q.New(HostVO.class).select(HostVO_.clusterUuid).eq(HostVO_.uuid, hostUuid).findValue();
        if ( clusterUuid == null) {
            throw new OperationFailureException(ErrorCode.fromString(String.format("invalid host uuid %s", hostUuid)));
        }

        ipVO = new MirrorNetworkUsedIpVO();
        ipVO.setUuid(r.getIpInventory().getUuid());
        ipVO.setHostUuid(hostUuid);
        ipVO.setClusterUuid(clusterUuid);
        ipVO.setL3NetworkUuid(mirrorNetwork);
        ipVO.setDescription(String.format("port mirror network[%s]", mirrorNetwork));
        ipVO = dbf.persistAndRefresh(ipVO);

        reply.setIpInventory(ipVO.toInventory());
        bus.reply(msg, reply);
        completion.done();
    }

    private MirrorNetworkUsedIpInventory allocateMirrorIp(String mirrorNetwork, String hostUuid, PortMirrorSessionInventory inv) {
        MirrorNetworkUsedIpVO ipVO = Q.New(MirrorNetworkUsedIpVO.class)
                                      .eq(MirrorNetworkUsedIpVO_.hostUuid, hostUuid)
                                      .eq(MirrorNetworkUsedIpVO_.l3NetworkUuid, mirrorNetwork).find();
        if (ipVO != null) {
            return ipVO.toInventory();
        }

        AllocateMirrorNetworkIpMsg amsg = new AllocateMirrorNetworkIpMsg();
        amsg.setL3NetworkUuid(mirrorNetwork);
        amsg.setHostUuid(hostUuid);
        bus.makeTargetServiceIdByResourceUuid(amsg, PortMirrorConstant.SERVICE_ID, inv.getUuid());
        MessageReply reply = bus.call(amsg);
        if (!reply.isSuccess()) {
            throw new OperationFailureException(reply.getError());
        }
        AllocateMirrorNetworkIpReply r = reply.castReply();
        return r.getIpInventory();
    }

    private PortMirrorStruct generatePortMirrorStructWithHost(PortMirrorSessionInventory inv, String srcHostUuid, String dstHostUuid) {
        PortMirrorStruct struct = new PortMirrorStruct();
        PortMirrorSessionMirrorNetworkRefVO sessionRef = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).eq(PortMirrorSessionMirrorNetworkRefVO_.sessionUuid, inv.getUuid()).find();

        /*acquire the tunnel ip for this session from mirror Network
        * 1. acquire source MirrorNetworkUsedIpVO
        * 2. acquire dest MirrorNetworkUsedIpVO
        * 3. generate PortMirrorSessionMirrorNetworkRefVO
        * */
        String mirrorNetwork = Q.New(PortMirrorVO.class).select(PortMirrorVO_.mirrorNetworkUuid)
                                .eq(PortMirrorVO_.uuid, inv.getPortMirrorUuid()).findValue();

        MirrorNetworkUsedIpInventory srcTunnalIp = allocateMirrorIp(mirrorNetwork, srcHostUuid, inv);
        MirrorNetworkUsedIpInventory dstTunnalIp = allocateMirrorIp(mirrorNetwork, dstHostUuid, inv);

        if (sessionRef == null) {
            sessionRef = new PortMirrorSessionMirrorNetworkRefVO();
            sessionRef.setUuid(Platform.getUuid());
            sessionRef.setSessionUuid(inv.getUuid());
            sessionRef.setSrcTunnelUuid(srcTunnalIp.getUuid());
            sessionRef.setDstTunnelUuid(dstTunnalIp.getUuid());
            dbf.persist(sessionRef);
        } else {
            if (!(sessionRef.getSrcTunnelUuid().equals(srcTunnalIp.getUuid()))
                    || ! (sessionRef.getDstTunnelUuid().equals(dstTunnalIp.getUuid()))) {
                sessionRef.setSrcTunnelUuid(srcTunnalIp.getUuid());
                sessionRef.setDstTunnelUuid(dstTunnalIp.getUuid());
                dbf.update(sessionRef);
            }
        }

        VmNicVO srcNic = dbf.findByUuid(inv.getSrcEndPoint(), VmNicVO.class);
        VmNicVO dstNic = dbf.findByUuid(inv.getDstEndPoint(), VmNicVO.class);

        struct.setSession(inv);
        struct.setSrcNic(VmNicInventory.valueOf(srcNic));
        struct.setDstNic(VmNicInventory.valueOf(dstNic));
        struct.setSrcTunnelIp(srcTunnalIp);
        struct.setDstTunnelIp(dstTunnalIp);
        struct.setInternalId(inv.getInternalId());

        return struct;
    }

    @MessageSafe
    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private class RunInSessionQueue {
        private String name;
        private List<AsyncBackup> asyncBackups = new ArrayList<>();

        public RunInSessionQueue name(String v) {
            name = v;
            return this;
        }

        public RunInSessionQueue asyncBackup(AsyncBackup v) {
            asyncBackups.add(v);
            return this;
        }

        public void run(Consumer<SyncTaskChain> consumer) {
            DebugUtils.Assert(name != null, "name() must be called");
            DebugUtils.Assert(!asyncBackups.isEmpty(), "asyncBackup must be called");

            AsyncBackup one = asyncBackups.get(0);
            AsyncBackup[] rest = asyncBackups.size() > 1 ?
                    asyncBackups.subList(1, asyncBackups.size()).toArray(new AsyncBackup[asyncBackups.size()-1]) :
                    new AsyncBackup[0];

            thdf.chainSubmit(new ChainTask(one, rest) {
                @Override
                public String getSyncSignature() {
                    return name;
                }

                @Override
                public void run(SyncTaskChain chain) {
                    consumer.accept(chain);
                }

                @Override
                protected int getSyncLevel() {
                    return 1;
                }

                @Override
                public String getName() {
                    return name;
                }
            });
        }
    }

    private RunInSessionQueue inQueue() {
        return new RunInSessionQueue();
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof ApplyPortMirrorSessionMsg) {
            handle((ApplyPortMirrorSessionMsg) msg);
        } else if (msg instanceof ReleasePortMirrorSessionMsg) {
            handle((ReleasePortMirrorSessionMsg) msg);
        } else if (msg instanceof DeletePortMirrorSessionMsg) {
            handle((DeletePortMirrorSessionMsg) msg);
        } else if (msg instanceof DeletePortMirrorMsg) {
            handle((DeletePortMirrorMsg) msg);
        } else if (msg instanceof AllocateMirrorNetworkIpMsg) {
            handle((AllocateMirrorNetworkIpMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APICreatePortMirrorMsg) {
            handle((APICreatePortMirrorMsg) msg);
        } else if (msg instanceof APICreatePortMirrorSessionMsg) {
            handle((APICreatePortMirrorSessionMsg) msg);
        } else if (msg instanceof APIDeletePortMirrorMsg) {
            handle((APIDeletePortMirrorMsg) msg);
        } else if (msg instanceof APIDeletePortMirrorSessionMsg) {
            handle((APIDeletePortMirrorSessionMsg) msg);
        } else if (msg instanceof APIChangePortMirrorStateMsg) {
            handle((APIChangePortMirrorStateMsg) msg);
        } else if (msg instanceof APIGetCandidateVmNicsForPortMirrorMsg) {
            handle((APIGetCandidateVmNicsForPortMirrorMsg) msg);
        } else if (msg instanceof APIUpdatePortMirrorMsg) {
            handle((APIUpdatePortMirrorMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(DeletePortMirrorMsg msg) {
        DeletePortMirrorReply reply = new DeletePortMirrorReply();
        PortMirrorVO mirror = dbf.findByUuid(msg.getUuid(), PortMirrorVO.class);
        List<String> usedIpUuids = Q.New(MirrorNetworkUsedIpVO.class).select(MirrorNetworkUsedIpVO_.uuid)
                                    .eq(MirrorNetworkUsedIpVO_.l3NetworkUuid, mirror.getMirrorNetworkUuid()).listValues();
        if (usedIpUuids.isEmpty()) {
            dbf.remove(mirror);
            bus.reply(msg, reply);
            return;
        }

        ErrorCodeList errorCodes = new ErrorCodeList();

        new While<>(usedIpUuids).all((uuid, whileCompletion) -> {
            ReturnIpMsg rmsg = new ReturnIpMsg();
            rmsg.setL3NetworkUuid(mirror.getMirrorNetworkUuid());
            rmsg.setUsedIpUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(rmsg, L3NetworkConstant.SERVICE_ID, mirror.getMirrorNetworkUuid());
            bus.send(rmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        dbf.removeByPrimaryKey(uuid, MirrorNetworkUsedIpVO.class);
                    } else {
                        errorCodes.getCauses().add(reply.getError());
                    }
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodes.getCauses().isEmpty()) {
                    logger.error(String.format("failed to free mirror usedIp: %d", errorCodes.getCauses().size()));
                    reply.setError(errorCodes.getCauses().get(0));
                } else {
                    dbf.remove(mirror);
                }
                bus.reply(msg, reply);
            }
        });
    }

    private void directApplyPortMirrorSession(ApplyPortMirrorSessionMsg msg, final NoErrorCompletion completion) {
        RefreshPortMirrorSessionReply reply = new RefreshPortMirrorSessionReply();
        String srcHostUuid = msg.getSrcHostUuid();
        String dstHostUuid = msg.getDstHostUuid();
        PortMirrorSessionVO sessionVO = dbf.findByUuid(msg.getSessionUuid(), PortMirrorSessionVO.class);
        if (sessionVO == null) {
            bus.reply(msg, reply);
            completion.done();
            return;
        }
        VmNicVO srcNic = dbf.findByUuid(sessionVO.getSrcEndPoint(), VmNicVO.class);
        VmNicVO dstNic = dbf.findByUuid(sessionVO.getDstEndPoint(), VmNicVO.class);
        if (srcHostUuid == null) {
            VmInstanceVO srcVm = Q.New(VmInstanceVO.class)
                                  .eq(VmInstanceVO_.uuid, srcNic.getVmInstanceUuid()).find();
            srcHostUuid = srcVm.getHostUuid();
        }
        if (dstHostUuid == null) {
            VmInstanceVO dstVm = Q.New(VmInstanceVO.class)
                                  .eq(VmInstanceVO_.uuid, dstNic.getVmInstanceUuid()).find();
            dstHostUuid = dstVm.getHostUuid();
        }
        /*the vm status maybe not running*/
        if (srcHostUuid == null || dstHostUuid == null) {
            sessionVO.setStatus(SessionStatus.Inactive);
            dbf.update(sessionVO);
            bus.reply(msg, reply);
            completion.done();
            return;
        }
        if (srcNic.getState().equals(VmNicState.disable) || dstNic.getState().equals(VmNicState.disable)) {
            sessionVO.setStatus(SessionStatus.Inactive);
            dbf.update(sessionVO);
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        startPortMirrorSession(sessionVO.toInventory(), new Completion(msg) {
            @Override
            public void success() {
                logger.debug(String.format("successfully applied portMirror session[%s] to hypervisor", sessionVO.getUuid()));
                sessionVO.setStatus(SessionStatus.Active);
                dbf.update(sessionVO);
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.error(String.format("failed to applied portMirror session[%s] to hypervisor", sessionVO.getUuid()));
                sessionVO.setStatus(SessionStatus.Inactive);
                dbf.update(sessionVO);
                reply.setError(errorCode);
                bus.reply(msg, reply);
                completion.done();
            }
        });
    }

    private void directDeletePortMirrorSession(DeletePortMirrorSessionMsg msg, final NoErrorCompletion completion) {
        RefreshPortMirrorSessionReply reply = new RefreshPortMirrorSessionReply();
        PortMirrorSessionMirrorNetworkRefVO refVO = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).eq(PortMirrorSessionMirrorNetworkRefVO_.sessionUuid, msg.getUuid()).find();
        PortMirrorSessionVO sessionVO = dbf.findByUuid(msg.getUuid(), PortMirrorSessionVO.class);
        if (sessionVO == null) {
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        if (refVO == null) {
            // port mirror service has been stopped
            dbf.remove(sessionVO);
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        stopPortMirrorSession(sessionVO.toInventory(), new Completion(msg) {
            @Override
            public void success() {
                List<PortMirrorSessionMirrorNetworkRefVO> sessionRefs = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).eq(PortMirrorSessionMirrorNetworkRefVO_.sessionUuid, msg.getUuid()).list();
                if ( !sessionRefs.isEmpty()) {
                    dbf.removeCollection(sessionRefs, PortMirrorSessionMirrorNetworkRefVO.class);
                }

                dbf.remove(sessionVO);
                logger.debug(String.format("remove port mirror sessions[uuid:%s] success", msg.getUuid()));
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(operr("failed to delete portMirror session[%s] from hypervisor, detail: %s", sessionVO.getUuid(), errorCode.getDetails()));
                bus.reply(msg, reply);
                completion.done();
            }
        });
    }

    private void directReleasePortMirrorSession(ReleasePortMirrorSessionMsg msg, final NoErrorCompletion completion) {
        RefreshPortMirrorSessionReply reply = new RefreshPortMirrorSessionReply();
        PortMirrorSessionMirrorNetworkRefVO refVO = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).eq(PortMirrorSessionMirrorNetworkRefVO_.sessionUuid, msg.getSessionUuid()).find();
        if (refVO == null) {
            bus.reply(msg, reply);
            completion.done();
            return;
        }
        PortMirrorSessionVO sessionVO = dbf.findByUuid(msg.getSessionUuid(), PortMirrorSessionVO.class);
        if (sessionVO == null) {
            bus.reply(msg, reply);
            completion.done();
            return;
        }
        stopPortMirrorSession(sessionVO.toInventory(), new Completion(msg) {
            @Override
            public void success() {
                logger.debug(String.format("successfully release portMirror session[%s] to hypervisor", sessionVO.getUuid()));
                sessionVO.setStatus(SessionStatus.Inactive);
                dbf.update(sessionVO);
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(operr("failed to release portMirror session[%s] from hypervisor, detail: %s", sessionVO.getUuid(), errorCode.getDetails()));
                bus.reply(msg, reply);
                completion.done();
            }
        });
    }

    private void handle(AllocateMirrorNetworkIpMsg msg) {
        inQueue().name(String.format("directly-allocate-ip-from-%s-for-host-%s-msg", msg.getL3NetworkUuid(), msg.getHostUuid()))
                 .asyncBackup(msg)
                 .run(chain -> directAllocateMirrorIp(msg, new NoErrorCompletion(chain) {
                     @Override
                     public void done() {
                         chain.next();
                     }
                 }));
    }

    private void handle(ApplyPortMirrorSessionMsg msg) {
        inQueue().name(String.format("directly-refresh-portSession-%s-msg", msg.getSessionUuid()))
                 .asyncBackup(msg)
                 .run(chain -> directApplyPortMirrorSession(msg, new NoErrorCompletion(chain) {
                     @Override
                     public void done() {
                         chain.next();
                     }
                 }));
    }

    private void handle(DeletePortMirrorSessionMsg msg) {
        inQueue().name(String.format("directly-refresh-portSession-%s-msg", msg.getUuid()))
                 .asyncBackup(msg)
                 .run(chain -> directDeletePortMirrorSession(msg, new NoErrorCompletion(chain) {
                     @Override
                     public void done() {
                         chain.next();
                     }
                 }));
    }

    private void handle(ReleasePortMirrorSessionMsg msg) {
        inQueue().name(String.format("directly-refresh-portSession-%s-msg", msg.getSessionUuid()))
                 .asyncBackup(msg)
                 .run(chain -> directReleasePortMirrorSession(msg, new NoErrorCompletion(chain) {
                     @Override
                     public void done() {
                         chain.next();
                     }
                 }));
    }

    private void handle(APICreatePortMirrorMsg msg) {
        PortMirrorVO vo = new PortMirrorVO();
        if (msg.getResourceUuid() != null) {
            vo.setUuid(msg.getResourceUuid());
        } else {
            vo.setUuid(Platform.getUuid());
        }
        if (msg.getName() != null) {
            vo.setName(msg.getName());
        }
        if (msg.getDescription() != null) {
            vo.setDescription(msg.getDescription());
        }
        if ("disable".equals(msg.getStateEvent()) ) {
            vo.setState(PortMirrorState.Disabled);
        } else {
            vo.setState(PortMirrorState.Enabled);
        }
        vo.setAccountUuid(msg.getSession().getAccountUuid());
        vo.setMirrorNetworkUuid(msg.getMirrorNetworkUuid());
        vo = dbf.persistAndRefresh(vo);
        APICreatePortMirrorEvent evt = new APICreatePortMirrorEvent(msg.getId());
        evt.setInventory(vo.toInventory());
        logger.debug(String.format("successfully created port mirror[uuid:%s, name:%s]", vo.getUuid(), vo.getName()));
        bus.publish(evt);
    }

    private void handle(APIUpdatePortMirrorMsg msg) {
        boolean update = false;
        PortMirrorVO vo = dbf.findByUuid(msg.getUuid(), PortMirrorVO.class);
        if (msg.getName() != null) {
            vo.setName(msg.getName());
            update = true;
        }
        if ( msg.getDescription() != null) {
            vo.setDescription(msg.getDescription());
            update = true;
        }

        if (update) {
            vo = dbf.updateAndRefresh(vo);
        }
        APIUpdatePortMirrorEvent evt = new APIUpdatePortMirrorEvent(msg.getId());
        evt.setInventory(vo.toInventory());
        logger.debug(String.format("successfully updated port mirror[uuid:%s, name:%s]", vo.getUuid(), vo.getName()));
        bus.publish(evt);
    }

    private void handle(APICreatePortMirrorSessionMsg msg) {
        APICreatePortMirrorSessionEvent evt = new APICreatePortMirrorSessionEvent(msg.getId());
        PortMirrorSessionVO vo = new PortMirrorSessionVO();
        if (msg.getResourceUuid() != null) {
            vo.setUuid(msg.getResourceUuid());
        } else {
            vo.setUuid(Platform.getUuid());
        }
        vo.setName(msg.getName());
        if (msg.getDescription() != null) {
            vo.setDescription(msg.getDescription());
        }
        vo.setSrcEndPoint(msg.getSrcEndPoint());
        vo.setDstEndPoint(msg.getDstEndPoint());
        vo.setType(SessionType.valueOf(msg.getType()));
        vo.setPortMirrorUuid(msg.getPortMirrorUuid());
        vo.setAccountUuid(msg.getSession().getAccountUuid());
        vo.setInternalId(dbf.generateSequenceNumber(PortMirrorSessionSequenceNumberVO.class));
        if (vo.getInternalId() > PortMirrorConstant.PORT_MIRROR_MAX_SESSION_SUPPORT_PER_HOST) {
            throw new OperationFailureException(operr("cannot find internal id of the session[uuid:%s], are there too many sessions in a host???", vo.getUuid()));

        }
        VmNicVO srcNic = dbf.findByUuid(msg.getSrcEndPoint(), VmNicVO.class);
        VmNicVO dstNic = dbf.findByUuid(msg.getDstEndPoint(), VmNicVO.class);
        VmInstanceVO srcVm = dbf.findByUuid(srcNic.getVmInstanceUuid(), VmInstanceVO.class);
        VmInstanceVO dstVm = dbf.findByUuid(dstNic.getVmInstanceUuid(), VmInstanceVO.class);
        PortMirrorState state = Q.New(PortMirrorVO.class).select(PortMirrorVO_.state).eq(PortMirrorVO_.uuid, msg.getPortMirrorUuid()).findValue();
        if (!PortMirrorConstant.applyableVmStates.contains(srcVm.getState()) ||
                !PortMirrorConstant.applyableVmStates.contains(dstVm.getState()) ||
                !VmNicState.enable.equals(srcNic.getState()) ||
                !VmNicState.enable.equals(dstNic.getState()) || PortMirrorState.Disabled.equals(state) ) {
            vo.setStatus(SessionStatus.Inactive);
            vo = dbf.persistAndRefresh(vo);
            evt.setInventory(vo.toInventory());
            bus.publish(evt);
            return;
        }

        final PortMirrorSessionVO fvo = dbf.persistAndRefresh(vo);
        applyPortMirrorSessions(Arrays.asList(fvo), new Completion(msg) {
            @Override
            public void success() {
                PortMirrorSessionVO session = fvo;
                session.setStatus(SessionStatus.Active);
                session = dbf.updateAndRefresh(session);
                evt.setInventory(session.toInventory());
                bus.publish(evt);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                dbf.removeByPrimaryKey(fvo.getUuid(), PortMirrorSessionVO.class);
                evt.setError(errorCode);
                bus.publish(evt);
            }
        });

    }

    private void handle(APIDeletePortMirrorMsg msg) {
        APIDeletePortMirrorEvent evt = new APIDeletePortMirrorEvent(msg.getId());
        final String issuer = PortMirrorVO.class.getSimpleName();
        PortMirrorVO vo = dbf.findByUuid(msg.getUuid(), PortMirrorVO.class);
        final List<PortMirrorInventory> ctx = Arrays.asList(PortMirrorInventory.valueOf(vo));

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-port-mirror-service-%s", msg.getUuid()));
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }

        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(evt);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                evt.setError(err(SysErrors.DELETE_RESOURCE_ERROR, errCode, errCode.getDetails()));
                bus.publish(evt);
            }
        }).start();
    }

    private void handle(APIDeletePortMirrorSessionMsg msg) {
        APIDeletePortMirrorSessionEvent evt = new APIDeletePortMirrorSessionEvent(msg.getId());
        PortMirrorSessionVO vo = dbf.findByUuid(msg.getUuid(), PortMirrorSessionVO.class);

        VmNicVO srcNic = dbf.findByUuid(vo.getSrcEndPoint(), VmNicVO.class);
        VmNicVO dstNic = dbf.findByUuid(vo.getDstEndPoint(), VmNicVO.class);
        VmInstanceVO srcVm = dbf.findByUuid(srcNic.getVmInstanceUuid(), VmInstanceVO.class);
        VmInstanceVO dstVm = dbf.findByUuid(dstNic.getVmInstanceUuid(), VmInstanceVO.class);

        if (!PortMirrorConstant.applyableVmStates.contains(srcVm.getState()) &&
                !PortMirrorConstant.applyableVmStates.contains(dstVm.getState()) ) {
            PortMirrorSessionMirrorNetworkRefVO sessionRef = Q.New(PortMirrorSessionMirrorNetworkRefVO.class).eq(PortMirrorSessionMirrorNetworkRefVO_.sessionUuid, msg.getUuid()).find();
            if ( sessionRef != null) {
                dbf.remove(sessionRef);
            }

            dbf.remove(vo);
            bus.publish(evt);
            return;
        }

        releasePortMirrorSessions(Arrays.asList(vo), true, new Completion(msg) {
            @Override
            public void success() {
                bus.publish(evt);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                evt.setError(errorCode);
                bus.publish(evt);
            }
        });

    }

    private void handle(APIChangePortMirrorStateMsg msg) {
        PortMirrorState sevent = "enable".equals(msg.getStateEvent())?PortMirrorState.Enabled:PortMirrorState.Disabled;
        PortMirrorVO vo = dbf.findByUuid(msg.getUuid(), PortMirrorVO.class);
        APIChangePortMirrorStateEvent evt = new APIChangePortMirrorStateEvent(msg.getId());

        if (sevent.equals(vo.getState())) {
            evt.setInventory(vo.toInventory());
            bus.publish(evt);
            return;
        }

        List<PortMirrorSessionVO> sessions = null;
        if (PortMirrorState.Enabled.equals(sevent)) {
            /*get the sessions of this port mirror and remove the sessions whose endpoints not active*/
            List<String> endpoints = Q.New(PortMirrorSessionVO.class).select(PortMirrorSessionVO_.srcEndPoint)
                    .eq(PortMirrorSessionVO_.portMirrorUuid, vo.getUuid()).listValues();
            if (!endpoints.isEmpty()) {
                endpoints.addAll(Q.New(PortMirrorSessionVO.class).select(PortMirrorSessionVO_.dstEndPoint)
                                  .eq(PortMirrorSessionVO_.portMirrorUuid, vo.getUuid()).listValues());
                List<String> inactiveNics = SQL.New("select distinct nic.uuid from VmNicVO nic, VmInstanceVO vm where nic.uuid in (:nics) and nic.vmInstanceUuid = vm.uuid and vm.state != (:state)")
                                               .param("nics", endpoints).param("state", VmInstanceState.Running).list();
                endpoints.removeAll(inactiveNics);
            }

            if (!endpoints.isEmpty()) {
                sessions = SQL.New("select s from PortMirrorSessionVO s where s.srcEndPoint in (:snics) and s.dstEndPoint in (:dnics)")
                              .param("snics", endpoints).param("dnics", endpoints).list();
            }
        }
        else {
            sessions = Q.New(PortMirrorSessionVO.class)
              .eq(PortMirrorSessionVO_.portMirrorUuid, vo.getUuid()).eq(PortMirrorSessionVO_.status, SessionStatus.Active).list();
        }

        if (sessions == null || sessions.isEmpty()) {
            vo.setState(sevent);
            vo = dbf.updateAndRefresh(vo);
            evt.setInventory(vo.toInventory());
            bus.publish(evt);
            return;
        }

        PortMirrorVO fvo = vo;
        if (PortMirrorState.Enabled.equals(sevent)) {
            applyPortMirrorSessions(sessions, new Completion(msg) {
                @Override
                public void success() {
                    fvo.setState(sevent);
                    evt.setInventory(dbf.updateAndRefresh(fvo).toInventory());
                    bus.publish(evt);
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    evt.setError(errorCode);
                    bus.publish(evt);
                }
            });
        } else {
            releasePortMirrorSessions(sessions, false, new Completion(evt) {
                @Override
                public void success() {
                    fvo.setState(sevent);
                    evt.setInventory(dbf.updateAndRefresh(fvo).toInventory());
                    bus.publish(evt);
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    evt.setError(errorCode);
                    bus.publish(evt);
                }
            });
        }
    }

    private List<VmNicInventory> getCandidateSrcVmNics(String portMirrorUuid) {
        /*
        all the kvm & user-vm vnics that are located in clusters attached with mirror network except for :
        1. all the src endpoints in portMirror Session
        2. all the dst endpoints in portMirror Session
         */
        PortMirrorVO vo = dbf.findByUuid(portMirrorUuid, PortMirrorVO.class);
        String l2NetworkUuid = Q.New(L3NetworkVO.class).select(L3NetworkVO_.l2NetworkUuid).eq(L3NetworkVO_.uuid, vo.getMirrorNetworkUuid()).findValue();
        List<String> clusterUuids = Q.New(L2NetworkClusterRefVO.class).select(L2NetworkClusterRefVO_.clusterUuid).eq(L2NetworkClusterRefVO_.l2NetworkUuid, l2NetworkUuid).listValues();
        List<Tuple> nicUuidsToExcluedTuple = Q.New(PortMirrorSessionVO.class).select(PortMirrorSessionVO_.srcEndPoint, PortMirrorSessionVO_.dstEndPoint)
                                         .listTuple();
        if (!nicUuidsToExcluedTuple.isEmpty()) {
            List<String> nicUuidsToExclued = nicUuidsToExcluedTuple.stream().map(t -> (String) t.get(0)).collect(Collectors.toList());
            nicUuidsToExclued.addAll(nicUuidsToExcluedTuple.stream().map(t -> (String) t.get(1)).collect(Collectors.toList()));

            List<VmNicVO> vnics = SQL.New("select nic from VmNicVO nic, VmInstanceVO vm where vm.uuid = nic.vmInstanceUuid " +
                        " and vm.clusterUuid in :clusterUuids and nic.hypervisorType = :hyType and vm.type = :vmType and nic.uuid not in :exclued")
                                         .param("clusterUuids", clusterUuids).param("hyType", KVMConstant.KVM_HYPERVISOR_TYPE)
                                         .param("vmType", VmInstanceConstant.USER_VM_TYPE).param("exclued", nicUuidsToExclued).list();

            return VmNicInventory.valueOf(vnics);
        } else {
            List<VmNicVO> vnics = SQL.New("select nic from VmNicVO nic, VmInstanceVO vm where vm.uuid = nic.vmInstanceUuid " +
                    " and vm.clusterUuid in :clusterUuids and nic.hypervisorType = :hyType and vm.type = :vmType")
                                     .param("clusterUuids", clusterUuids).param("hyType", KVMConstant.KVM_HYPERVISOR_TYPE)
                                     .param("vmType", VmInstanceConstant.USER_VM_TYPE).list();

            return VmNicInventory.valueOf(vnics);
        }
    }

    private List<VmNicInventory> getCandidateDstVmNics(String portMirrorUuid) {
        /*
        all the kvm & user-vm vnics that are located in clusters attached with mirror network except for :
        1. all the src endpoints in portMirror Session
        2. all the default nics
         */
        PortMirrorVO vo = dbf.findByUuid(portMirrorUuid, PortMirrorVO.class);
        String l2NetworkUuid = Q.New(L3NetworkVO.class).select(L3NetworkVO_.l2NetworkUuid).eq(L3NetworkVO_.uuid, vo.getMirrorNetworkUuid()).findValue();
        List<String> clusterUuids = Q.New(L2NetworkClusterRefVO.class).select(L2NetworkClusterRefVO_.clusterUuid).eq(L2NetworkClusterRefVO_.l2NetworkUuid, l2NetworkUuid).listValues();
        List<String> nicUuidsToExclued = Q.New(PortMirrorSessionVO.class).select(PortMirrorSessionVO_.srcEndPoint).listValues();
        if (nicUuidsToExclued.isEmpty()) {
            List<VmNicVO> vnics = SQL.New("select nic from VmNicVO nic, VmInstanceVO vm where vm.uuid = nic.vmInstanceUuid " +
                    " and nic.l3NetworkUuid != vm.defaultL3NetworkUuid and vm.clusterUuid in :clusterUuids and nic.hypervisorType = :hyType " +
                    " and vm.type = :vmType")
                                     .param("clusterUuids", clusterUuids).param("hyType", KVMConstant.KVM_HYPERVISOR_TYPE)
                                     .param("vmType", VmInstanceConstant.USER_VM_TYPE).list();
            return VmNicInventory.valueOf(vnics);
        } else {
            List<VmNicVO> vnics = SQL.New("select nic from VmNicVO nic, VmInstanceVO vm where vm.uuid = nic.vmInstanceUuid " +
                    " and nic.l3NetworkUuid != vm.defaultL3NetworkUuid and vm.clusterUuid in :clusterUuids and nic.hypervisorType = :hyType " +
                    " and vm.type = :vmType and nic.uuid not in :exclued")
                                     .param("clusterUuids", clusterUuids).param("hyType", KVMConstant.KVM_HYPERVISOR_TYPE)
                                     .param("exclued", nicUuidsToExclued).param("vmType", VmInstanceConstant.USER_VM_TYPE).list();

            return VmNicInventory.valueOf(vnics);
        }
    }

    private List<VmNicInventory> ingoreNicsWithQos(List<VmNicInventory> nics) {
        if (nics.isEmpty()) {
            return nics;
        }
        List<String> vmUuids = nics.stream().map(nic->nic.getVmInstanceUuid()).collect(Collectors.toList());
        SimpleQuery<SystemTagVO> q = dbf.createQuery(SystemTagVO.class);
        q.add(SystemTagVO_.resourceUuid, SimpleQuery.Op.IN, vmUuids);
        q.add(SystemTagVO_.resourceType, SimpleQuery.Op.EQ, VmInstanceVO.class.getSimpleName());
        List<SystemTagVO> tags = q.list();
        for (SystemTagVO tag : tags) {
            if (MevocoSystemTags.NETWORK_OUTBOUND_BANDWIDTH.isMatch(tag.getTag()) || MevocoSystemTags.NETWORK_INBOUND_BANDWIDTH.isMatch(tag.getTag())) {
                nics = nics.stream().filter(nic -> !nic.getVmInstanceUuid().equals(tag.getResourceUuid())).collect(Collectors.toList());
            }
        }

        if (nics.isEmpty()) {
            return nics;
        }

        List<String> nicUuids = nics.stream().map(nic->nic.getUuid()).collect(Collectors.toList());
        q = dbf.createQuery(SystemTagVO.class);
        q.add(SystemTagVO_.resourceUuid, SimpleQuery.Op.IN, nicUuids);
        q.add(SystemTagVO_.resourceType, SimpleQuery.Op.EQ, InstanceOfferingVO.class.getSimpleName());
        tags = q.list();
        for (SystemTagVO tag : tags) {
            if (MevocoSystemTags.NETWORK_OUTBOUND_BANDWIDTH.isMatch(tag.getTag()) || MevocoSystemTags.NETWORK_INBOUND_BANDWIDTH.isMatch(tag.getTag())) {
                nics = nics.stream().filter(nic -> !nic.getUuid().equals(tag.getResourceUuid())).collect(Collectors.toList());
            }
        }

        return nics;
    }

    private void handle(APIGetCandidateVmNicsForPortMirrorMsg msg) {
        APIGetCandidateVmNicsForPortMirrorReply reply = new APIGetCandidateVmNicsForPortMirrorReply();
        if (PortMirrorConstant.EndPointType.source.toString().equals(msg.getType())) {
            reply.setInventories(ingoreNicsWithQos(getCandidateSrcVmNics(msg.getPortMirrorUuid())));
        } else {
            reply.setInventories(ingoreNicsWithQos(getCandidateDstVmNics(msg.getPortMirrorUuid())));
        }

        bus.reply(msg, reply);

    }


    @Override
    public String getId() {
        return bus.makeLocalServiceId(PortMirrorConstant.SERVICE_ID);
    }

    @Override
    public boolean start() {
        PortMirrorGlobalConfig.SESSION_HEALTH_CHECK_INTERVAL.installUpdateExtension((oldConfig, newConfig) -> startApplyHealthySessionTask());

        populateExtensions();
        installInstanceStateEventListener();
        startApplyHealthySessionTask();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    private void handlePortMirrorSessionTracker() {
        /*
        * search all the inactive sessions that portmirror's state enabled
        * exclude the sessions whose srcEndpoint and dstEndpoint's vm with status not running
        * */
        List<PortMirrorSessionVO> sessions = SQL.New("select s from PortMirrorSessionVO s, PortMirrorVO p where p.uuid = s.portMirrorUuid and p.state = (:state) and s.status = (:sessionStatus)")
                                                .param("sessionStatus", SessionStatus.Inactive).param("state", PortMirrorState.Enabled).list();

        if (sessions == null || sessions.isEmpty()) {
            return;
        }

        List<PortMirrorSessionVO> sessionVos = new Callable<List<PortMirrorSessionVO>>() {
            @Override
            @Transactional(readOnly = true)
            public List<PortMirrorSessionVO> call() {
                List<PortMirrorSessionVO> sessions = SQL.New("select distinct s from PortMirrorSessionVO s, PortMirrorVO p where p.uuid = s.portMirrorUuid and p.state = (:state) and s.status = (:sessionStatus)")
                                                        .param("sessionStatus", SessionStatus.Inactive).param("state", PortMirrorState.Enabled).list();

                if (sessions == null || sessions.isEmpty()) {
                    return null;
                }

                List<PortMirrorSessionVO> excludeSessions = SQL.New("select distinct session from PortMirrorSessionVO session, VmNicVO nic, VmInstanceVO vm where " +
                        " session.uuid in (:sUuids) and (session.srcEndPoint = nic.uuid or session.dstEndPoint = nic.uuid) " +
                        " and nic.vmInstanceUuid = vm.uuid and vm.state != (:vmState)")
                                                        .param("sUuids", sessions.stream().map(PortMirrorSessionVO::getUuid).collect(Collectors.toList())).param("vmState", VmInstanceState.Running).list();
                if (!sessions.isEmpty()) {
                    sessions.removeAll(excludeSessions);
                }
                return sessions;
            }
        }.call();

        if (sessionVos == null || sessionVos.isEmpty()) {
            return;
        }

        applyPortMirrorSessions(sessionVos, new Completion(null) {
            @Override
            public void success() {
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.warn(String.format("fail to apply the sessions in the portmirror tracker:%s", errorCode.toString()));
            }
        });
    }


    private void vmStateChanged(VmInstanceInventory vm, VmInstanceState oldState, VmInstanceState newState) {
        List<String> vNicUuids = vm.getVmNics().stream().map(VmNicInventory::getUuid).collect(Collectors.toList());
        if (vNicUuids == null || vNicUuids.isEmpty()) {
            return;
        }

        List<PortMirrorSessionVO> sessionVos = SQL.New("select s from PortMirrorSessionVO s where s.srcEndPoint in (:srcNics) or s.dstEndPoint in (:dstNics)")
                                                  .param("srcNics", vNicUuids).param("dstNics", vNicUuids).list();

        if (sessionVos == null || sessionVos.isEmpty()) {
            return;
        }

        if ( (oldState == VmInstanceState.Unknown || oldState == VmInstanceState.Migrating || oldState == VmInstanceState.Rebooting || oldState == VmInstanceState.Starting) && newState == VmInstanceState.Running) {
            applyPortMirrorSessions(sessionVos, new Completion(null) {
                @Override
                public void success() {

                }

                @Override
                public void fail(ErrorCode errorCode) {
                    Logger.logError(errorCode.toString());
                }
            });
        }

        if ( oldState == VmInstanceState.Running && (newState == VmInstanceState.Unknown || newState == VmInstanceState.Stopped)) {
            for (PortMirrorSessionVO session : sessionVos) {
                session.setStatus(SessionStatus.Inactive);
            }
            dbf.updateCollection(sessionVos);
        }
    }


    private synchronized void startApplyHealthySessionTask() {
        if (portMirrorSessionTracker != null) {
            portMirrorSessionTracker.cancel(true);
        }

        portMirrorSessionTracker = thdf.submitPeriodicTask(new PeriodicTask() {
            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.SECONDS;
            }

            @Override
            public long getInterval() {
                return PortMirrorGlobalConfig.SESSION_HEALTH_CHECK_INTERVAL.value(Long.class);
            }

            @Override
            public String getName() {
                return "port-mirror-session-tracker-task";
            }

            @Override
            public void run() {
                handlePortMirrorSessionTracker();
            }
        });
    }

    private void installInstanceStateEventListener() {
        evf.on(VmCanonicalEvents.VM_FULL_STATE_CHANGED_PATH, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                VmCanonicalEvents.VmStateChangedData d = (VmCanonicalEvents.VmStateChangedData) data;
                if (KVMConstant.KVM_HYPERVISOR_TYPE.equals(d.getInventory().getHypervisorType())) {
                    vmStateChanged(d.getInventory(), VmInstanceState.valueOf(d.getOldState()), VmInstanceState.valueOf(d.getNewState()));
                }
            }
        });
    }

    private void populateExtensions() {
        for (PortMirrorBackend ext : pluginRgty.getExtensionList(PortMirrorBackend.class)) {
            PortMirrorBackend old = backends.get(ext.getHypervisorType());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate PortMirrorBackend[%s,%s] for type[%s]", old.getClass().getName(),
                        ext.getClass().getName(), ext.getHypervisorType()));
            }
            backends.put(ext.getHypervisorType(), ext);
        }
    }

    private void applyPortMirrorSessions(List<PortMirrorSessionVO> sessionVOs, Completion completion) {
        List<PortMirrorSessionInventory> invs = PortMirrorSessionInventory.valueOf(sessionVOs);
        ErrorCodeList errorCodes = new ErrorCodeList();
        new While<>(invs).all((inv, whileCompletion) -> {
            List<String> vmUuids = Q.New(VmNicVO.class).select(VmNicVO_.vmInstanceUuid)
                                    .in(VmNicVO_.uuid, Arrays.asList(inv.getSrcEndPoint(), inv.getDstEndPoint())).listValues();
            ApplyPortMirrorSessionMsg rmsg = new ApplyPortMirrorSessionMsg();
            rmsg.setSessionUuid(inv.getUuid());
            bus.makeTargetServiceIdByResourceUuid(rmsg, PortMirrorConstant.SERVICE_ID, inv.getUuid());
            NeedReplyMessage cmsg = rmsg;
            if ( !vmUuids.isEmpty()) {
                vmUuids = vmUuids.stream().distinct().sorted().collect(Collectors.toList());
                for (String vmUuid : vmUuids) {
                    RefreshPortMirrorSessionOverlapMsg overlapMsg = new RefreshPortMirrorSessionOverlapMsg();
                    overlapMsg.setVmInstanceUuid(vmUuid);
                    overlapMsg.setMessage(cmsg);
                    bus.makeTargetServiceIdByResourceUuid(overlapMsg, VmInstanceConstant.SERVICE_ID, vmUuid);
                    cmsg = overlapMsg;
                }
            }
            bus.send(cmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(reply.getError().toString());
                        errorCodes.getCauses().add(reply.getError());
                        whileCompletion.done();
                    } else {
                        logger.debug(String.format("apply port mirror session[uuid:%s] success", rmsg.getSessionUuid()));
                        whileCompletion.done();
                    }
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodes.getCauses().isEmpty()) {
                    logger.error(String.format("failed to applied portMirror Sessions: %d", errorCodes.getCauses().size()));
                    completion.fail(errorCodes.getCauses().get(0));
                } else {
                    completion.success();
                }
            }
        });
    }

    private void releasePortMirrorSessions(List<PortMirrorSessionVO> sessionVOs, Boolean needDelete, Completion completion) {
        List<PortMirrorSessionInventory> invs = PortMirrorSessionInventory.valueOf(sessionVOs);
        ErrorCodeList errorCodes = new ErrorCodeList();
        new While<>(invs).all((inv, whileCompletion) -> {
            List<String> vmUuids = Q.New(VmNicVO.class).select(VmNicVO_.vmInstanceUuid)
                                    .in(VmNicVO_.uuid, Arrays.asList(inv.getSrcEndPoint(), inv.getDstEndPoint())).listValues();
            NeedReplyMessage cmsg;
            if (needDelete) {
                DeletePortMirrorSessionMsg rmsg = new DeletePortMirrorSessionMsg();
                rmsg.setUuid(inv.getUuid());
                cmsg = rmsg;
            } else {
                ReleasePortMirrorSessionMsg rmsg = new ReleasePortMirrorSessionMsg();
                rmsg.setSessionUuid(inv.getUuid());
                cmsg = rmsg;
            }

            bus.makeTargetServiceIdByResourceUuid(cmsg, PortMirrorConstant.SERVICE_ID, inv.getUuid());

            if ( !vmUuids.isEmpty()) {
                vmUuids = vmUuids.stream().distinct().sorted().collect(Collectors.toList());
                for (String vmUuid : vmUuids) {
                    RefreshPortMirrorSessionOverlapMsg overlapMsg = new RefreshPortMirrorSessionOverlapMsg();
                    overlapMsg.setVmInstanceUuid(vmUuid);
                    overlapMsg.setMessage(cmsg);
                    bus.makeTargetServiceIdByResourceUuid(overlapMsg, VmInstanceConstant.SERVICE_ID, vmUuid);
                    cmsg = overlapMsg;
                }
            }
            bus.send(cmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(reply.getError().toString());
                        errorCodes.getCauses().add(reply.getError());
                        whileCompletion.done();
                    } else {
                        logger.debug(String.format("release port mirror session[uuid:%s] success", inv.getUuid()));
                        whileCompletion.done();
                    }
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodes.getCauses().isEmpty()) {
                    logger.error(String.format("failed to release portMirror Sessions: %d", errorCodes.getCauses().size()));
                    completion.fail(errorCodes.getCauses().get(0));
                } else {
                    completion.success();
                }
            }
        });
    }

    private void applyPortMirrorByEndpoints(List<String> endpoints, Completion completion) {
        List<PortMirrorSessionVO> sessionVOs = SQL.New("select s from PortMirrorSessionVO s, PortMirrorVO p where p.uuid = s.portMirrorUuid and p.state = (:state) and (s.srcEndPoint in (:srcNics) or s.dstEndPoint in (:dstNics))")
                .param("srcNics", endpoints).param("dstNics", endpoints).param("state", PortMirrorState.Enabled).list();

        if (sessionVOs == null || sessionVOs.isEmpty()) {
            completion.success();
            return;
        }

        applyPortMirrorSessions(sessionVOs, completion);
    }

    private void releasePortMirrorByEndpoints(List<String> endpoints, Completion completion) {
        List<PortMirrorSessionVO> sessionVOs = SQL.New("select s from PortMirrorSessionVO s, PortMirrorVO p where p.uuid = s.portMirrorUuid and p.state = (:state) and (s.srcEndPoint in (:srcNics) or s.dstEndPoint in (:dstNics))")
                                                .param("srcNics", endpoints).param("dstNics", endpoints).param("state", PortMirrorState.Enabled).list();

        if (sessionVOs == null || sessionVOs.isEmpty()) {
            completion.success();
            return;
        }

        releasePortMirrorSessions(sessionVOs, false, completion);
    }

    @Override
    public void preDetachNic(VmNicInventory nic) {

    }

    @Override
    public void beforeDetachNic(VmNicInventory nic) {
        List<PortMirrorSessionVO> sessions = SQL.New("select distinct s from PortMirrorSessionVO s, PortMirrorVO p where p.uuid = s.portMirrorUuid and (s.srcEndPoint = (:vmnic) or s.dstEndPoint = (:vmnic))")
                                                .param("vmnic", nic.getUuid()).list();
        if (sessions.isEmpty()) {
            return;
        }
        releasePortMirrorSessions(sessions, true, new Completion(null) {
            @Override
            public void success() {
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.debug(String.format(" failed to delete port mirror sessions during detach nic, %s", errorCode.getDetails()));
            }
        });
    }

    @Override
    public void afterDetachNic(VmNicInventory nic) {

    }

    @Override
    public void failedToDetachNic(VmNicInventory nic, ErrorCode error) {

    }

    @Override
    public void afterChangeVmNicState(String vmNic, String state) {
        if(state.equals(VmNicState.disable.toString())) {
            releasePortMirrorByEndpoints(list(vmNic), new Completion(null) {
                @Override
                public void success() {
                    logger.debug(String.format(" success to release port mirror sessions during update vmNic:%s state", vmNic));
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    logger.debug(String.format(" failed to release port mirror sessions during update vmNic:%s state", vmNic));
                }
            });
        } else {
            applyPortMirrorByEndpoints(list(vmNic), new Completion(null) {
                @Override
                public void success() {
                    logger.debug(String.format(" success to apply port mirror sessions during update vmNic:%s state", vmNic));
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    logger.debug(String.format(" failed to apply port mirror sessions during update vmNic:%s state", vmNic));
                }
            });
        }
    }

    @Override
    public String preStopVm(VmInstanceInventory inv) {
        return null;
    }

    @Override
    public void beforeStopVm(VmInstanceInventory inv) {
        List<String> vNicUuids = inv.getVmNics().stream().map(VmNicInventory::getUuid).collect(Collectors.toList());
        if (vNicUuids == null || vNicUuids.isEmpty()) {
            return;
        }
        releasePortMirrorByEndpoints(vNicUuids, new Completion(null) {
            @Override
            public void success() {
                logger.debug(String.format(" success to release port mirror sessions during stop vm:%s", inv.getUuid()));
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.debug(String.format(" failed to release port mirror sessions during stop vm:%s", inv.getUuid()));
            }
        });
    }

    @Override
    public void afterStopVm(VmInstanceInventory inv) {

    }

    @Override
    public void failedToStopVm(VmInstanceInventory inv, ErrorCode reason) {
        /*what should it do here*/
    }

    @Override
    public void afterFailedToStopVm(VmInstanceInventory inv, ErrorCode reason) {

    }

    /*maintainate/remove host will trigger the vm status to change that will trigger the portmirror to update*/

    private void createSystemTagForHostSequence(String hostUuid) {
        String sequence = hostUuid.substring(hostUuid.length()-5);
        for (int i = 0; i < 16; i++) {
            String tag = PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE.instantiateTag(
                    map(e(PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE_TOKEN, sequence)));
            SystemTagVO old = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceType, HostVO.class.getSimpleName()).
                    eq(SystemTagVO_.tag, tag).eq(SystemTagVO_.type, TagType.System).find();
            if (old == null) {
                break;
            }
            sequence = hostUuid.substring(i, i+5);
            /*can't find a squence case*/
            if (i == 15) {
                Logger.logError(String.format("can't find the squence for host[%s] in port mirror service", hostUuid));
                return;
            }
        }


        SystemTagCreator creator = PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE.newSystemTagCreator(hostUuid);
        creator.inherent = true;
        creator.setTagByTokens(map(e(
                PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE_TOKEN, sequence)));
        creator.create();
    }

    /*reconnect host*/
    @Override
    public void afterHostConnected(HostInventory host) {
        String sequence = PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE.getTokenByResourceUuid(host.getUuid(), PortMirrorSystemTags.PORT_MIRROR_HOST_SEQUENCE_TOKEN);
        if (sequence == null) {
            createSystemTagForHostSequence(host.getUuid());
        }

        List<String> usedIpUuids = Q.New(MirrorNetworkUsedIpVO.class).select(MirrorNetworkUsedIpVO_.uuid)
                                    .eq(MirrorNetworkUsedIpVO_.hostUuid, host.getUuid()).listValues();
        if (usedIpUuids == null || usedIpUuids.isEmpty()) {
            return;
        }

        List<PortMirrorSessionVO> sessionVos = SQL.New("select distinct s from PortMirrorSessionVO s, PortMirrorVO p, PortMirrorSessionMirrorNetworkRefVO ref where p.uuid = s.portMirrorUuid and p.state = (:state) and ref.sessionUuid = s.uuid and " +
                "(ref.srcTunnelUuid in (:srcTunnel) or ref.dstTunnelUuid in (:dstTunnel))")
                                                .param("srcTunnel", usedIpUuids).param("dstTunnel", usedIpUuids).param("state", PortMirrorState.Enabled).list();
        if (sessionVos == null || sessionVos.isEmpty()) {
            return;
        }

        applyPortMirrorSessions(sessionVos, new Completion(null) {
            @Override
            public void success() {
                logger.debug("apply the sessions after host reconnected successfully");
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.debug(String.format("fail to apply the sessions after host reconnected:%s", errorCode.toString()));
            }
        });
    }

}
