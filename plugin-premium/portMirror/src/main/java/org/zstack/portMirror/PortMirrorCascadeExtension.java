package org.zstack.portMirror;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.portMirror.PortMirrorConstant;
import org.zstack.header.portMirror.PortMirrorInventory;
import org.zstack.header.portMirror.PortMirrorVO;
import org.zstack.header.portMirror.PortMirrorVO_;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-18
 **/
public class PortMirrorCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(PortMirrorCascadeExtension.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;

    private static final String NAME = PortMirrorVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        completion.success();
    }

    private List<PortMirrorInventory> PortMirrorFromAction(CascadeAction action) {
        if (L3NetworkVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<L3NetworkInventory> invs = action.getParentIssuerContext();
            List<PortMirrorVO> mirrors = null;
            if (invs != null && !invs.isEmpty()) {
                List<String> l3Uuids = invs.stream().map(L3NetworkInventory::getUuid).collect(Collectors.toList());
                mirrors = Q.New(PortMirrorVO.class).in(PortMirrorVO_.mirrorNetworkUuid, l3Uuids).list();
            }

            if (mirrors != null && !mirrors.isEmpty()) {
                return PortMirrorInventory.valueOf(mirrors);
            }

        } else if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }
        return null;
    }

    
    private void handleDeletion(final CascadeAction action, final Completion completion) {
        final List<PortMirrorInventory> invs = PortMirrorFromAction(action);
        List<String> uuids = new ArrayList<>();
        if (invs != null && !invs.isEmpty()) {
            for (PortMirrorInventory inv : invs) {
                logger.debug(String.format("delete Port Mirror service[uuid:%s]", inv.getUuid()));
                uuids.add(inv.getUuid());
            }
        }

        if (uuids.isEmpty()) {
            completion.success();
            return;
        }

        ErrorCodeList errorCodes = new ErrorCodeList();
        new While<>(uuids).all((uuid, whileCompletion) -> {
            DeletePortMirrorMsg rmsg = new DeletePortMirrorMsg();
            rmsg.setUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(rmsg, PortMirrorConstant.SERVICE_ID, uuid);
            bus.send(rmsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(reply.getError().toString());
                        errorCodes.getCauses().add(reply.getError());
                    }
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodes.getCauses().isEmpty()) {
                    logger.error(String.format("failed to remove portMirror: %d", errorCodes.getCauses().size()));
                    completion.fail(errorCodes.getCause());
                } else {
                    completion.success();
                }
            }
        });
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(L3NetworkVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<PortMirrorInventory> ctx = PortMirrorFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}