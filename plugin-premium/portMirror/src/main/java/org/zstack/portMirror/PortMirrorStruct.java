package org.zstack.portMirror;

import org.zstack.header.portMirror.MirrorNetworkUsedIpInventory;
import org.zstack.header.portMirror.PortMirrorSessionInventory;
import org.zstack.header.vm.VmNicInventory;

/**
 * @author: zhanyong.miao
 * @date: 2019-09-10
 **/
public class PortMirrorStruct {
    private PortMirrorSessionInventory session;
    private VmNicInventory srcNic;
    private VmNicInventory dstNic;
    private MirrorNetworkUsedIpInventory srcTunnelIp;
    private MirrorNetworkUsedIpInventory dstTunnelIp;
    private Long internalId; /*GRE Key ID*/

    public Long getInternalId() {
        return internalId;
    }

    public void setInternalId(Long internalId) {
        this.internalId = internalId;
    }

    public PortMirrorSessionInventory getSession() {
        return session;
    }

    public void setSession(PortMirrorSessionInventory session) {
        this.session = session;
    }

    public VmNicInventory getSrcNic() {
        return srcNic;
    }

    public void setSrcNic(VmNicInventory srcNic) {
        this.srcNic = srcNic;
    }

    public VmNicInventory getDstNic() {
        return dstNic;
    }

    public void setDstNic(VmNicInventory dstNic) {
        this.dstNic = dstNic;
    }

    public MirrorNetworkUsedIpInventory getSrcTunnelIp() {
        return srcTunnelIp;
    }

    public void setSrcTunnelIp(MirrorNetworkUsedIpInventory srcTunnelIp) {
        this.srcTunnelIp = srcTunnelIp;
    }

    public MirrorNetworkUsedIpInventory getDstTunnelIp() {
        return dstTunnelIp;
    }

    public void setDstTunnelIp(MirrorNetworkUsedIpInventory dstTunnelIp) {
        this.dstTunnelIp = dstTunnelIp;
    }
}
