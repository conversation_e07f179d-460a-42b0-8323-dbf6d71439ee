package org.zstack.portMirror;

import org.zstack.header.message.MessageReply;
import org.zstack.header.portMirror.MirrorNetworkUsedIpInventory;

/**
 * @author: zhanyong.miao
 * @date: 2019-10-21
 **/
public class AllocateMirrorNetworkIpReply extends MessageReply {
    private MirrorNetworkUsedIpInventory ipInventory;

    public MirrorNetworkUsedIpInventory getIpInventory() {
        return ipInventory;
    }

    public void setIpInventory(MirrorNetworkUsedIpInventory ipInventory) {
        this.ipInventory = ipInventory;
    }
}
