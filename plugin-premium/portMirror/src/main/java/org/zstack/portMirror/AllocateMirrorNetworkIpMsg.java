package org.zstack.portMirror;

import org.zstack.header.message.NeedReplyMessage;

/**
 * @author: zhanyong.miao
 * @date: 2019-10-21
 **/
public class AllocateMirrorNetworkIpMsg extends NeedReplyMessage {
    String hostUuid;
    private String l3NetworkUuid;


    public String getHostUuid() {
        return hostUuid;
    }

    public void setHostUuid(String hostUuid) {
        this.hostUuid = hostUuid;
    }

    public String getL3NetworkUuid() {
        return l3NetworkUuid;
    }

    public void setL3NetworkUuid(String l3NetworkUuid) {
        this.l3NetworkUuid = l3NetworkUuid;
    }
}
