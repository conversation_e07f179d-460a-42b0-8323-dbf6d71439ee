package org.zstack.network.ovn;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.http.HttpMethod;
import org.springframework.web.util.UriComponentsBuilder;
import org.zstack.compute.vm.VmInstanceManager;
import org.zstack.compute.vm.VmNicQosConfigBackend;
import org.zstack.compute.vm.VmNicQosStruct;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.db.Q;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.*;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NoErrorCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.host.*;
import org.zstack.header.network.l3.*;
import org.zstack.header.rest.JsonAsyncRESTCallback;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.vm.*;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO_;
import org.zstack.network.l2.L2NetworkSystemTags;
import org.zstack.network.l2.vxlan.vxlanNetwork.L2VxlanNetworkInventory;
import org.zstack.network.l3.IpRangeHelper;
import org.zstack.sdnController.*;
import org.zstack.sdnController.header.*;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6Constants;
import org.zstack.utils.network.IPv6NetworkUtils;
import org.zstack.utils.network.NetworkUtils;

import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;
import static org.zstack.utils.CollectionDSL.*;


@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnController implements SdnController, SdnControllerL2 {
    private static final CLogger logger = Utils.getLogger(OvnController.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    protected RESTFacade restf;
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    protected EventFacade evtf;
    @Autowired
    protected OvsVSwitchBackend ovsBackend;
    @Autowired
    private VmInstanceManager vmMgr;

    private SdnControllerVO self;
    private OvnControllerHelper ovnHelper = new OvnControllerHelper();

    public OvnController(SdnControllerVO self) {
        this.self = self;
    }

    private String getOvnSyncSignature() {
        return new SdnControllerBase(self).getSdnControllerSignature();
    }

    @Override
    public void handleMessage(SdnControllerMessage msg) {
        if (msg instanceof SdnControllerPingMsg) {
            handMessage((SdnControllerPingMsg) msg);
        } else if (msg instanceof OvnControllerAsyncHttpCallMsg) {
            handMessage((OvnControllerAsyncHttpCallMsg) msg);
        } else {
            bus.dealWithUnknownMessage((Message) msg);
        }
    }

    protected String buildUrl(String ovnControllerIP, String path) {
        UriComponentsBuilder ub = UriComponentsBuilder.newInstance();
        ub.scheme(OvnControllerGlobalProperty.AGENT_URL_SCHEME);

        if (CoreGlobalProperty.UNIT_TEST_ON) {
            ub.host("localhost");
            ub.port(8989);
        } else {
            ub.host(ovnControllerIP);
            ub.port(OvnControllerGlobalProperty.AGENT_PORT);
        }

        if (!"".equals(OvnControllerGlobalProperty.AGENT_URL_ROOT_PATH)) {
            ub.path(OvnControllerGlobalProperty.AGENT_URL_ROOT_PATH);
        }
        ub.path(path);

        return ub.build().toUriString();
    }

    protected VSwitchOvsConfigStruct getVSwitchOvsConfigStruct(APISdnControllerAddHostMsg msg) {
        String proto = OvnControllerSystemTags.OVN_NORTHD_PROTO.getTokenByResourceUuid(self.getUuid(),
                OvnControllerSystemTags.OVN_NORTHD_PROTO_TOKEN);
        String port = OvnControllerSystemTags.OVN_NORTHD_PORT.getTokenByResourceUuid(self.getUuid(),
                OvnControllerSystemTags.OVN_NORTHD_PORT_TOKEN);

        Map<String, String> nicNameDriverMap = new HashMap<>();
        Map<String, String> nicNamePciAddressMap = new HashMap<>();
        List<Tuple> nicTuples = Q.New(HostNetworkInterfaceVO.class)
                .eq(HostNetworkInterfaceVO_.hostUuid, msg.getHostUuid())
                .in(HostNetworkInterfaceVO_.interfaceName, msg.getNicNames())
                .select(HostNetworkInterfaceVO_.interfaceName,
                        HostNetworkInterfaceVO_.driverType,
                        HostNetworkInterfaceVO_.pciDeviceAddress)
                .listTuple();
        for (Tuple t : nicTuples) {
            nicNameDriverMap.put(t.get(0, String.class), t.get(1, String.class));
            nicNamePciAddressMap.put(t.get(0, String.class), t.get(2, String.class));
        }

        VSwitchOvsConfigStruct struct = new VSwitchOvsConfigStruct();
        struct.setvSwitchType(msg.getvSwitchType());
        struct.setOvnControllerIp(self.getIp());
        struct.setBrExName(OvnControllerGlobalProperty.OVN_BR_EX_NAME);
        struct.setOvnEncapIP(msg.getVtepIp());
        struct.setOvnEncapType(OvnControllerSystemTags.OVN_ENCAP_TYPE.getTokenByResourceUuid(
                self.getUuid(), OvnControllerSystemTags.OVN_ENCAP_TYPE_TOKEN));
        struct.setOvnRemoteConnection(String.format("%s:%s:%s", proto, self.getIp(), port));
        struct.setNicNamePciMap(nicNamePciAddressMap);
        struct.setNicNameDriverMap(nicNameDriverMap);
        struct.setBondMode(msg.getBondMode());
        struct.setLacpMode(msg.getLacpMode());
        struct.setOvnEncapNetmask(msg.getNetmask());

        return struct;
    }

    public VSwitchOvsConfigStruct getVSwitchOvsConfigStruct(SdnControllerHostRefVO refVO) {
        String proto = OvnControllerSystemTags.OVN_NORTHD_PROTO.getTokenByResourceUuid(refVO.getSdnControllerUuid(),
                OvnControllerSystemTags.OVN_NORTHD_PROTO_TOKEN);
        String port = OvnControllerSystemTags.OVN_NORTHD_PORT.getTokenByResourceUuid(refVO.getSdnControllerUuid(),
                OvnControllerSystemTags.OVN_NORTHD_PORT_TOKEN);

        Gson gson = new Gson();
        Type type = new TypeToken<Map<String, String>>(){}.getType();
        Map<String, String> nicNamePciAddressMap = gson.fromJson(refVO.getNicPciAddresses(), type);
        Map<String, String> nicNameDriverMap = gson.fromJson(refVO.getNicDrivers(), type);

        VSwitchOvsConfigStruct struct = new VSwitchOvsConfigStruct();
        struct.setvSwitchType(refVO.getvSwitchType());
        struct.setOvnControllerIp(self.getIp());
        struct.setBrExName(OvnControllerGlobalProperty.OVN_BR_EX_NAME);
        struct.setOvnEncapIP(refVO.getVtepIp());
        struct.setOvnEncapType(OvnControllerSystemTags.OVN_ENCAP_TYPE.getTokenByResourceUuid(
                refVO.getSdnControllerUuid(), OvnControllerSystemTags.OVN_ENCAP_TYPE_TOKEN));
        struct.setOvnRemoteConnection(String.format("%s:%s:%s", proto, self.getIp(), port));
        struct.setNicNamePciMap(nicNamePciAddressMap);
        struct.setNicNameDriverMap(nicNameDriverMap);
        struct.setBondMode(refVO.getBondMode());
        struct.setLacpMode(refVO.getLacpMode());
        struct.setOvnEncapNetmask(refVO.getNetmask());

        return struct;
    }


    void handMessage(OvnControllerAsyncHttpCallMsg msg) {
        OvnControllerAsyncHttpCallReply reply = new OvnControllerAsyncHttpCallReply();

        if (msg.isCheckStatus() && self.getStatus() != SdnControllerStatus.Connected) {
            reply.setError(operr("ovn controller[uuid:%s] is not connected, current status: %s", self.getUuid(), self.getStatus()));
            bus.reply(msg, reply);
            return;
        }

        String url = buildUrl(self.getIp(), msg.getPath());
        if (msg.getMethod() == HttpMethod.GET) {
            UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(url);
            for (Map.Entry<String, String> e : msg.getQueryParams().entrySet()) {
                ub.queryParam(e.getKey(), e.getValue());
            }
            url = ub.toUriString();
        }
        Map<String, String> headers = new HashMap<>();
        restf.asyncJson(url, msg.getCommand(), headers, msg.getMethod(), new JsonAsyncRESTCallback<LinkedHashMap>(msg) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
            }

            @Override
            public void success(LinkedHashMap ret) {
                reply.setResponse(ret);
                bus.reply(msg, reply);
            }

            @Override
            public Class<LinkedHashMap> getReturnClass() {
                return LinkedHashMap.class;
            }
        }, TimeUnit.SECONDS, OvnControllerGlobalConfig.CONNECT_TIMEOUT.value(Integer.class).longValue());
    }

    void handMessage(SdnControllerPingMsg msg) {
        SdnControllerPingReply reply = new SdnControllerPingReply();

        OvnControllerCommands.PingCmd cmd = new OvnControllerCommands.PingCmd(self);
        cmd.setUuid(self.getUuid());

        OvnControllerAsyncHttpCallMsg omsg = new OvnControllerAsyncHttpCallMsg();
        omsg.setMethod(HttpMethod.POST);
        omsg.setCommand(cmd);
        omsg.setCheckStatus(false);
        omsg.setPath(OvnControllerCommands.OVN_PING_PATH);
        omsg.setOvnControllerUuid(msg.getSdnControllerUuid());
        bus.makeTargetServiceIdByResourceUuid(omsg, OvnControllerConstant.SERVICE_ID, msg.getSdnControllerUuid());
        bus.send(omsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply mreply) {
                if (!mreply.isSuccess()) {
                    reply.setError(mreply.getError());
                    bus.reply(msg, reply);
                    return;
                }

                OvnControllerAsyncHttpCallReply re = mreply.castReply();
                OvnControllerCommands.PingRsp rsp = re.toResponse(OvnControllerCommands.PingRsp.class);
                if (rsp.isSuccess()) {
                    bus.reply(msg, reply);
                } else {
                    reply.setError(operr("failed to ping ovn controller[uuid:%s, ip:%s], because %s",
                            self.getUuid(), self.getIp(), rsp.getError()));
                    bus.reply(msg, reply);
                }
            }
        });
    }

    @Override
    public void preInitSdnController(APIAddSdnControllerMsg msg, Completion completion) {
        completion.success();
    }

    @Override
    public void initSdnController(APIAddSdnControllerMsg msg, Completion completion) {
        String proto = OvnControllerConstant.OVN_NORTHD_DEFAYLT_PROTO;
        Integer port = OvnControllerConstant.OVN_NORTHD_DEFAYLT_PORT;
        String encapType = OvnControllerConstant.OVN_NORTHD_DEFAYLT_ENCAP_TYPE;
        for (String tag : msg.getSystemTags()) {
            if (OvnControllerSystemTags.OVN_NORTHD_PROTO.isMatch(tag)) {
                proto = OvnControllerSystemTags.OVN_NORTHD_PROTO.getTokenByTag(tag, OvnControllerSystemTags.OVN_NORTHD_PROTO_TOKEN);
            } else if (OvnControllerSystemTags.OVN_NORTHD_PORT.isMatch(tag)) {
                port = Integer.parseInt(OvnControllerSystemTags.OVN_NORTHD_PORT.getTokenByTag(tag, OvnControllerSystemTags.OVN_NORTHD_PORT_TOKEN));
            } else if (OvnControllerSystemTags.OVN_ENCAP_TYPE.isMatch(tag)) {
                encapType = OvnControllerSystemTags.OVN_ENCAP_TYPE.getTokenByTag(tag, OvnControllerSystemTags.OVN_ENCAP_TYPE_TOKEN);
            }
        }

        SystemTagCreator creator = OvnControllerSystemTags.OVN_NORTHD_PROTO.newSystemTagCreator(self.getUuid());
        creator.setTagByTokens(map(
                e(OvnControllerSystemTags.OVN_NORTHD_PROTO_TOKEN, proto)
        ));
        creator.recreate = true;
        creator.inherent = false;
        creator.create();

        SystemTagCreator creator1 = OvnControllerSystemTags.OVN_NORTHD_PORT.newSystemTagCreator(self.getUuid());
        creator1.setTagByTokens(map(
                e(OvnControllerSystemTags.OVN_NORTHD_PORT_TOKEN, port)
        ));
        creator1.recreate = true;
        creator1.inherent = false;
        creator1.create();

        SystemTagCreator creator2 = OvnControllerSystemTags.OVN_ENCAP_TYPE.newSystemTagCreator(self.getUuid());
        creator2.setTagByTokens(map(
                e(OvnControllerSystemTags.OVN_ENCAP_TYPE_TOKEN, encapType)
        ));
        creator2.recreate = true;
        creator2.inherent = false;
        creator2.create();

        completion.success();
    }

    @Override
    public void postInitSdnController(SdnControllerVO vo, Completion completion) {
        SdnControllerPingMsg pmsg = new SdnControllerPingMsg();
        pmsg.setSdnControllerUuid(vo.getUuid());
        bus.makeTargetServiceIdByResourceUuid(pmsg, SdnControllerConstant.SERVICE_ID, vo.getUuid());
        bus.send(pmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(operr("ping sdn controller failed, error: %s", reply.getError().getDetails()));
                } else {
                    completion.success();
                }
            }
        });
    }

    @Override
    public void preCreateVxlanNetwork(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }


    public void syncL2Network(List<L2NetworkInventory> l2Invs, Completion completion) {
        OvnControllerCommands.LogicalSwitchCmd cmd = new OvnControllerCommands.LogicalSwitchCmd(self);
        cmd.setSync(true);
        for (L2NetworkInventory inv : l2Invs) {
            OvnControllerCommands.LogicalSwitchTo to = new OvnControllerCommands.LogicalSwitchTo();
            to.setUuid(inv.getUuid());
            to.setType(inv.getType());
            to.setName(inv.getName());
            to.setVni(inv.getVirtualNetworkId());
            cmd.getLogicalSwitches().add(to);
        }

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setMethod(HttpMethod.POST);
        cmsg.setCommand(cmd);
        cmsg.setPath(OvnControllerCommands.OVN_NETWORKS_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        cmsg.setCheckStatus(false);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.LogicalSwitchRsp rsp = re.toResponse(OvnControllerCommands.LogicalSwitchRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully sync logical switch[num:%d] to ovn controller[uuid:%s, ip:%s]",
                            l2Invs.size(), self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to sync logical switch to ovn controller[uuid:%s, ip:%s], because %s",
                            self.getUuid(), self.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }

    @Override
    public void createL2Network(L2NetworkInventory inv, List<String> systemTags, Completion completion) {
        OvnControllerCommands.LogicalSwitchCmd cmd = new OvnControllerCommands.LogicalSwitchCmd(self);
        OvnControllerCommands.LogicalSwitchTo to = new OvnControllerCommands.LogicalSwitchTo();
        to.setUuid(inv.getUuid());
        to.setType(inv.getType());
        to.setName(inv.getName());
        to.setVni(inv.getVirtualNetworkId());
        cmd.getLogicalSwitches().add(to);

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setMethod(HttpMethod.POST);
        cmsg.setCommand(cmd);
        cmsg.setPath(OvnControllerCommands.OVN_NETWORKS_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        cmsg.setCheckStatus(true);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.LogicalSwitchRsp rsp = re.toResponse(OvnControllerCommands.LogicalSwitchRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully add logical switch[uuid:%s, name:%s] to ovn controller[uuid:%s, ip:%s]",
                            inv.getUuid(), inv.getName(), self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to add logical switch[uuid:%s, name:%s] to ovn controller[uuid:%s, ip:%s], because %s",
                            inv.getUuid(), inv.getName(), self.getUuid(), self.getIp(), rsp.getError());
                   completion.fail(err);
                }
            }
        });
    }

    @Override
    public void postCreateVxlanNetwork(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    public void preAttachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    public void attachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> clusterUuids, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    public void postAttachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    public void deleteSdnController(SdnControllerDeletionMsg msg, SdnControllerInventory sdn, Completion completion) {
        completion.success();
    }


    @Override
    public void detachL2NetworkFromCluster(L2VxlanNetworkInventory vxlan, String clusterUuid, Completion completion) {
        completion.success();
    }

    @Override
    public void deleteL2Network(L2NetworkInventory inv, Completion completion) {
        OvnControllerCommands.LogicalSwitchCmd cmd = new OvnControllerCommands.LogicalSwitchCmd(self);
        OvnControllerCommands.LogicalSwitchTo to = new OvnControllerCommands.LogicalSwitchTo();
        to.setUuid(inv.getUuid());
        to.setName(inv.getName());
        cmd.getLogicalSwitches().add(to);

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setMethod(HttpMethod.DELETE);
        cmsg.setPath(OvnControllerCommands.OVN_NETWORKS_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        cmsg.setCheckStatus(true);
        cmsg.setCommand(cmd);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.LogicalSwitchRsp rsp = re.toResponse(OvnControllerCommands.LogicalSwitchRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully add logical switch[uuid:%s, name:%s] to ovn controller[uuid:%s, ip:%s]",
                            inv.getUuid(), inv.getName(), self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to add logical switch[uuid:%s, name:%s] to ovn controller[uuid:%s, ip:%s], because %s",
                            inv.getUuid(), inv.getName(), self.getUuid(), self.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }

    @Override
    public List<SdnVniRange> getVniRange(SdnControllerInventory controller) {
        return new ArrayList<>();
    }

    @Override
    public List<SdnVlanRange> getVlanRange(SdnControllerInventory controller) {
        return new ArrayList<>();
    }

    @Override
    public List<String> getL2NetworkOfSdnController() {
        String sql = "select l2.uuid from L2NetworkVO l2, SystemTagVO tag where " +
                " l2.vSwitchType=:vSwitchType " +
                " and l2.uuid=tag.resourceUuid " +
                " and tag.tag=:tag";
        TypedQuery<String> q = dbf.getEntityManager().createQuery(sql, String.class);
        q.setParameter("vSwitchType", L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK);
        q.setParameter("tag", L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.instantiateTag(
                map(e(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN, self.getUuid()))));

        List<String> l2Uuids = q.getResultList();
        return  l2Uuids;
    }

    @Override
    public void addHost(APISdnControllerAddHostMsg msg, Completion completion) {
        VSwitchOvsConfigStruct struct = getVSwitchOvsConfigStruct(msg);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("ovn-controller-add-host-%s", msg.getHostUuid()));
        chain.then(new Flow() {
            String __name__ = String.format("install-ovn-packages");

            @Override
            public void run(FlowTrigger trigger, Map data) {
                ovsBackend.installPackages(msg.getHostUuid(), struct, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                ovsBackend.unInstallPackages(msg.getHostUuid(), struct, new NoErrorCompletion(trigger) {
                            @Override
                            public void done() {
                                trigger.rollback();
                            }
                        });
            }
        }).then(new Flow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                ovsBackend.startService(msg.getHostUuid(), struct, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                ovsBackend.stopService(msg.getHostUuid(), struct, new NoErrorCompletion(trigger) {
                            @Override
                            public void done() {
                                trigger.rollback();
                            }
                        });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    @Override
    public void removeHost(SdnControllerRemoveHostMsg msg, Completion completion) {
        SdnControllerHostRefVO ref = Q.New(SdnControllerHostRefVO.class)
                .eq(SdnControllerHostRefVO_.hostUuid, msg.getHostUuid())
                .eq(SdnControllerHostRefVO_.sdnControllerUuid, msg.getSdnControllerUuid())
                .eq(SdnControllerHostRefVO_.vSwitchType, msg.getvSwitchType()).find();

        VSwitchOvsConfigStruct struct = getVSwitchOvsConfigStruct(ref);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("ovn-controller-%s-remove-host-%s", self.getUuid(), msg.getHostUuid()));
        chain.then(new NoRollbackFlow() {
            String __name__ = "remove-ovn-nics";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<String> l2Uuids = getL2NetworkOfSdnController();
                if (l2Uuids.isEmpty()) {
                    logger.debug(String.format("there is no ovn l2 network attached to ovn controller[uuid:%s]",
                            self.getUuid()));
                    trigger.next();
                    return;
                }

                /* get all ovn vm nics of this host */
                String sql = "select nic.uuid,nic.vmInstanceUuid from VmNicVO nic, VmInstanceVO vm, L3NetworkVO l3 where " +
                        "vm.uuid=nic.vmInstanceUuid " +
                        "and (vm.hostUuid=:hostUuid or (vm.hostUuid is null and vm.lastHostUuid=:hostUuid)) " +
                        "and nic.l3NetworkUuid=l3.uuid and l3.l2NetworkUuid in (:l2Uuids)";
                TypedQuery<Tuple> q = dbf.getEntityManager().createQuery(sql, Tuple.class);
                q.setParameter("hostUuid", msg.getHostUuid());
                q.setParameter("l2Uuids", l2Uuids);
                List<Tuple> tuples = q.getResultList();
                if (tuples.isEmpty()) {
                    logger.debug(String.format("there is no ovn nic attached to host [uuid:%s]",
                            msg.getHostUuid()));
                    trigger.next();
                    return;
                }

                new While<>(tuples).step((t, wcomp) -> {
                    DetachNicFromVmMsg dmsg = new DetachNicFromVmMsg();
                    String vmUuid = t.get(1, String.class);
                    dmsg.setVmNicUuid(t.get(0, String.class));
                    dmsg.setVmInstanceUuid(vmUuid);
                    bus.makeTargetServiceIdByResourceUuid(dmsg, VmInstanceConstant.SERVICE_ID, vmUuid);
                    bus.send(dmsg, new CloudBusCallBack(wcomp) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.getError() != null) {
                                wcomp.addError(reply.getError());
                                wcomp.allDone();
                            } else {
                                wcomp.done();
                            }
                        }
                    });
                }, 10).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                        } else {
                            logger.warn(String.format("detach vmnic failed, because %s", errorCodeList.getReadableDetails()));
                            trigger.next();
                        }
                    }
                });

            }
        }).then(new NoRollbackFlow() {
            String __name__ = "uninstall-ovn-packages";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                ovsBackend.stopService(msg.getHostUuid(), struct, new NoErrorCompletion(trigger) {
                            @Override
                            public void done() {
                                trigger.next();
                            }
                        });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                ovsBackend.unInstallPackages(msg.getHostUuid(), struct, new NoErrorCompletion(trigger) {
                            @Override
                            public void done() {
                                trigger.next();
                            }
                        });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    @Override
    public void changeHost(SdnControllerHostRefVO oldRef, SdnControllerHostRefVO newRef, Completion completion) {
        VSwitchOvsConfigStruct struct = getVSwitchOvsConfigStruct(newRef);
        ovsBackend.startService(oldRef.getHostUuid(), struct, new Completion(completion) {
            @Override
            public void success() {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    public void addLogicalPorts(List<VmNicInventory> nics, boolean sync, Completion completion) {
        Map<String, String> logicalSwitchNameMap = new HashMap<>();
        Map<String, Integer> vniMap = new HashMap<>();
        Map<String, String> chassisNameMap = new HashMap<>();

        if (!sync && nics.isEmpty()) {
            completion.success();
            return;
        }

        List<OvnControllerCommands.LogicalSwitchPortTo> ports = new ArrayList<>();
        for (VmNicInventory nic : nics) {
            OvnControllerCommands.LogicalSwitchPortTo to = new OvnControllerCommands.LogicalSwitchPortTo();
            /* ONLY ipv4 is used */
            L3NetworkVO l3VO = dbf.findByUuid(nic.getL3NetworkUuid(), L3NetworkVO.class);
            VmInstanceVO vmVo = dbf.findByUuid(nic.getVmInstanceUuid(), VmInstanceVO.class);

            to.setUuid(nic.getUuid());
            to.setName(nic.getInternalName());
            to.setMac(nic.getMac());
            for (UsedIpInventory ip : nic.getUsedIps()) {
                if (IPv6NetworkUtils.isIpv6Address(ip.getIp())) {
                    to.setIp6(ip.getIp());
                } else if (NetworkUtils.isIpAddress(ip.getIp())) {
                    to.setIp(ip.getIp());
                }
            }
            Integer vni = vniMap.get(nic.getL3NetworkUuid());
            if (vni == null) {
                int vnid = Q.New(L2NetworkVO.class).eq(L2NetworkVO_.uuid, l3VO.getL2NetworkUuid())
                        .select(L2NetworkVO_.virtualNetworkId).findValue();
                vniMap.put(nic.getL3NetworkUuid(), vnid);
                vni = vnid;
            }
            to.setLogicalSwitchUuid(l3VO.getL2NetworkUuid());
            to.setVni(vni);

            String chassisName = chassisNameMap.get(nic.getVmInstanceUuid());
            if (chassisName == null) {
                chassisName = Q.New(HostVO.class).eq(HostVO_.uuid, vmVo.getHostUuid())
                        .select(HostVO_.managementIp).findValue();
                chassisNameMap.put(nic.getVmInstanceUuid(), chassisName);
            }
            to.setRequestedChassis(chassisName);
            if (OvnControllerGlobalConfig.ACTIVATION_STRATEGY.value(String.class)
                    .equals(OvnControllerConstant.OVN_CONTROLLER_ACTIVATION_STRATEGY)) {
                to.setActivationStrategy(OvnControllerConstant.OVN_CONTROLLER_ACTIVATION_STRATEGY);
            }
            to.setIfaceIdVer(nic.getInternalName());

            //dhcp options
            if (l3VO.enableIpAddressAllocation()) {
                List<IpRangeInventory> iprs = IpRangeHelper.getNormalIpRanges(l3VO);
                List<IpRangeInventory> ip4s = iprs.stream().filter(
                        ipr -> ipr.getIpVersion() == IPv6Constants.IPv4).collect(Collectors.toList());
                List<IpRangeInventory> ip6s = iprs.stream().filter(
                        ipr -> ipr.getIpVersion() == IPv6Constants.IPv6).collect(Collectors.toList());
                if (!ip4s.isEmpty()) {
                    to.setDhcpv4Options(ovnHelper.getDhcp4OptionsID(nic.getL3NetworkUuid()));
                }
                if (!ip6s.isEmpty()) {
                    to.setDhcpv6Options(ovnHelper.getDhcp6OptionsID(nic.getL3NetworkUuid()));
                }
                if (vmVo.getDefaultL3NetworkUuid().equals(nic.getL3NetworkUuid())) {
                    String hostName = VmSystemTags.HOSTNAME.getTokenByResourceUuid(vmVo.getUuid(), VmSystemTags.HOSTNAME_TOKEN);
                    to.setHostname(hostName);
                }
            }

            VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(vmVo.getType());
            VmNicQosStruct struct = backend.getNicQos(vmVo.getUuid(), nic.getUuid());
            if (struct.inboundBandwidth != -1) {
                to.setInboundBandwidth(struct.inboundBandwidth);
            }
            if (struct.outboundBandwidth != -1) {
                to.setOutboundBandwidth(struct.outboundBandwidth);
            }

            ports.add(to);
        }

        OvnControllerCommands.LogicalSwitchPortCmd cmd = new OvnControllerCommands.LogicalSwitchPortCmd(self);
        cmd.setSync(sync);
        cmd.setNics(ports);
        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setMethod(HttpMethod.POST);
        cmsg.setPath(OvnControllerCommands.OVN_VM_NICS_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        cmsg.setCheckStatus(!sync);
        cmsg.setCommand(cmd);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                if (!re.isSuccess()) {
                    completion.fail(re.getError());
                    return;
                }

                OvnControllerCommands.LogicalSwitchPortRsp rsp = re.toResponse(OvnControllerCommands.LogicalSwitchPortRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully add logical switch ports to ovn controller[uuid:%s, ip:%s]",
                            self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to add logical switch ports to ovn controller[uuid:%s, ip:%s], because %s",
                            self.getUuid(), self.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }

    @Override
    public void addVmNics(List<VmNicInventory> nics, Completion completion) {
        addLogicalPorts(nics, false, completion);
    }

    @Override
    public void removeVmNics(List<VmNicInventory> nics, Completion completion) {
        List<OvnControllerCommands.LogicalSwitchPortTo> ports = new ArrayList<>();
        for (VmNicInventory nic : nics) {
            OvnControllerCommands.LogicalSwitchPortTo to = new OvnControllerCommands.LogicalSwitchPortTo();
            to.setUuid(nic.getUuid());
            to.setName(nic.getInternalName());
            ports.add(to);
        }

        OvnControllerCommands.LogicalSwitchPortCmd cmd = new OvnControllerCommands.LogicalSwitchPortCmd(self);
        cmd.setNics(ports);

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setCommand(cmd);
        cmsg.setMethod(HttpMethod.DELETE);
        cmsg.setPath(OvnControllerCommands.OVN_VM_NICS_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        cmsg.setCheckStatus(true);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.LogicalSwitchRsp rsp = re.toResponse(OvnControllerCommands.LogicalSwitchRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully delete logical switch port from ovn controller[uuid:%s, ip:%s]",
                            self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to delete logical switch port from ovn controller[uuid:%s, ip:%s], because %s",
                            self.getUuid(), self.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }
}