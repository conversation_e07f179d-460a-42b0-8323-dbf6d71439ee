package org.zstack.cube;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatch;
import org.zstack.header.Component;
import org.zstack.header.host.*;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.managementnode.PrepareDbInitialValueExtensionPoint;
import org.zstack.header.message.MessageReply;
import org.zstack.header.rest.RESTFacade;
import org.zstack.kvm.KVMHostAsyncHttpCallMsg;
import org.zstack.mevoco.DeployMode;
import org.zstack.mevoco.MevocoGlobalProperty;
import org.zstack.sns.system.SNSSystemAlarmTopicManager;
import org.zstack.utils.ShellResult;
import org.zstack.utils.ShellUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.zwatch.ZWatchGlobalConfig;
import org.zstack.zwatch.alarm.*;
import org.zstack.zwatch.alarm.sns.SNSActionFactory;
import org.zstack.zwatch.datatype.EmergencyLevel;
import org.zstack.zwatch.datatype.Label;
import org.zstack.zwatch.namespace.HostNamespace;
import org.zstack.zwatch.namespace.ThirdpartyAlertNamespace;
import org.zstack.zwatch.ruleengine.ComparisonOperator;
import org.zstack.zwatch.thirdparty.Constants;
import org.zstack.zwatch.thirdparty.entity.ThirdpartyPlatformState;
import org.zstack.zwatch.thirdparty.entity.ThirdpartyPlatformVO;
import org.zstack.zwatch.thirdparty.entity.ThirdpartyPlatformVO_;
import org.zstack.zwatch.thirdparty.xsky.XSKYConstants;

import java.io.File;
import java.sql.Timestamp;
import java.util.concurrent.TimeUnit;

import static org.zstack.core.Platform.operr;
import static org.zstack.zwatch.alarm.system.AlarmSystemTagUtils.persistSystemTagOfLanguage;
import static org.zstack.zwatch.alarm.system.SystemAlarmManager.*;

public class CubeManagerImpl implements CubeAlarmManager, PrepareDbInitialValueExtensionPoint, HostAfterConnectedExtensionPoint, Component {
    protected static final CLogger logger = Utils.getLogger(CubeManagerImpl.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private HostNamespace hostNamespace;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private ThirdpartyAlertNamespace thirdpartyAlertNamespace;
    @Autowired
    private RESTFacade restf;
    @Autowired
    private EventFacade evf;

    @Override
    public void prepareDbInitialValue() {
        if (!DeployMode.cube.toString().equals(MevocoGlobalProperty.DEPLOY_MODE)) {
            return;
        }

        addXSkyToThirdPartyAlert();
    }

    private void addXSkyToThirdPartyAlert() {
        boolean thirdpartyPlatforExist = Q.New(ThirdpartyPlatformVO.class)
                .eq(ThirdpartyPlatformVO_.uuid, XSKYConstants.XSKY_MESSAGE_SOURCE_UUID)
                .isExists();
        if (thirdpartyPlatforExist) {
            return;
        }

        boolean eventSubscriptionExist = Q.New(EventSubscriptionVO.class)
                .eq(EventSubscriptionVO_.uuid, XSKYConstants.XSKY_MESSAGE_ALARM_UUID)
                .isExists();
        if (eventSubscriptionExist) {
            return;
        }

        // enable thirdparty alter
        ZWatchGlobalConfig.THIRDPARTY_ALERT_ENABLE.updateValue(true);

        // get xsky monitor ip
        File sdsInfo = new File(XSKYConstants.XSKY_INFO_PATH);
        if (!sdsInfo.exists()) {
            return;
        }
        ShellResult infoRes = ShellUtils.runAndReturn(String.format("grep monitor: %s | awk -F ':' '{print $2}'| awk -F ',' '{print $1}'", XSKYConstants.XSKY_INFO_PATH));
        if (!infoRes.isReturnCode(0)) {
            return;
        }
        String master = infoRes.getStdout().trim();

        // get xsky token
        File sdsToken = new File(XSKYConstants.XSKY_TOKEN_PATH);
        if (!sdsToken.exists()) {
            return;
        }
        ShellResult tokenRes = ShellUtils.runAndReturn(String.format("cat %s", XSKYConstants.XSKY_TOKEN_PATH));
        if (!tokenRes.isReturnCode(0)) {
            return;
        }
        String token = tokenRes.getStdout().trim();

        // get xsky versiob
        ShellResult versionRes = ShellUtils.runAndReturn("xms-cli -v | grep -i xms-cli | awk -F ':' '{print $2}' | awk -F '_' '{print $2}'");
        if (!versionRes.isReturnCode(0)) {
            return;
        }
        String version = versionRes.getStdout().trim();

        // The alter url of the v5 xsky is different from other versions
        final String url = version.compareTo("5") < 0 ? String.format("http://%s:8056/v1/alerts/?token=%s", master, token) : String.format("http://%s:8056/v1/alert-infos/?token=%s", master, token);

        ThirdpartyPlatformVO vo = new ThirdpartyPlatformVO();
        vo.setUuid(XSKYConstants.XSKY_MESSAGE_SOURCE_UUID);
        vo.setType(Constants.XSKY);
        vo.setTemplate(XSKYConstants.XSKY_MESSAGE_TEMPLATE);
        vo.setName("CephMessageSource");
        vo.setState(ThirdpartyPlatformState.Enabled.name());
        vo.setUrl(url);
        vo.setLastSyncDate(new Timestamp(System.currentTimeMillis()));
        dbf.persist(vo);

        EventSubscriptionVO xskyMessageEventSubscription = new EventSubscriptionVO();
        xskyMessageEventSubscription.setUuid(XSKYConstants.XSKY_MESSAGE_ALARM_UUID);
        xskyMessageEventSubscription.setName("CephMessageAlarm");
        xskyMessageEventSubscription.setEventName(ThirdpartyAlertNamespace.ThirdpartyAlert.getName());
        xskyMessageEventSubscription.setNamespace(thirdpartyAlertNamespace.getName());
        xskyMessageEventSubscription.setState(EventSubscriptionState.Enabled);
        xskyMessageEventSubscription.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
        xskyMessageEventSubscription.setEmergencyLevel(EmergencyLevel.Important);
        dbf.persist(xskyMessageEventSubscription);

        if (!Q.New(EventSubscriptionActionVO.class)
                .eq(EventSubscriptionActionVO_.subscriptionUuid, XSKYConstants.XSKY_MESSAGE_ALARM_UUID)
                .eq(EventSubscriptionActionVO_.actionUuid, SNSSystemAlarmTopicManager.SYSTEM_ALARM_TOPIC_UUID)
                .isExists()) {
            EventSubscriptionActionVO action = new EventSubscriptionActionVO();
            action.setActionUuid(SNSSystemAlarmTopicManager.SYSTEM_ALARM_TOPIC_UUID);
            action.setActionType(SNSActionFactory.type.toString());
            action.setSubscriptionUuid(XSKYConstants.XSKY_MESSAGE_ALARM_UUID);
            dbf.persist(action);
        }

        if (!Q.New(EventSubscriptionLabelVO.class)
                .eq(EventSubscriptionLabelVO_.subscriptionUuid, XSKYConstants.XSKY_MESSAGE_ALARM_UUID)
                .eq(EventSubscriptionLabelVO_.value, XSKYConstants.XSKY_MESSAGE_SOURCE_UUID)
                .isExists()) {
            EventSubscriptionLabelVO label = new EventSubscriptionLabelVO();
            label.setUuid(Platform.getUuid());
            label.setKey("PlatformUuid");
            label.setValue(XSKYConstants.XSKY_MESSAGE_SOURCE_UUID);
            label.setSubscriptionUuid(XSKYConstants.XSKY_MESSAGE_ALARM_UUID);
            label.setOperator(Label.Operator.Equal);
            dbf.persist(label);
        }
    }

    @Override
    public boolean start() {
        if (!DeployMode.cube.toString().equals(MevocoGlobalProperty.DEPLOY_MODE)) {
            return true;
        }

        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void afterHostConnected(HostInventory host) {

    }
}
