package org.zstack.iam2.rbac;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatch;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.cluster.ClusterVO;
import org.zstack.header.cluster.ClusterVO_;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.identity.rbac.PolicyMatcher;
import org.zstack.header.identity.rbac.RBAC;
import org.zstack.header.identity.rbac.RBACEntity;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.network.l2.L2NetworkClusterRefVO;
import org.zstack.header.network.l2.L2NetworkClusterRefVO_;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l2.L2NetworkVO_;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO_;
import org.zstack.header.vm.APICreateVmInstanceFromVolumeMsg;
import org.zstack.header.vm.APICreateVmInstanceMsg;
import org.zstack.iam2.IAM2APIRequestCheckerExtensionPoint;
import org.zstack.iam2.IAM2GlobalConfig;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO_;
import org.zstack.identity.rbac.OperationTargetAPIRequestChecker;
import org.zstack.identity.rbac.PolicyUtils;
import org.zstack.resourceconfig.ResourceConfigFacade;
import org.zstack.vmware.ESXConstant;

import java.util.List;

import static org.zstack.core.Platform.argerr;

/**
 * Created by kayo on 2018/9/25.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class IAM2OperationTargetAPIRequestChecker extends OperationTargetAPIRequestChecker {
    private static PolicyMatcher policyMatcher = new PolicyMatcher();
    @Autowired
    private ResourceConfigFacade rcf;
    @Autowired
    private PluginRegistry pluginRgty;


    @Override
    protected boolean isMatch(String as) {
        String ap = PolicyUtils.apiNamePatternFromAction(as);
        return policyMatcher.match(ap, rbacEntity.getApiMessage().getClass().getName());
    }

    @Override
    public void check(RBACEntity entity) {
        super.check(entity);
        rbacEntity = entity;
        RBAC.Permission info = getRBACInfo();

        new SQLBatch() {
            @Override
            protected void scripts() {
                String projectUuid = Q.New(IAM2ProjectAccountRefVO.class)
                        .eq(IAM2ProjectAccountRefVO_.accountUuid, rbacEntity.getApiMessage().getSession().getAccountUuid())
                        .select(IAM2ProjectAccountRefVO_.projectUuid)
                        .findValue();
                if (rcf.getResourceConfigValue(IAM2GlobalConfig.IAM2_FORCE_ENABLE_SECURITY_GROUP, projectUuid, Boolean.class)) {
                    APIMessage.getApiParams().get(rbacEntity.getApiMessage().getClass()).forEach(this::checkOperationTarget);
                }
            }

            private void checkOperationTarget(APIMessage.FieldParam param) {
                Class resourceType = param.param.resourceType();
                if (resourceType == Object.class)  {
                    return;
                }

                if (info.getTargetResources().isEmpty()) {
                    return;
                }

                if (rbacEntity.getApiMessage() instanceof APISyncCallMessage) {
                    // no check to read api
                    return;
                }

                try {
                    if (resourceType.equals(L3NetworkVO.class)) {
                        checkL3IfTheSecurityGroup(param);
                    }
                } catch (OperationFailureException oe) {
                    throw oe;
                } catch (Exception e) {
                    throw new CloudRuntimeException(e);
                }

            }

            private void checkL3IfTheSecurityGroup(APIMessage.FieldParam param) throws IllegalAccessException {
                List<String> systemtags = rbacEntity.getApiMessage().getSystemTags();

                if (rbacEntity.getApiMessage() instanceof APICreateVmInstanceMsg) {
                    APICreateVmInstanceMsg cmsg = (APICreateVmInstanceMsg) rbacEntity.getApiMessage();
                    if (cmsg.getL3NetworkUuids().isEmpty()) {
                        return;
                    }

                    List<String> l2Uuids = Q.New(L3NetworkVO.class).in(L3NetworkVO_.uuid, cmsg.getL3NetworkUuids())
                            .select(L3NetworkVO_.l2NetworkUuid).listValues();
                    List<String> clusterUuids = Q.New(L2NetworkClusterRefVO.class).in(L2NetworkClusterRefVO_.l2NetworkUuid, l2Uuids)
                            .select(L2NetworkClusterRefVO_.clusterUuid).listValues();
                    if (clusterUuids.isEmpty()) {
                        throw new ApiMessageInterceptionException(argerr(String.format(
                                "create vm instance failed, because l3 network[uuid:%s] is not attached any clusters",
                                cmsg.getL3NetworkUuids())));
                    }
                    List<String> hypervisorTypes = Q.New(ClusterVO.class).in(ClusterVO_.uuid, clusterUuids)
                            .select(ClusterVO_.hypervisorType).listValues();
                    if (hypervisorTypes.contains(ESXConstant.VMWARE_HYPERVISOR_TYPE)) {
                        /* esx vm doesn't need security group */
                        return;
                    }
                }

                String refTag = null;
                if (systemtags != null && !systemtags.isEmpty()) {
                    for (IAM2APIRequestCheckerExtensionPoint exp : pluginRgty.getExtensionList(IAM2APIRequestCheckerExtensionPoint.class)) {
                        refTag = exp.checkTagsIncludedSecurityGroupTag(systemtags);
                        if (!StringUtils.isEmpty(refTag)) {
                            break;
                        }
                    }

                }

                if (rbacEntity.getApiMessage() instanceof APICreateMessage && !StringUtils.isEmpty(refTag)) {
                    return;
                }

                if (rbacEntity.getApiMessage() instanceof APICreateMessage && StringUtils.isEmpty(refTag)) {
                    throw new ApiMessageInterceptionException(argerr(
                            "since the project starts the force securityGroup, systemtag is required for VM operation"));
                }
            }

        }.execute();


    }
}
