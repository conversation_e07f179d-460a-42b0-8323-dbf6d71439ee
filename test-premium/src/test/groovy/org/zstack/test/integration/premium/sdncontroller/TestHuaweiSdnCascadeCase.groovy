package org.zstack.test.integration.premium.sdncontroller

import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.host.NetworkInterfaceType
import org.zstack.header.network.l2.L2NetworkConstant
import org.zstack.network.hostNetworkInterface.*
import org.zstack.network.huawei.imaster.HuaweiIMasterConstant
import org.zstack.network.huawei.imaster.HuaweiIMasterSystemTags
import org.zstack.network.l3.L3NetworkSystemTags
import org.zstack.network.ovn.OvnControllerCommands
import org.zstack.sdk.*
import org.zstack.sdnController.header.SdnControllerConstant
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
class TestHuaweiSdnCascadeCase extends PremiumSubCase {
    EnvSpec env
    KVMHostInventory kvm

    @Override
    void setup() {
        useSpring(SdnControllerTest.springSpec)
    }

    @Override
    void environment() {
        env = SdnControllerTestEnv.BasicVpc()
    }

    @Override
    void test() {
        env.create {
            TestHuaweiSdnControllerCascade()
            cleanEnv()
        }
    }

    void TestHuaweiSdnControllerCascade() {
        ZoneInventory zone = env.inventoryByName("zone")
        ClusterInventory cluster = env.inventoryByName("cluster")
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")
        ImageInventory image = env.inventoryByName("image1")
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->

            OvnControllerCommands.LogicalSwitchPortCmd cmd =
                    JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchPortCmd.class)

            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:8c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:8d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:8e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingName("bond0");
            bond0.setBondingType("noBridge")
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE);
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setMode(L2NetworkConstant.BONDING_MODE_AB);
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }

        reconnectHost {
            uuid = h1.uuid
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->

            OvnControllerCommands.LogicalSwitchPortCmd cmd =
                    JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchPortCmd.class)

            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:9c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:9d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:9e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE);
            bond0.setMode(L2NetworkConstant.BONDING_MODE_LACP);
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingType("noBridge")
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }
        reconnectHost {
            uuid = h2.uuid
        }

        SdnControllerInventory sdn = addSdnController {
            name = "huawei-imaster"
            vendorType = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE
            description = "huawei imaster"
            ip = "*************"
            userName = "admin"
            password = "password"
            systemTags = [
                    HuaweiIMasterSystemTags.HUAWEI_VLAN_RANGE.instantiateTag(
                            [(HuaweiIMasterSystemTags.HUAWEI_START_VLAN_TOKEN): 1000,
                             (HuaweiIMasterSystemTags.HUAWEI_END_VLAN_TOKEN): 2000]),
                    HuaweiIMasterSystemTags.HUAWEI_VNI_RANGE.instantiateTag(
                            [(HuaweiIMasterSystemTags.HUAWEI_START_VNI_TOKEN): 1000,
                             (HuaweiIMasterSystemTags.HUAWEI_END_VNI_TOKEN): 2000]),
                    ]
        }
        // link host1: eth1, eth2, eth3 ---> 10GE1/0/1, 10GE1/0/2, 10GE1/0/3
        // link host2: eth1, eth2, eth3 ---> 10GE1/0/4, 10GE1/0/5, 10GE1/0/6
        String host1Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host1Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host1Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        String host2Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host2Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host2Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/1")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/2")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/3")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth3Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/4")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/5")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/6")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth3Uuid).update()

        L2VxlanNetworkPoolInventory hardPool = createL2HardwareVxlanNetworkPool {
            name = "hardwareVxlanPool1"
            type = SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE
            sdnControllerUuid = sdn.uuid
            physicalInterface = "bond0"
            zoneUuid = zone.uuid
            startVlan = 1000
            endVlan = 1200
        }

        /*
        createVniRange {
            startVni = 1000
            endVni = 1200
            l2NetworkUuid = hardPool.getUuid()
            name = "TestRange-1"
        }*/

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }

        HuaweiIMasterTenantInventory tenant = queryHuaweiIMasterTenant {}[0]
        HuaweiIMasterVpcInventory vpc = queryHuaweiIMasterVpc {} [0]
        
        L2VxlanNetworkInventory vx2 = createL2HardwareVxlanNetwork {
            poolUuid = hardPool.uuid
            name = "TestVxlan2"
            zoneUuid = zone.uuid
            vni = 1002
            vlan = 1001
            systemTags = [
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid]),
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN): tenant.uuid]),
            ]
        }

        HuaweiIMasterVRouterInventory vr = createHuaweiIMasterVRouter {
            name = "R1"
            huaweiVpcUuid = vpc.uuid
        }

        L3NetworkInventory l3 = createL3Network {
            name = "huaweiL3"
            l2NetworkUuid = vx2.uuid
            systemTags = [L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER.instantiateTag(
                    [(L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER_TOKEN): vr.uuid])]
        }

        attachNetworkServiceToL3Network {
            l3NetworkUuid = l3.uuid
            networkServices = ['Flat':['DHCP', 'Userdata']]
        }

        addIpRange {
            name = "TestIpRange"
            l3NetworkUuid = l3.getUuid()
            startIp = "*************0"
            endIp = "***************"
            gateway = "*************"
            netmask = "*************"
        }

        createVmInstance {
            name = "vm-pub"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
        }

        deleteHuaweiIMasterTenant {
            uuid = tenant.uuid
        }
    }

    void cleanEnv() {
        List<SdnControllerInventory> invs = querySdnController {}
        for (SdnControllerInventory inv : invs) {
            removeSdnController {
                uuid = inv.uuid
            }
        }
        //TODO
        SQL.New(PhysicalSwitchPortVO.class).delete()
        SQL.New(PhysicalSwitchVO.class).delete()
    }

    @Override
    void clean() {
        env.delete()
    }
}
