package org.zstack.vrouterRoute;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * Created by weiwang on 15/06/2017.
 */
@StaticMetamodel(VirtualRouterVRouterRouteTableRefVO.class)
public class VirtualRouterVRouterRouteTableRefVO_ {
    public static volatile SingularAttribute<VirtualRouterVRouterRouteTableRefVO, String> virtualRouterVmUuid;
    public static volatile SingularAttribute<VirtualRouterVRouterRouteTableRefVO, String> routeTableUuid;
}
