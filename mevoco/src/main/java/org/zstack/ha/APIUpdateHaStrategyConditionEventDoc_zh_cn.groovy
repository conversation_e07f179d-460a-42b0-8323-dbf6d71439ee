package org.zstack.ha

import org.zstack.ha.HaStrategyConditionInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "更新Ha策略返回"

	ref {
		name "inventory"
		path "org.zstack.ha.APIUpdateHaStrategyConditionEvent.inventory"
		desc "null"
		type "HaStrategyConditionInventory"
		since "4.7.0"
		clz HaStrategyConditionInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.7.0"
	}
	ref {
		name "error"
		path "org.zstack.ha.APIUpdateHaStrategyConditionEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.7.0"
		clz ErrorCode.class
	}
}
