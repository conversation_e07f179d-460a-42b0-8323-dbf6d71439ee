package org.zstack.mttyDevice;

import org.zstack.header.identity.rbac.RBACDescription;

public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("mtty-devices")
                .adminOnlyAPIs("org.zstack.mttyDevice.**")
                .normalAPIs(APIQueryMttyDeviceMsg.class)
                .build();
    }

    @Override
    public void contributeToRoles() {
    
    }

    @Override
    public void roles() {
        roleBuilder()
                .uuid("77affc9ac2eb452cb7170953460e9770")
                .name("mtty-devices")
                .permissionsByName("mtty-devices")
                .build();
    }

    @Override
    public void globalReadableResources() {

    }
}
