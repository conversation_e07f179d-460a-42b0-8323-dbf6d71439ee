package org.zstack.compute.vm;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.appliancevm.ApplianceVmSystemTags;
import org.zstack.compute.cluster.MevocoClusterGlobalConfig;
import org.zstack.compute.cpuPinning.CpuRangeSet;
import org.zstack.compute.host.MevocoKVMAgentCommands;
import org.zstack.compute.host.MevocoKVMConstant;
import org.zstack.compute.sriov.SriovSystemTags;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.core.config.GlobalConfig;
import org.zstack.core.db.*;
import org.zstack.core.jsonlabel.JsonLabel;
import org.zstack.core.jsonlabel.JsonLabelInventory;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SingleFlightTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.core.workflow.SimpleFlowChain;
import org.zstack.ha.HaSystemTags;
import org.zstack.ha.VmHaLevel;
import org.zstack.header.allocator.AllocateHostDryRunReply;
import org.zstack.header.allocator.DesignatedAllocateHostMsg;
import org.zstack.header.allocator.HostAllocatorConstant;
import org.zstack.header.allocator.HostAllocatorError;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.progress.TaskProgressRange;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.*;
import org.zstack.header.image.*;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.message.NeedReplyMessage;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.UsedIpVO;
import org.zstack.header.storage.primary.*;
import org.zstack.header.storage.snapshot.*;
import org.zstack.header.storage.snapshot.group.VolumeSnapshotGroupInventory;
import org.zstack.header.storage.snapshot.group.VolumeSnapshotGroupRefInventory;
import org.zstack.header.vm.ChangeVmPasswordMsg;
import org.zstack.header.vm.ChangeVmPasswordReply;
import org.zstack.header.vm.*;
import org.zstack.header.vm.cdrom.VmCdRomVO;
import org.zstack.header.vm.cdrom.VmCdRomVO_;
import org.zstack.header.volume.*;
import org.zstack.identity.AccountManager;
import org.zstack.image.ImageSystemTags;
import org.zstack.kvm.KVMGlobalConfig;
import org.zstack.kvm.KVMHostAsyncHttpCallMsg;
import org.zstack.kvm.KVMHostAsyncHttpCallReply;
import org.zstack.kvm.KVMSystemTags;
import org.zstack.kvm.xmlhook.XmlHookConstant;
import org.zstack.kvm.xmlhook.XmlHookVmInstanceRefVO;
import org.zstack.kvm.xmlhook.XmlHookVmInstanceRefVO_;
import org.zstack.mevoco.MevocoConstants;
import org.zstack.mevoco.MevocoGlobalProperty;
import org.zstack.mevoco.MevocoSystemTags;
import org.zstack.mevoco.VolumeQosHelper;
import org.zstack.network.l3.L3NetworkHelper;
import org.zstack.network.securitygroup.*;
import org.zstack.resourceconfig.ResourceConfig;
import org.zstack.resourceconfig.ResourceConfigFacade;
import org.zstack.storage.primary.local.LocalStorageConstants;
import org.zstack.storage.primary.local.LocalStorageResourceRefVO;
import org.zstack.storage.primary.local.LocalStorageResourceRefVO_;
import org.zstack.storage.snapshot.VolumeSnapshotSystemTags;
import org.zstack.storage.volume.VolumeSystemTags;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.SystemTagUtils;
import org.zstack.tag.TagManager;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.WwnUtils;
import org.zstack.utils.function.Function;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6NetworkUtils;
import org.zstack.vmware.ESXConstant;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static org.codehaus.groovy.runtime.InvokerHelper.asList;
import static org.zstack.core.Platform.*;
import static org.zstack.core.progress.ProgressReportService.markTaskStage;
import static org.zstack.header.volume.VolumeQosMode.OVERWRITE;
import static org.zstack.mevoco.DeployMode.zsv;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * Created by mingjian.deng on 16/10/29.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class MevocoVmInstanceBase extends VmInstanceBase {
    protected static final CLogger logger = Utils.getLogger(MevocoVmInstanceBase.class);

    @Autowired
    MevocoVmInstanceBaseFactory mimpl;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    protected AccountManager acntMgr;
    @Autowired
    private ResourceConfigFacade rcf;
    @Autowired
    private VmInstanceManager vmMgr;
    @Autowired
    protected VmInstanceExtensionPointEmitter extEmitter;

    public MevocoVmInstanceBase(VmInstanceVO vo) {
        super(vo);
        allowedOperations.addState(VmInstanceState.Running,
                APIChangeVmPasswordMsg.class.getName(),
                ChangeVmPasswordMsg.class.getName(),
                APIGetVmInstanceFirstBootDeviceMsg.class.getName(),
                CloneVmInstanceMsg.class.getName(),
                APICloneVmInstanceMsg.class.getName());
        allowedOperations.addState(VmInstanceState.Stopped,
                APIChangeVmImageMsg.class.getName(),
                CloneVmInstanceMsg.class.getName(),
                APICloneVmInstanceMsg.class.getName());
        allowedOperations.addState(VmInstanceState.Paused,
                CloneVmInstanceMsg.class.getName(),
                APICloneVmInstanceMsg.class.getName());
    }

    @MessageSafe
    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    protected void handleApiMessage(APIMessage msg){
        if (msg instanceof APIChangeVmPasswordMsg) {
            handle((APIChangeVmPasswordMsg) msg);
        } else if (msg instanceof APIDeleteNicQosMsg) {
            handle((APIDeleteNicQosMsg) msg);
        } else if (msg instanceof APISetNicQosMsg) {
            handle((APISetNicQosMsg) msg);
        } else if (msg instanceof APIGetNicQosMsg) {
            handle((APIGetNicQosMsg) msg);
        } else if (msg instanceof APISetVmQgaMsg) {
            handle((APISetVmQgaMsg) msg);
        } else if (msg instanceof APIGetVmQgaMsg) {
            handle((APIGetVmQgaMsg) msg);
        } else if (msg instanceof APISetVmUsbRedirectMsg) {
            handle((APISetVmUsbRedirectMsg) msg);
        } else if (msg instanceof APIGetVmUsbRedirectMsg) {
            handle((APIGetVmUsbRedirectMsg) msg);
        } else if (msg instanceof APISetVmRDPMsg) {
            handle((APISetVmRDPMsg) msg);
        } else if (msg instanceof APIGetVmRDPMsg) {
            handle((APIGetVmRDPMsg) msg);
        } else if (msg instanceof APISetVmMonitorNumberMsg) {
            handle((APISetVmMonitorNumberMsg) msg);
        } else if (msg instanceof APIGetVmMonitorNumberMsg) {
            handle((APIGetVmMonitorNumberMsg) msg);
        } else if (msg instanceof APIGetImageCandidatesForVmToChangeMsg) {
            handle((APIGetImageCandidatesForVmToChangeMsg) msg);
        } else if (msg instanceof APIChangeVmImageMsg) {
            handle((APIChangeVmImageMsg) msg);
        } else if (msg instanceof APIUpdateVmNicMacMsg) {
            handle((APIUpdateVmNicMacMsg) msg);
        } else if (msg instanceof APISetVmConsoleModeMsg) {
            handle((APISetVmConsoleModeMsg) msg);
        } else if (msg instanceof APISetVmCleanTrafficMsg) {
            handle((APISetVmCleanTrafficMsg) msg);
        } else if (msg instanceof APIGetVmInstanceFirstBootDeviceMsg) {
            handle((APIGetVmInstanceFirstBootDeviceMsg) msg);
        } else if (msg instanceof APISetVmSecurityLevelMsg) {
            handle((APISetVmSecurityLevelMsg) msg);
        } else if (msg instanceof APIDeleteVmUserDefinedXmlMsg) {
            handle((APIDeleteVmUserDefinedXmlMsg) msg);
        } else if (msg instanceof APISetVmUserDefinedXmlMsg) {
            handle((APISetVmUserDefinedXmlMsg) msg);
        } else if (msg instanceof APIGetVmXmlMsg) {
            handle((APIGetVmXmlMsg) msg);
        } else if (msg instanceof APIDeleteVmUserDefinedXmlHookScriptMsg) {
            handle((APIDeleteVmUserDefinedXmlHookScriptMsg) msg);
        } else if (msg instanceof APISetVmUserDefinedXmlHookScriptMsg) {
            handle((APISetVmUserDefinedXmlHookScriptMsg) msg);
        } else if (msg instanceof APIGetVmXmlHookScriptMsg) {
            handle((APIGetVmXmlHookScriptMsg) msg);
        } else if (msg instanceof APISetVmNumaMsg) {
            handle((APISetVmNumaMsg) msg);
        } else if (msg instanceof APIGetVmNumaMsg) {
            handle((APIGetVmNumaMsg) msg);
        } else if (msg instanceof APISetVmEmulatorPinningMsg) {
            handle((APISetVmEmulatorPinningMsg) msg);
        } else if (msg instanceof APIGetVmEmulatorPinningMsg) {
            handle((APIGetVmEmulatorPinningMsg) msg);
        } else if (msg instanceof APIGetVmvNUMATopologyMsg) {
            handle((APIGetVmvNUMATopologyMsg) msg);
        } else if (msg instanceof APISyncVmClockMsg) {
            handle((APISyncVmClockMsg) msg);
        } else if (msg instanceof APIDetachUserDefinedXmlHookScriptFromVmMsg) {
            handle((APIDetachUserDefinedXmlHookScriptFromVmMsg) msg);
        } else if (msg instanceof APIAttachUserDefinedXmlHookScriptToVmMsg) {
            handle((APIAttachUserDefinedXmlHookScriptToVmMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    protected void handleLocalMessage(Message msg) {
        if (msg instanceof CloneVmSyncQosMsg) {
            handle((CloneVmSyncQosMsg) msg);
        } else if (msg instanceof GetImageCandidatesForVmToChangeMsg) {
            handle((GetImageCandidatesForVmToChangeMsg) msg);
        } else if (msg instanceof ChangeVmImageMsg) {
            handle((ChangeVmImageMsg) msg);
        } else if (msg instanceof CloneVmInstanceMsg) {
            handle((CloneVmInstanceMsg) msg);
        } else if (msg instanceof ChangeVmPasswordMsg) {
            handle((ChangeVmPasswordMsg) msg);
        } else if (msg instanceof SyncVmClockMsg) {
            handle((SyncVmClockMsg) msg);
        } else if (msg instanceof SetVmQgaSyncClockTaskMsg) {
            handle((SetVmQgaSyncClockTaskMsg) msg);
        } else if (msg instanceof GetVmNicQosMsg) {
            handle((GetVmNicQosMsg) msg);
        } else if (msg instanceof SetVmNicQosMsg) {
            handle((SetVmNicQosMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APISyncVmClockMsg msg) {
        APISyncVmClockEvent event = new APISyncVmClockEvent(msg.getId());
        SyncVmClockMsg innerMsg = new SyncVmClockMsg();
        innerMsg.setUuid(msg.getUuid());
        bus.makeTargetServiceIdByResourceUuid(innerMsg, MevocoConstants.SERVICE_ID, innerMsg.getVmInstanceUuid());
        bus.send(innerMsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    event.setError(reply.getError());
                }
                bus.publish(event);
            }
        });
    }

    private void handle(SyncVmClockMsg msg) {
        final SyncVmClockReply reply = new SyncVmClockReply();
        if (!VmInstanceState.Running.equals(self.getState())) {
            reply.setError(operr("state of vm[uuid:%s] is not in Running state, can not sync clock"));
            bus.reply(msg, reply);
            return;
        }

        thdf.singleFlightSubmit(new SingleFlightTask(msg)
                .setSyncSignature(String.format("sync-vm-%s-clock-single-flight", msg.getUuid()))
                .run(this::syncVmClockNow)
                .done(((result) -> {
                    if (!result.isSuccess()) {
                        reply.setError(result.getErrorCode());
                    }
                    bus.reply(msg, reply);
                }))
        );
    }

    private void syncVmClockNow(ReturnValueCompletion<Object> completion) {
        MevocoKVMAgentCommands.SyncVmClockCmd cmd = new MevocoKVMAgentCommands.SyncVmClockCmd();
        cmd.setVmUuid(self.getUuid());
        String hostUuid = self.getLastHostUuid();

        KVMHostAsyncHttpCallMsg kmsg = new KVMHostAsyncHttpCallMsg();
        kmsg.setPath(MevocoKVMConstant.SYNC_VM_CLOCK_PATH);
        kmsg.setHostUuid(hostUuid);
        kmsg.setCommand(cmd);
        kmsg.setNoStatusCheck(false);
        bus.makeTargetServiceIdByResourceUuid(kmsg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(kmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                KVMHostAsyncHttpCallReply asyncReply= reply.castReply();
                MevocoKVMAgentCommands.SyncVmClockRsp rsp = asyncReply.toResponse(MevocoKVMAgentCommands.SyncVmClockRsp.class);
                if (!rsp.isSuccess()) {
                    completion.fail(operr(rsp.getError()));
                    return;
                }

                completion.success(null);
            }
        });
    }

    @SuppressWarnings("rawtypes")
    private void handle(SetVmQgaSyncClockTaskMsg msg) {
        final SetVmQgaSyncClockTaskReply reply = new SetVmQgaSyncClockTaskReply();
        final ResourceConfig syncAfterResumeConfig = rcf.getResourceConfig(VmGlobalConfig.VM_CLOCK_SYNC_AFTER_VM_RESUME.getIdentity());
        final ResourceConfig intervalConfig = rcf.getResourceConfig(VmGlobalConfig.VM_CLOCK_SYNC_INTERVAL_IN_SECONDS.getIdentity());
        final String vmUuid = msg.getVmInstanceUuid();

        FlowChain chain = new SimpleFlowChain();
        chain.setName(String.format("set-vm-%s-qga-sync-clock", vmUuid));
        chain.then(new NoRollbackFlow() {
            String __name__ = "refresh-qga-task-in-host";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                // sync clock by QGA
                String hostUuid = Q.New(VmInstanceVO.class)
                        .select(VmInstanceVO_.hostUuid)
                        .eq(VmInstanceVO_.uuid, vmUuid)
                        .findValue();
                if (StringUtils.isEmpty(hostUuid)) {
                    trigger.next();
                    return;
                }

                Map<String, Integer> vmIntervalMap = KvmUserVmClockSyncExtension.buildNeedSyncVmMap(hostUuid);
                final Integer intervalInSeconds = msg.getIntervalInSeconds();
                if (intervalInSeconds != null && !VmClockSyncConstant.DISABLE_VM_CLOCK_SYNC.equals(intervalInSeconds)) {
                    vmIntervalMap.put(msg.getVmInstanceUuid(), intervalInSeconds);
                } else {
                    vmIntervalMap.remove(msg.getVmInstanceUuid());
                }

                UpdateHostClockSyncVmMsg updateMsg = new UpdateHostClockSyncVmMsg();
                updateMsg.setHostUuid(hostUuid);
                // NOTE: empty interval map also need to synchronize data with the host
                updateMsg.setVmIntervalMap(vmIntervalMap);
                bus.makeTargetServiceIdByResourceUuid(updateMsg, HostConstant.SERVICE_ID, hostUuid);
                bus.send(updateMsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                            return;
                        }
                        trigger.fail(reply.getError());
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "update-vm-qga-sync-clock-resource-config";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (msg.getSyncAfterVMResume() != null) {
                    syncAfterResumeConfig.updateValue(vmUuid, msg.getSyncAfterVMResume().toString());
                }
                if (msg.getIntervalInSeconds() != null) {
                    intervalConfig.updateValue(vmUuid, msg.getIntervalInSeconds().toString());
                }
                trigger.next();
            }
        });

        chain.error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errorCode, Map data) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                bus.reply(msg, reply);
            }
        }).start();
    }

    private void handle(APISetVmEmulatorPinningMsg msg) {
        APISetVmEmulatorPinningEvent evt = new APISetVmEmulatorPinningEvent(msg.getId());
        if (!Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid()).isExists() ||Q.New(VmInstanceVO.class).select(VmInstanceVO_.hostUuid).eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid()).findValue()==null){
            setEmulatorPinningSystemTag(msg.getEmulatorPinning());
            bus.publish(evt);
            return;
        }
        ChangeVmEmulatorPinningMsg cmsg = new ChangeVmEmulatorPinningMsg();
        cmsg.setEmulatorPinning(msg.getEmulatorPinning());
        cmsg.setUuid(msg.getUuid());
        ChangeVmEmulatorPinningReply lmsg = new ChangeVmEmulatorPinningReply();
        bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, cmsg.getHostUuid());
        bus.send(cmsg, new CloudBusCallBack(lmsg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    evt.setError(err(VmErrors.NOT_IN_CORRECT_STATE, reply.getError(), reply.getError().getDetails()));
                    bus.publish(evt);
                    return;
                }
                setEmulatorPinningSystemTag(msg.getEmulatorPinning());
                bus.publish(evt);
            }
        });
    }

    private void setEmulatorPinningSystemTag(String emulatorPinningValue){
        if (Objects.equals(emulatorPinningValue, "")){
            MevocoVmSystemTags.VM_EMULATOR_PINNING.delete(self.getUuid());
        }else {
            String cpus = StringUtils.join(CpuRangeSet.originValueOf(emulatorPinningValue), ",");
            SystemTagCreator creator = MevocoVmSystemTags.VM_EMULATOR_PINNING.newSystemTagCreator(self.getUuid());
            creator.setTagByTokens(map(e(MevocoVmSystemTags.VM_EMULATOR_PINNING_TOKEN, cpus)));
            creator.recreate = true;
            creator.create();
        }
    }

    private void handle(APIGetVmEmulatorPinningMsg msg) {
        String emulatorPinning = "";
        APIGetVmEmulatorPinningReply reply = new APIGetVmEmulatorPinningReply() ;
        if (MevocoVmSystemTags.VM_EMULATOR_PINNING.hasTag(self.getUuid())){
            emulatorPinning = MevocoVmSystemTags.VM_EMULATOR_PINNING.getTokenByResourceUuid(self.getUuid(),
                    MevocoVmSystemTags.VM_EMULATOR_PINNING_TOKEN);
        }
        reply.setEmulatorPinning(emulatorPinning);
        bus.reply(msg, reply);
    }

    private void handle(APISetVmNumaMsg msg) {
        APISetVmNumaEvent evt = new APISetVmNumaEvent(msg.getId());
        SystemTagCreator creator = MevocoVmSystemTags.VM_NUMA_ENABLE.newSystemTagCreator(self.getUuid());
        if (!msg.isEnable()) {
            dbf.removeCollection(Q.New(VmInstanceNumaNodeVO.class)
                    .eq(VmInstanceNumaNodeVO_.vmUuid,msg.getUuid()).list(), VmInstanceNumaNodeVO.class);
            String clusterUuid = Q.New(VmInstanceVO.class).select(VmInstanceVO_.clusterUuid)
                    .eq(VmInstanceVO_.uuid,msg.getUuid()).findValue();
            boolean isOvsDpdkSup = rcf.getResourceConfigValue(MevocoClusterGlobalConfig.OVS_DPDK_SUPPORT, clusterUuid, Boolean.class);
            if (isOvsDpdkSup) {
                ResourceConfig numaConfig = rcf.getResourceConfig(VmGlobalConfig.NUMA.getIdentity());
                numaConfig.deleteValue(msg.getUuid());
            }
            MevocoVmSystemTags.VM_NUMA_ENABLE.delete(self.getUuid());
        } else {
            boolean numa = rcf.getResourceConfigValue(VmGlobalConfig.NUMA, msg.getUuid(), Boolean.class);
            if (numa) {
                evt.setError(operr("hot plug is not turned off,can not open vm numa"));
                bus.publish(evt);
                return;
            } else {
                ResourceConfig numaConfig = rcf.getResourceConfig(VmGlobalConfig.NUMA.getIdentity());
                numaConfig.updateValue(self.getUuid(),Boolean.FALSE.toString());
            }
            creator.setTagByTokens(map(e(MevocoVmSystemTags.VM_NUMA_ENABLE_TOKEN, Boolean.TRUE.toString())));
            creator.recreate = true;
            creator.create();
        }
        bus.publish(evt);
    }


    private void handle(APIGetVmNumaMsg msg) {
        APIGetVmNumaReply reply = new APIGetVmNumaReply() ;
        String vnumaEnable = MevocoVmSystemTags.VM_NUMA_ENABLE.getTokenByResourceUuid(self.getUuid(),
                MevocoVmSystemTags.VM_NUMA_ENABLE_TOKEN);
        if (vnumaEnable == null){
            reply.setEnable(false);
        }else{
            reply.setEnable(Boolean.parseBoolean(vnumaEnable));
        }
        bus.reply(msg, reply);
    }

    private void handle(APIGetVmvNUMATopologyMsg msg) {
        APIGetVmvNUMATopologyReply reply = new APIGetVmvNUMATopologyReply();

        VmInstanceVO vm = dbf.findByUuid(msg.getUuid(), VmInstanceVO.class);

        List<Map<String, Object>> topology = new ArrayList<>();

        SimpleQuery<VmInstanceNumaNodeVO> vmQuery = dbf.createQuery(VmInstanceNumaNodeVO.class);
        vmQuery.add(VmInstanceNumaNodeVO_.vmUuid, SimpleQuery.Op.EQ, msg.getUuid());
        List<VmInstanceNumaNodeVO> tuples = vmQuery.list();
        if (!tuples.isEmpty()) {
            for (VmInstanceNumaNodeVO vNode: tuples) {
                Map<String, Object> node = new HashMap<>();
                node.put("nodeID", vNode.getvNodeID());
                node.put("phyNodeID", vNode.getpNodeID());
                node.put("memSize", vNode.getvNodeMemSize());

                String CPUString = vNode.getvNodeCPUs();
                node.put("CPUsID", Arrays.asList(CPUString.split(",")));

                String distanceString = vNode.getvNodeDistance();
                node.put("distance", Arrays.asList(distanceString.split(",")));

                topology.add(node);
            }
        }

        reply.setHostUuid(vm.getHostUuid());
        reply.setName(vm.getName());
        reply.setTopology(topology);
        reply.setUuid(msg.getUuid());
        bus.reply(msg, reply);
    }


    private void handle(APIDeleteVmUserDefinedXmlMsg msg) {
        APIDeleteVmUserDefinedXmlEvent event = new APIDeleteVmUserDefinedXmlEvent(msg.getId());

        UserDefinedXmlHelper.removeUserDefinedVmXmlIfExists(msg.getVmInstanceUuid());
        bus.publish(event);
    }

    private void handle(APISetVmUserDefinedXmlMsg msg) {
        APISetVmUserDefinedXmlEvent event = new APISetVmUserDefinedXmlEvent(msg.getId());
        UserDefinedXmlHelper.removeUserDefinedVmXmlIfExists(msg.getVmInstanceUuid());

        if (UserDefinedXmlHelper.VmXmlHookScriptExists(msg.getVmInstanceUuid())) {
            throw new CloudRuntimeException("there is a xml hook script on this vm, can not set user defined xml when xml hook script exists");
        }

        try {
            DocumentHelper.parseText(new String(Base64.decodeBase64(msg.getXmlBase64())));
        } catch (DocumentException e) {
            throw new CloudRuntimeException("can not parse xml", e);
        }

        new JsonLabel().create(UserDefinedXmlHelper.getUserDefinedVmXmlLabelKey(msg.getVmInstanceUuid()), msg.getXmlBase64());
        event.setVmUserDefinedXml(new String(Base64.decodeBase64(msg.getXmlBase64())));

        bus.publish(event);
    }

    private void handle(APIGetVmXmlMsg msg) {
        APIGetVmXmlReply reply = new APIGetVmXmlReply();
        JsonLabelInventory label = UserDefinedXmlHelper.getUserDefinedVmXmlBase64(msg.getVmInstanceUuid());
        if (label != null) {
            reply.setUserDefinedXml(new String(Base64.decodeBase64(label.getLabelValue())));
        }

        VmInstanceState state = Q.New(VmInstanceVO.class)
                .select(VmInstanceVO_.state).eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid()).findValue();
        if (Arrays.asList(VmInstanceState.Running, VmInstanceState.Paused).contains(state)) {
            //TODO(weiw) not implemented
            reply.setRunningXml(reply.getUserDefinedXml());
        }

        if (reply.getRunningXml() != null && reply.getUserDefinedXml() != null) {
            reply.setMatch(reply.getRunningXml().trim().equalsIgnoreCase(reply.getUserDefinedXml().trim()));
        }

        bus.reply(msg, reply);
    }

    private void handle(APIDeleteVmUserDefinedXmlHookScriptMsg msg) {
        APIDeleteVmUserDefinedXmlHookScriptEvent event = new APIDeleteVmUserDefinedXmlHookScriptEvent(msg.getId());

        UserDefinedXmlHookScriptHelper.removeUserDefinedVmXmlHookScriptIfExists(msg.getVmInstanceUuid());
        bus.publish(event);
    }

    private void handle(APISetVmUserDefinedXmlHookScriptMsg msg) {
        APISetVmUserDefinedXmlHookScriptEvent event = new APISetVmUserDefinedXmlHookScriptEvent(msg.getId());
        UserDefinedXmlHookScriptHelper.removeUserDefinedVmXmlHookScriptIfExists(msg.getVmInstanceUuid());

        if (UserDefinedXmlHookScriptHelper.VmUserDefinedXmlExists(msg.getVmInstanceUuid())) {
            throw new CloudRuntimeException("there is a user defined xml on this vm, can not set xml hook script when user defined xml exists");
        }

        new JsonLabel().create(UserDefinedXmlHookScriptHelper.getUserDefinedVmXmlHookScriptLabelKey(msg.getVmInstanceUuid()), msg.getXmlHookScriptBase64());
        event.setVmUserDefinedXmlHookScript(new String(Base64.decodeBase64(msg.getXmlHookScriptBase64())));

        bus.publish(event);
    }

    private void handle(APIDetachUserDefinedXmlHookScriptFromVmMsg msg) {
        APIDetachUserDefinedXmlHookScriptFromVmEvent event = new APIDetachUserDefinedXmlHookScriptFromVmEvent(msg.getId());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                detachUserDefinedXmlHookScriptFromVm(msg.getVmInstanceUuid(), msg.getStartupStrategy(), new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(event);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        event.setError(errorCode);
                        bus.publish(event);
                        chain.next();
                    }
                });
            }

            @Override
            public String getSyncSignature() {
                return String.format("%s:%s", XmlHookConstant.OPERATE_VM_WITH_HOOK, msg.getVmInstanceUuid());
            }

            @Override
            public String getName() {
                return String.format("detach-user-defined-xml-hook-script-from-vm-%s", msg.getVmInstanceUuid());
            }
        });
    }

    private void detachUserDefinedXmlHookScriptFromVm(String vmInstanceUuid, String startupStrategy, Completion completion) {
        String xmlHookUuid = Q.New(XmlHookVmInstanceRefVO.class)
                .select(XmlHookVmInstanceRefVO_.xmlHookUuid)
                .eq(XmlHookVmInstanceRefVO_.vmInstanceUuid, vmInstanceUuid)
                .findValue();
        if (xmlHookUuid == null) {
            throw new CloudRuntimeException(String.format("there is no xml hook script on the vm[uuid: %s]", vmInstanceUuid));
        }

        FlowChain chain = new SimpleFlowChain();
        chain.setName(String.format("detach-user-defined-xml-hook-%s-script-to-vm-%s", xmlHookUuid, vmInstanceUuid));
        chain.then(new NoRollbackFlow() {
            String __name__ = "refresh-db";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                XmlHookVmInstanceRefVO refVO = Q.New(XmlHookVmInstanceRefVO.class)
                        .eq(XmlHookVmInstanceRefVO_.vmInstanceUuid, vmInstanceUuid)
                        .find();
                dbf.remove(refVO);
                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "startup-vm";

            @Override
            public boolean skip(Map data) {
                return !Objects.equals(startupStrategy, VmInstanceConstant.VmOperation.Reboot.toString()) || self.getState() == VmInstanceState.Destroyed;
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                CheckAndStartVmInstanceMsg msg = new CheckAndStartVmInstanceMsg();
                msg.setVmInstanceUuid(self.getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, self.getUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                        } else {
                            trigger.fail(operr("xml hook[uuid: %s] detached from vm[uuid:%s] successfully, but failed to restart vm. details is: %s",
                                    xmlHookUuid, self.getUuid(), reply.getError().getDetails()));
                        }
                    }
                });
            }
        });
        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void handle(APIAttachUserDefinedXmlHookScriptToVmMsg msg) {
        APIAttachUserDefinedXmlHookScriptToVmEvent event = new APIAttachUserDefinedXmlHookScriptToVmEvent(msg.getId());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                attachUserDefinedXmlHookScriptToVm(msg.getVmInstanceUuid(), msg.getXmlHookUuid(), msg.getStartupStrategy(), new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(event);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        event.setError(errorCode);
                        bus.publish(event);
                        chain.next();
                    }
                });
            }

            @Override
            public String getSyncSignature() {
                return String.format("%s:%s", XmlHookConstant.OPERATE_VM_WITH_HOOK, msg.getVmInstanceUuid());
            }

            @Override
            public String getName() {
                return String.format("attach-user-defined-xml-hook-script-%s-to-vm-%s", msg.getXmlHookUuid(), msg.getVmInstanceUuid());
            }
        });
    }

    private void attachUserDefinedXmlHookScriptToVm(String vmInstanceUuid, String xmlHookUuid, String startupStrategy, Completion completion) {
        FlowChain chain = new SimpleFlowChain();
        chain.setName(String.format("attach-user-defined-xml-hook-%s-script-to-vm-%s", xmlHookUuid, vmInstanceUuid));
        chain.then(new NoRollbackFlow() {
            String __name__ = "refresh-db";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                XmlHookVmInstanceRefVO refVO = new XmlHookVmInstanceRefVO();
                refVO.setVmInstanceUuid(vmInstanceUuid);
                refVO.setXmlHookUuid(xmlHookUuid);
                dbf.persist(refVO);
                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "startup-vm";

            @Override
            public boolean skip(Map data) {
                return !Objects.equals(startupStrategy, VmInstanceConstant.VmOperation.Reboot.toString());
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                CheckAndStartVmInstanceMsg msg = new CheckAndStartVmInstanceMsg();
                msg.setVmInstanceUuid(self.getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, self.getUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                        } else {
                            trigger.fail(operr("xml hook[uuid: %s] attached to vm[uuid:%s] successfully, but failed to restart vm. details is: %s",
                                    xmlHookUuid, self.getUuid(), reply.getError().getDetails()));
                        }
                    }
                });
            }
        });
        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void handle(APIGetVmXmlHookScriptMsg msg) {
        APIGetVmXmlHookScriptReply reply = new APIGetVmXmlHookScriptReply();
        JsonLabelInventory label = UserDefinedXmlHookScriptHelper.getUserDefinedVmXmlHookScriptBase64(msg.getVmInstanceUuid());
        if (label != null) {
            reply.setUserDefinedXmlHookScript(new String(Base64.decodeBase64(label.getLabelValue())));
        }

        bus.reply(msg, reply);
    }

    private void handle(APISetVmSecurityLevelMsg msg) {
        APISetVmSecurityLevelEvent evt = new APISetVmSecurityLevelEvent(msg.getId());

        if (msg.getSecurityLevel() != null) {
            SystemTagCreator creator = MevocoVmSystemTags.SECURITY_LEVEL.newSystemTagCreator(self.getUuid());
            creator.setTagByTokens(map(e(MevocoVmSystemTags.SECURITY_LEVEL_TOKEN, msg.getSecurityLevel())));
            creator.recreate = true;
            creator.create();
        } else {
            MevocoVmSystemTags.SECURITY_LEVEL.delete(self.getUuid());
        }

        bus.publish(evt);
    }

    private void handle(GetImageCandidatesForVmToChangeMsg msg) {
        GetImageCandidatesForVmToChangeReply reply = new GetImageCandidatesForVmToChangeReply();
        reply.setInventories(getImageCandidatesForVm(ImageConstant.ImageMediaType.RootVolumeTemplate));
        bus.reply(msg, reply);
    }

    // change vm's root volume using user choosed image
    private void doChangeVmImage(final ChangeVmImageMsg msg, final SyncTaskChain chain) {
        ChangeVmImageReply reply = new ChangeVmImageReply();
        self = dbf.reload(self);
        if (self.getState() != VmInstanceState.Stopped) {
            reply.setError(operr("vm[uuid: %s]'s state is not Stopped now, cannot operate 'changevmimage' action", self.getUuid()));
            bus.reply(msg, reply);
            chain.next();
            return;
        }
        // VmSpec to be updated
        VmInstanceInventory vmInv = VmInstanceInventory.valueOf(dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class));
        VmInstanceSpec vmSpec = buildSpecFromInventory(vmInv, VmInstanceConstant.VmOperation.ChangeImage);

        // remove all data volumes from vmSpec
        vmSpec.getVmInventory().setAllVolumes(
                vmSpec.getVmInventory().getAllVolumes().stream().filter(
                        v -> v.getType().equals(VolumeType.Root.toString()))
                        .collect(Collectors.toList())
        );
        vmSpec.getVmInventory().setHostUuid(null);

        // use new image
        VmInstanceSpec.ImageSpec imageSpec = new VmInstanceSpec.ImageSpec();
        imageSpec.setInventory(ImageInventory.valueOf(dbf.findByUuid(msg.getImageUuid(), ImageVO.class)));
        vmSpec.setImageSpec(imageSpec);

        // discard these info in vmSpec
        vmSpec.setVolumeSpecs(new ArrayList<>());
        vmSpec.setDestDataVolumes(Collections.emptyList());
        vmSpec.setDataDiskOfferings(Collections.emptyList());
        vmSpec.setDestHost(null);

        // use the same primary storage as before changing
        String psUuid = Q.New(VolumeVO.class)
                .select(VolumeVO_.primaryStorageUuid)
                .eq(VolumeVO_.uuid, vmSpec.getVmInventory().getRootVolumeUuid())
                .findValue();
        vmSpec.setRequiredPrimaryStorageUuidForRootVolume(psUuid);

        // if root volume on LocalStorage, then allocate last host again
        String psType = Q.New(PrimaryStorageVO.class)
                .select(PrimaryStorageVO_.type)
                .eq(PrimaryStorageVO_.uuid, psUuid)
                .findValue();
        if (psType.equals(LocalStorageConstants.LOCAL_STORAGE_TYPE)) {
            String hostUuid = Q.New(LocalStorageResourceRefVO.class)
                    .select(LocalStorageResourceRefVO_.hostUuid)
                    .eq(LocalStorageResourceRefVO_.resourceUuid, vmInv.getRootVolumeUuid())
                    .findValue();
            HostVO hvo = dbf.findByUuid(hostUuid, HostVO.class);
            vmSpec.setDestHost(HostInventory.valueOf(hvo));
            vmSpec.getVmInventory().setHostUuid(hostUuid);
            vmSpec.setHostAllocatorStrategy(HostAllocatorConstant.DESIGNATED_HOST_ALLOCATOR_STRATEGY_TYPE);
        } else if (vmSpec.getDestHost() == null) {
            // randomly select host in the cluster if last host doesn't exist
            String clusterUuid = vmSpec.getVmInventory().getClusterUuid();
            if (clusterUuid == null) {
                reply.setError(operr("vm[uuid:%s] cluster uuid is null, cannot change image for it", self.getUuid()));
                bus.reply(msg, reply);
                chain.next();
                return;
            }

            HostVO host = Q.New(HostVO.class)
                    .eq(HostVO_.clusterUuid, clusterUuid)
                    .eq(HostVO_.hypervisorType, self.getHypervisorType())
                    .eq(HostVO_.state, HostState.Enabled)
                    .eq(HostVO_.status, HostStatus.Connected)
                    .limit(1).find();
            if (host == null) {
                reply.setError(operr("vm[uuid:%s] is in cluster[uuid:%s], but there is no available host in the cluster, " +
                        "cannot change image for the vm", self.getUuid(), self.getClusterUuid()));
                bus.reply(msg, reply);
                chain.next();
                return;
            }

            vmSpec.setDestHost(HostInventory.valueOf(host));
        }

        if (!CollectionUtils.isEmpty(msg.getSystemTags())) {
            vmSpec.setRootVolumeSystemTags(vmSpec.getRootVolumeSystemTags() == null ? new ArrayList<>() : vmSpec.getRootVolumeSystemTags());
            vmSpec.getRootVolumeSystemTags().addAll(msg.getSystemTags());
        }

        // sync guest tools tag based on new image
        String guestTools = ImageSystemTags.IMAGE_GUEST_TOOLS.getTokenByResourceUuid(msg.getImageUuid(), ImageSystemTags.IMAGE_GUEST_TOOLS_VERSION_TOKEN);
        if (guestTools == null) {
            if (VmSystemTags.VM_GUEST_TOOLS.hasTag(msg.getVmInstanceUuid())) {
                VmSystemTags.VM_GUEST_TOOLS.delete(msg.getVmInstanceUuid());
            }
        } else {
            SystemTagCreator creator = VmSystemTags.VM_GUEST_TOOLS.newSystemTagCreator(self.getUuid());
            creator.setTagByTokens(Collections.singletonMap(VmSystemTags.VM_GUEST_TOOLS_VERSION_TOKEN, guestTools));
            creator.inherent = false;
            creator.recreate = true;
            creator.create();
        }

        if (VolumeSystemTags.VOLUME_PROVISIONING_STRATEGY.hasTag(vmSpec.getVmInventory().getRootVolumeUuid(), VolumeVO.class)) {
            if (vmSpec.getRootVolumeSystemTags() == null) {
                vmSpec.setRootVolumeSystemTags(new ArrayList<>());
            }
            vmSpec.getRootVolumeSystemTags().add(VolumeSystemTags.VOLUME_PROVISIONING_STRATEGY.getTag(vmSpec.getVmInventory().getRootVolumeUuid(), VolumeVO.class));
        }

        FlowChain fchain = getChangeVmImageWorkFlowChain();
        fchain.setFlowMarshaller(new FlowMarshaller() {
            @Override
            public Flow marshalTheNextFlow(String previousFlowClassName, String nextFlowClassName, FlowChain chain, Map data) {
                Flow nflow = null;
                for (MarshalVmOperationFlowExtensionPoint mext : pluginRgty.getExtensionList(MarshalVmOperationFlowExtensionPoint.class)) {
                    VmInstanceSpec spec = (VmInstanceSpec) data.get(VmInstanceConstant.Params.VmInstanceSpec.toString());
                    nflow = mext.marshalVmOperationFlow(previousFlowClassName, nextFlowClassName, chain, spec);
                    if (nflow != null) {
                        logger.debug(String.format("a VM[uuid: %s, operation: %s] operation flow[%s] is changed to the flow[%s] by %s",
                                self.getUuid(), spec.getCurrentVmOperation(), nextFlowClassName, nflow.getClass(), mext.getClass()));
                        break;
                    }
                }
                return nflow;
            }
        });
        fchain.setName(String.format("change-vm-%s-image-to-%s", msg.getVmInstanceUuid(), msg.getImageUuid()));
        fchain.getData().put(VmInstanceConstant.Params.VmInstanceSpec.toString(), vmSpec);
        fchain.getData().put(VmInstanceConstant.Params.DeletionPolicy, VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.Direct);
        fchain.getData().put("uuid", msg.getResourceUuid());
        fchain.done(new FlowDoneHandler(chain) {
            @Override
            public void handle(Map data) {
                logger.info(String.format("successfully changed vm[uuid:%s] image to %s",
                        msg.getVmInstanceUuid(), msg.getImageUuid()));
                extEmitter.cleanUpAfterVmChangeImage(vmInv);
                reply.setInventory(VmInstanceInventory.valueOf(dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class)));
                bus.reply(msg, reply);
                chain.next();
            }
        }).error(new FlowErrorHandler(chain) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                logger.error(String.format("failed to change vm[uuid:%s] to image %s",
                        msg.getVmInstanceUuid(), msg.getImageUuid()));
                reply.setError(errCode);
                bus.reply(msg, reply);
                chain.next();
            }
        }).start();
    }

    private void handle(ChangeVmImageMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return syncThreadName;
            }

            @Override
            public void run(SyncTaskChain chain) {
                doChangeVmImage(msg, chain);
            }

            @Override
            public String getName() {
                return String.format("change-vm-%s-root-image", msg.getVmInstanceUuid());
            }
        });
    }

    private static class VolumeImageSpec {
        String volumeQos;
        VolumeType type;
        ImageInventory image;
        VolumeSnapshotInventory snapshot;
        int deviceId;
    }

    private void handle(CloneVmInstanceMsg msg) {
        ErrorCode err = validateOperationByState(msg, self.getState(), SysErrors.OPERATION_ERROR);
        if (err != null) {
            throw new OperationFailureException(err);
        }

        VmInstanceVO vivo = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        if (vivo.getHypervisorType().equals(ESXConstant.VMWARE_HYPERVISOR_TYPE)) {
            handleESXClone(msg, vivo);
        } else {
            handleKVMClone(msg, vivo);
        }
    }

    private void handleESXClone(CloneVmInstanceMsg msg, VmInstanceVO vivo) {
        final CloneVmInstanceResults results = new CloneVmInstanceResults();
        List<CloneVmOnHypervisorMsg> hmsgs = new ArrayList<>();
        msg.getNames().forEach(name -> {
            CloneVmOnHypervisorMsg hmsg = new CloneVmOnHypervisorMsg();
            hmsg.setVmInventory(VmInstanceInventory.valueOf(vivo));
            hmsg.setName(name);
            hmsg.setStrategy(msg.getStrategy());
            hmsg.setAccountUuid(msg.getSession().getAccountUuid());
            bus.makeTargetServiceIdByResourceUuid(hmsg, HostConstant.SERVICE_ID, hmsg.getHostUuid());
            hmsgs.add(hmsg);
        });

        new While<>(hmsgs).all((hmsg, completion) -> {
            bus.send(hmsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    CloneVmInstanceInventory inv = new CloneVmInstanceInventory();
                    if (reply.isSuccess()) {
                        CloneVmOnHypervisorReply creply = reply.castReply();
                        inv.setInventory(creply.getInventory());
                    } else {
                        inv.setError(reply.getError());
                    }
                    results.addVmInstanceInventory(inv);
                    completion.done();
                }
            });

        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                CloneVmInstanceReply reply = new CloneVmInstanceReply();
                results.resetNumberOfCloneVm();
                reply.setResults(results);
                bus.reply(msg, reply);
            }
        });
    }

    private void handleKVMClone(CloneVmInstanceMsg msg, VmInstanceVO vivo) {
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("clone-vm-%s", msg.getVmInstanceUuid()));
        chain.then(new ShareFlow() {
            final CloneVmInstanceResults results = new CloneVmInstanceResults();
            final Set<VolumeVO> volumesToClone = msg.isFull() ? vivo.getAllDiskVolumes() : Collections.singleton(vivo.getRootVolume());
            final Map<String, VolumeImageSpec> volumeImages = new HashMap<>();
            final Map<String, Long> volumeActualSize = new ConcurrentHashMap<>();
            final Map<String, String> selectedBackupStorageUuids = new HashMap<>();
            boolean fastCreate = msg.hasSystemTag(VolumeSystemTags.FAST_CREATE::isMatch);

            @Override
            public void setup() {
                setupPreAllocateHostFlows();
                setupPrepareCreateImagesFlows();
                boolean cloneDataVolumes = volumesToClone.size() > 1;
                boolean preferSnapshotGroup = zsv.name().equals(MevocoGlobalProperty.DEPLOY_MODE) && msg.isFull();

                if (cloneDataVolumes || preferSnapshotGroup) {
                    setupCreateVolumeSnapshotGroupFlows();
                } else {
                    setupCreateRootVolumeSnapshotFlows();
                }

                setupCreateVolumesImageFlows();

                setupCreateVmAndSyncTagFlows();

                if (cloneDataVolumes) {
                    setupAttachDataVolumesFlows();
                }

                if (!VmCreationStrategy.JustCreate.name().equals(msg.getStrategy())) {
                    setupSyncQosFlows();
                }

                done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        CloneVmInstanceReply reply = new CloneVmInstanceReply();
                        results.getInventoriesWithoutError().forEach(cloneInv -> {
                            VmInstanceVO vmInstanceVO = Q.New(VmInstanceVO.class)
                                    .eq(VmInstanceVO_.uuid, cloneInv.getInventory().getUuid())
                                    .find();
                            cloneInv.setInventory(VmInstanceInventory.valueOf(vmInstanceVO));
                        });

                        List<ImageInventory> tempImages = volumeImages.values().stream()
                                .map(it -> it.image).filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        results.resetNumberOfCloneVm();

                        reply.setResults(results);
                        reply.setTempImages(tempImages);
                        bus.reply(msg, reply);
                    }
                });

                error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        CloneVmInstanceReply reply = new CloneVmInstanceReply();
                        reply.setError(errCode);
                        bus.reply(msg, reply);
                    }
                });
            }

            private void setupPreAllocateHostFlows() {
                flow(new NoRollbackFlow() {
                    @Override
                    public boolean skip(Map data) {
                        return VmCreationStrategy.JustCreate.name().equals(msg.getStrategy());
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        DesignatedAllocateHostMsg amsg = new DesignatedAllocateHostMsg();
                        amsg.setHostUuid(msg.getHostUuid());
                        amsg.setClusterUuid(msg.getClusterUuid());
                        amsg.setCpuCapacity(self.getCpuNum());
                        amsg.setMemoryCapacity(self.getMemorySize());
                        if (msg.getVmNicParms().isEmpty()) {
                            amsg.setL3NetworkUuids(msg.getVmNicParms().stream()
                                    .map(VmNicParam::getL3NetworkUuid).collect(Collectors.toList()));
                        } else {
                            amsg.setL3NetworkUuids(VmNicHelper.getL3Uuids(VmNicInventory.valueOf(self.getVmNics())));
                        }

                        amsg.setAllowNoL3Networks(true);
                        amsg.setVmInstance(VmInstanceInventory.valueOf(self));
                        amsg.setZoneUuid(self.getZoneUuid());
                        amsg.setDryRun(true);
                        amsg.setAllocatorStrategy(HostAllocatorConstant.DESIGNATED_HOST_ALLOCATOR_STRATEGY_TYPE);
                        bus.makeLocalServiceId(amsg, HostAllocatorConstant.SERVICE_ID);
                        bus.send(amsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }
                                AllocateHostDryRunReply reply1 = (AllocateHostDryRunReply) reply;
                                if (reply1.getHosts().isEmpty()) {
                                    trigger.fail(err(HostAllocatorError.NO_AVAILABLE_HOST, "unable to allocate hosts, no host meets the following conditions: " +
                                                    "clusterUuid=%s hostUuid=%s cpu=%d memoryCapacity=%d L3NetworkUuids=%s",
                                            amsg.getClusterUuids(), amsg.getHostUuid(), amsg.getCpuCapacity(), amsg.getMemoryCapacity(), amsg.getL3NetworkUuids()));
                                } else {
                                    trigger.next();
                                }
                            }
                        });
                    }
                });
            }

            private void setupPrepareCreateImagesFlows() {
                flow(new NoRollbackFlow() {
                    final String __name__ = "calculate-all-volumes-actual-size";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        TaskProgressRange COMMIT_SNAPSHOTS_TO_IMAGES = new TaskProgressRange(20, 25);
                        markTaskStage(COMMIT_SNAPSHOTS_TO_IMAGES);

                        ErrorCodeList errList = new ErrorCodeList();
                        new While<>(volumesToClone).step((volume, whileCompletion) -> {
                            SyncVolumeSizeMsg smsg = new SyncVolumeSizeMsg();
                            smsg.setVolumeUuid(volume.getUuid());
                            bus.makeTargetServiceIdByResourceUuid(smsg, VolumeConstant.SERVICE_ID, volume.getPrimaryStorageUuid());
                            bus.send(smsg, new CloudBusCallBack(trigger) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        errList.getCauses().add(reply.getError());
                                        whileCompletion.done();
                                        return;
                                    }

                                    SyncVolumeSizeReply sr = reply.castReply();
                                    volumeActualSize.put(smsg.getVolumeUuid(), sr.getActualSize());
                                    whileCompletion.done();
                                }
                            });
                        }, 3).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (errList.getCauses().isEmpty()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(errList.getCauses().get(0));
                                }
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "allocate-bs";

                    @Override
                    public boolean skip(Map data) {
                        return fastCreate;
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        new While<>(volumesToClone).each((vol, compl) -> {
                            SelectBackupStorageMsg smsg = new SelectBackupStorageMsg();
                            smsg.setVolumeUuid(vol.getUuid());
                            smsg.setRequiredSize(volumeActualSize.getOrDefault(smsg.getVolumeUuid(), 0L));
                            smsg.setPrimaryStorageUuid(vol.getPrimaryStorageUuid());

                            String requiredPsUuid = vol.getType() == VolumeType.Root ?
                                    msg.getPrimaryStorageUuidForRootVolume() :
                                    msg.getPrimaryStorageUuidForDataVolume();
                            if (requiredPsUuid != null) {
                                smsg.setRequiredBackupStorageTypes(getRequiredBackupStorageTypes(requiredPsUuid));
                            }
                            bus.makeLocalServiceId(smsg, PrimaryStorageConstant.SERVICE_ID);
                            bus.send(smsg, new CloudBusCallBack(trigger) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        compl.addError(reply.getError());
                                        compl.allDone();
                                        return;
                                    }

                                    SelectBackupStorageReply sr = reply.castReply();
                                    if (sr.getInventory() == null) {
                                        compl.addError(operr("can not find backup storage, " +
                                                "unable to commit volume snapshot[psUuid:%s] as image, " +
                                                "destination required PS uuid:%s", vol.getPrimaryStorageUuid(), requiredPsUuid
                                        ));

                                        compl.allDone();
                                        return;
                                    }

                                    selectedBackupStorageUuids.put(smsg.getVolumeUuid(), sr.getInventory().getUuid());
                                    compl.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (!errorCodeList.getCauses().isEmpty()) {
                                    trigger.fail(errorCodeList.getCauses().get(0));
                                    return;
                                }

                                trigger.next();
                            }
                        });
                    }
                });
            }

            private void setupCreateRootVolumeSnapshotFlows() {
                flow(new NoRollbackFlow() {
                    String __name__ = String.format("create-root-volume-snapshot-for-clone-vm-%s", msg.getVmInstanceUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {

                        VolumeCreateSnapshotMsg cmsg = new VolumeCreateSnapshotMsg();
                        cmsg.setVolumeUuid(vivo.getRootVolumeUuid());
                        cmsg.setName(String.format("for-clone-vm-%s", msg.getNames().get(0)));
                        cmsg.setAccountUuid(msg.getSession().getAccountUuid());
                        bus.makeTargetServiceIdByResourceUuid(cmsg, VolumeConstant.SERVICE_ID, vivo.getRootVolume().getUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                VolumeCreateSnapshotReply creply = reply.castReply();
                                handleVolumeSnapshotCreate(creply.getInventory(), 0);

                                CloneVmCanonicalEvents.VmInnerSnapshotCreated createdEvent = new CloneVmCanonicalEvents.VmInnerSnapshotCreated();
                                createdEvent.primaryStorageUuid = vivo.getRootVolume().getPrimaryStorageUuid();
                                createdEvent.vmInstanceUuid = vivo.getUuid();
                                createdEvent.volumeSnapshot = creply.getInventory();
                                createdEvent.fire();
                                trigger.next();
                            }
                        });
                    }
                });
            }

            private void setupCreateVolumeSnapshotGroupFlows() {
                flow(new NoRollbackFlow() {
                    String __name__ = String.format("create-volume-snapshot-group-for-clone-vm-%s", msg.getVmInstanceUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        TaskProgressRange BULK_CREATE_SNAPSHOT = new TaskProgressRange(0, 20);
                        markTaskStage(BULK_CREATE_SNAPSHOT);

                        CreateVolumeSnapshotGroupMsg cmsg = new CreateVolumeSnapshotGroupMsg();
                        cmsg.setRootVolumeUuid(vivo.getRootVolumeUuid());
                        cmsg.setVmInstance(VmInstanceInventory.valueOf(vivo));
                        cmsg.setConsistentType(ConsistentType.None);
                        cmsg.setName("snapshot-group-for-clone");
                        cmsg.setDescription(String.format("bulk snapshot for clone vm[%s]", vivo.getUuid()));
                        cmsg.setSession(msg.getSession());

                        bus.makeTargetServiceIdByResourceUuid(cmsg, VolumeConstant.SERVICE_ID, cmsg.getVolumeUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                CreateVolumeSnapshotGroupReply cr = reply.castReply();
                                VolumeSnapshotGroupInventory group = cr.getInventory();

                                logger.debug(String.format("created volume snapshot group %s", group.getUuid()));
                                for (VolumeSnapshotGroupRefInventory ref : group.getVolumeSnapshotRefs()) {
                                    handleVolumeSnapshotCreate(VolumeSnapshotInventory.valueOf(ref), ref.getDeviceId());
                                }

                                CloneVmCanonicalEvents.VmInnerSnapshotCreated createdEvent = new CloneVmCanonicalEvents.VmInnerSnapshotCreated();
                                createdEvent.primaryStorageUuid = vivo.getRootVolume().getPrimaryStorageUuid();
                                createdEvent.vmInstanceUuid = vivo.getUuid();
                                createdEvent.snapshotInventoryList = group.getVolumeSnapshotRefs();
                                createdEvent.fire();

                                trigger.next();
                            }
                        });
                    }
                });
            }

            private void handleVolumeSnapshotCreate(VolumeSnapshotInventory snapshot, int deviceId) {
                VolumeImageSpec spec = new VolumeImageSpec();
                tagMgr.createNonInherentSystemTag(snapshot.getUuid(),
                        VolumeSnapshotSystemTags.VOLUMESNAPSHOT_CREATED_BY_SYSTEM.getTagFormat(),
                        VolumeSnapshotVO.class.getSimpleName());
                spec.snapshot = snapshot;
                spec.type = VolumeType.valueOf(snapshot.getVolumeType());
                spec.volumeQos = vivo.getVolume(it -> it.getUuid().equals(snapshot.getVolumeUuid())).getVolumeQos();
                spec.deviceId = deviceId;
                volumeImages.put(snapshot.getVolumeUuid(), spec);
            }

            private void setupCreateVolumesImageFlows() {
                flow(new Flow() {
                    String __name__ = String.format("create-volumes-image-for-clone-vm-%s", msg.getVmInstanceUuid());

                    final List<String> rollbackImageUuids = new ArrayList<>();

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        TaskProgressRange COMMIT_SNAPSHOTS_TO_IMAGES = new TaskProgressRange(25, 40);
                        markTaskStage(COMMIT_SNAPSHOTS_TO_IMAGES);

                        new While<>(volumeImages.values()).all((spec, whileCompletion) -> {
                            if (spec.type == VolumeType.Data && fastCreate) {
                                whileCompletion.done();
                                return;
                            }

                            AddImageMessage cmsg = buildMsg(spec.snapshot);
                            rollbackImageUuids.add(cmsg.getResourceUuid());

                            bus.send((NeedReplyMessage) cmsg, new CloudBusCallBack(whileCompletion) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        whileCompletion.addError(reply.getError());
                                        whileCompletion.done();
                                        return;
                                    }

                                    spec.image = ((ImageReply) reply).getInventory();
                                    tagMgr.createNonInherentSystemTag(spec.image.getUuid(),
                                            ImageSystemTags.IMAGE_CREATED_BY_SYSTEM.getTagFormat(),
                                            ImageVO.class.getSimpleName());

                                    whileCompletion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (errorCodeList.getCauses().isEmpty()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(errorCodeList.getCauses().get(0));
                                }
                            }
                        });
                    }

                    private AddImageMessage buildMsg(VolumeSnapshotInventory snap) {
                        AddImageMessage cmsg;
                        if (snap.getVolumeUuid().equals(vivo.getRootVolume().getUuid())) {
                            CreateRootVolumeTemplateMessage csmsg = fastCreate ?
                                    new CreateTemporaryRootVolumeTemplateFromVolumeSnapshotMsg() :
                                    new CreateRootVolumeTemplateFromVolumeSnapshotMsg();

                            csmsg.setName(String.format("for-clone-vm-%s-rootVolume-%s", vivo.getUuid(),
                                    snap.getVolumeUuid()));
                            csmsg.setPlatform(vivo.getPlatform());
                            csmsg.setArchitecture(vivo.getArchitecture());
                            csmsg.setVirtio(VmExtraInfoGetter.New(vivo.getUuid()).isVirtio());
                            csmsg.setGuestOsType(vivo.getGuestOsType());
                            cmsg = csmsg;
                        } else {
                            CreateDataVolumeTemplateFromVolumeSnapshotMsg csmsg = new CreateDataVolumeTemplateFromVolumeSnapshotMsg();
                            csmsg.setName(String.format("for-clone-vm-%s-volume-%s",
                                    vivo.getUuid(), snap.getVolumeUuid()));
                            cmsg = csmsg;
                        }

                        ((CreateTemplateFromSnapshotMessage) cmsg).setSnapshotUuid(snap.getUuid());
                        cmsg.setSession(msg.getSession());
                        cmsg.setResourceUuid(getUuid());
                        cmsg.setBackupStorageUuids(Collections.singletonList(selectedBackupStorageUuids.get(snap.getVolumeUuid())));
                        if (fastCreate) {
                            cmsg.addSystemTag(VolumeSystemTags.FAST_CREATE.getTagFormat());
                        }

                        bus.makeLocalServiceId((NeedReplyMessage) cmsg, ImageConstant.SERVICE_ID);
                        return cmsg;
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        new While<>(rollbackImageUuids).each((imageUuid, coml) -> {
                            ImageDeletionMsg dmsg = new ImageDeletionMsg();
                            dmsg.setImageUuid(imageUuid);
                            dmsg.setForceDelete(true);
                            dmsg.setDeletionPolicy(ImageDeletionPolicyManager.ImageDeletionPolicy.Direct.toString());
                            bus.makeTargetServiceIdByResourceUuid(dmsg, ImageConstant.SERVICE_ID, dmsg.getImageUuid());
                            bus.send(dmsg, new CloudBusCallBack(coml) {
                                @Override
                                public void run(MessageReply reply) {
                                    coml.done();
                                }
                            });

                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                trigger.rollback();
                            }
                        });
                    }
                });
            }

            private void setupCreateVmAndSyncTagFlows() {
                // both full and non-full clone will take the bellow flows
                flow(new NoRollbackFlow() {
                    String __name__ = String.format("cloning-vm-instance-%s", msg.getVmInstanceUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        TaskProgressRange CREATE_VM_INSTANCES = new TaskProgressRange(40, 45);
                        markTaskStage(CREATE_VM_INSTANCES);

                        List<CreateVmInstanceMsg> cmsgs = CollectionUtils.transformToList(msg.getNames(), new Function<CreateVmInstanceMsg, String>() {
                            @Override
                            public CreateVmInstanceMsg call(String arg) {
                                CreateVmInstanceMsg cvmsg = new CreateVmInstanceMsg();

                                cvmsg.setImageUuid(volumeImages.get(vivo.getRootVolumeUuid()).image.getUuid());
                                if (vivo.getAllocatorStrategy() != null) {
                                    cvmsg.setAllocatorStrategy(vivo.getAllocatorStrategy());
                                } else {
                                    cvmsg.setAllocatorStrategy(HostAllocatorConstant.LEAST_VM_PREFERRED_HOST_ALLOCATOR_STRATEGY_TYPE);
                                }
                                cvmsg.setAccountUuid(msg.getSession().getAccountUuid());
                                cvmsg.setName(arg);
                                cvmsg.setSystemTags(new ArrayList<>());

                                List<VmNicSpec> nicSpecs = new ArrayList<>();
                                List<String> disableL3Networks = new ArrayList<>();
                                String defaultL3Uuid = vivo.getDefaultL3NetworkUuid();
                                List<VmNicVO> sortedVmNics = vivo.getVmNics().stream()
                                        .sorted(Comparator.comparingInt(VmNicVO::getDeviceId))
                                        .collect(Collectors.toList());
                                if (msg.getVmNicParms().isEmpty()) {
                                    for (VmNicVO nic : sortedVmNics) {
                                        Set<String> l3Uuids = new HashSet<>();
                                        // Dirty fix to set up nic default l3 uuid work around clone recovered vm
                                        // jira: ZSTAC-16069
                                        l3Uuids.add(nic.getL3NetworkUuid());

                                        l3Uuids.addAll(nic.getUsedIps().stream().map(UsedIpVO::getL3NetworkUuid).filter(Objects::nonNull).collect(Collectors.toSet()));

                                        if (!l3Uuids.isEmpty()) {
                                            List<L3NetworkInventory> l3Invs = L3NetworkInventory.valueOf(dbf.listByPrimaryKeys(l3Uuids, L3NetworkVO.class));
                                            nicSpecs.add(new VmNicSpec(l3Invs, nic.getDriverType()));
                                        }
                                        if (nic.getState().equals(VmNicState.disable)) {
                                            disableL3Networks.add(nic.getL3NetworkUuid());
                                        }
                                    }

                                    List<VmNicSecurityGroupRefVO> refVOS = Q.New(VmNicSecurityGroupRefVO.class)
                                            .eq(VmNicSecurityGroupRefVO_.vmInstanceUuid, vivo.getUuid())
                                            .orderBy(VmNicSecurityGroupRefVO_.priority, SimpleQuery.Od.ASC)
                                            .list();
                                    Map<String, List<String>> nicSguuidsMap = new HashMap<>();
                                    for (VmNicSecurityGroupRefVO refVO : refVOS) {
                                        nicSguuidsMap.computeIfAbsent(refVO.getVmNicUuid(), v -> new ArrayList<>());
                                        nicSguuidsMap.get(refVO.getVmNicUuid()).add(refVO.getSecurityGroupUuid());
                                    }
                                    for (Map.Entry<String, List<String>>  entry : nicSguuidsMap.entrySet()) {
                                        VmNicVO nic = dbf.findByUuid(entry.getKey(), VmNicVO.class);
                                        cvmsg.getSystemTags().add(VmSystemTags.L3_NETWORK_SECURITY_GROUP_UUIDS_REF.instantiateTag(
                                                map(e(VmSystemTags.L3_UUID_TOKEN, nic.getL3NetworkUuid()),
                                                        e(VmSystemTags.SECURITY_GROUP_UUIDS_TOKEN, String.join(",", entry.getValue())))));
                                        VmNicSecurityPolicyVO policyVO = Q.New(VmNicSecurityPolicyVO.class).eq(VmNicSecurityPolicyVO_.vmNicUuid, nic.getUuid()).find();
                                        /* vmnic default policy has been changed */
                                        if (policyVO != null && (!policyVO.getIngressPolicy().equals(VmNicSecurityPolicy.DENY.toString())
                                                || !policyVO.getEgressPolicy().equals(VmNicSecurityPolicy.ALLOW.toString()))) {
                                            cvmsg.getSystemTags().add(VmSystemTags.SECURITY_GROUP_POLICY.instantiateTag(
                                                    map(e(VmSystemTags.L3_UUID_TOKEN, nic.getL3NetworkUuid()),
                                                            e(VmSystemTags.SECURITY_GROUP_INGRESS_POLICY_TOKEN, policyVO.getIngressPolicy()),
                                                            e(VmSystemTags.SECURITY_GROUP_EGRESS_POLICY_TOKEN, policyVO.getEgressPolicy()))));
                                        }
                                    }

                                    for (VmNicVO nic : sortedVmNics) {
                                        VmNicType nicType = VmNicType.valueOf(nic.getType());
                                        if (nicType.isUseSRIOV()) {
                                            cvmsg.getSystemTags().add(SriovSystemTags.L3_ENABLE_SRIOV.instantiateTag(
                                                    map(e(SriovSystemTags.ENABLE_SRIOV_TOKEN, SriovSystemTags.ENABLE_SRIOV_TOKEN),
                                                            e(SriovSystemTags.ENABLE_SRIOV_L3_UUID_TOKEN, nic.getL3NetworkUuid()))));
                                        }
                                    }

                                } else {
                                    HashMap<String, VmNicVO> nicMap = new HashMap<>();
                                    for (VmNicVO nicVO : sortedVmNics) {
                                        nicMap.put(nicVO.getL3NetworkUuid(), nicVO);
                                    }
                                    for (VmNicParam param : msg.getVmNicParms()) {
                                        VmNicVO oldNic = nicMap.get(param.getL3NetworkUuid());

                                        if (param.getDefaultNic()) {
                                            defaultL3Uuid = param.getL3NetworkUuid();
                                        }

                                        if (param.getMac() != null) {
                                            cvmsg.getSystemTags().add(VmSystemTags.CUSTOM_MAC.instantiateTag(
                                                    map(e(VmSystemTags.STATIC_IP_L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                            e(VmSystemTags.MAC_TOKEN, param.getMac()))));
                                        }

                                        if (param.getState().equals(VmNicState.disable.toString())) {
                                            disableL3Networks.add(param.getL3NetworkUuid());
                                        }

                                        VmNicType nicType = VmNicType.valueOf(param.getVmNicType());
                                        if (nicType.isUseSRIOV()) {
                                            cvmsg.getSystemTags().add(SriovSystemTags.L3_ENABLE_SRIOV.instantiateTag(
                                                    map(e(SriovSystemTags.ENABLE_SRIOV_TOKEN, SriovSystemTags.ENABLE_SRIOV_TOKEN),
                                                            e(SriovSystemTags.ENABLE_SRIOV_L3_UUID_TOKEN, param.getL3NetworkUuid()))));
                                        }

                                        List<String> sgUuids = new ArrayList<>();
                                        /* admin maybe add some sg to user vm, and user don't know it.
                                        *  when clone vm, these sg should be add to vm */
                                        if (oldNic != null && !acntMgr.isAdmin(msg.getSession())) {
                                            List<String> ownSgUuids = acntMgr.getResourceUuidsCanAccessByAccount(msg.getSession().getAccountUuid(),
                                                    SecurityGroupVO.class);
                                            List<VmNicSecurityGroupRefVO> refVOS = Q.New(VmNicSecurityGroupRefVO.class)
                                                    .eq(VmNicSecurityGroupRefVO_.vmInstanceUuid, vivo.getUuid())
                                                    .eq(VmNicSecurityGroupRefVO_.vmNicUuid, oldNic.getUuid())
                                                    .orderBy(VmNicSecurityGroupRefVO_.priority, SimpleQuery.Od.ASC)
                                                    .list();
                                            for (VmNicSecurityGroupRefVO refVO : refVOS) {
                                                if (ownSgUuids != null && !ownSgUuids.contains(refVO.getSecurityGroupUuid())) {
                                                    sgUuids.add(refVO.getSecurityGroupUuid());
                                                }
                                            }
                                        }
                                        sgUuids.addAll(param.getSgUuids());

                                        if (!sgUuids.isEmpty()) {
                                            cvmsg.getSystemTags().add(VmSystemTags.L3_NETWORK_SECURITY_GROUP_UUIDS_REF.instantiateTag(
                                                    map(e(VmSystemTags.L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                            e(VmSystemTags.SECURITY_GROUP_UUIDS_TOKEN, String.join(",", sgUuids)))));
                                        }

                                        if (!sgUuids.isEmpty() && oldNic != null) {
                                            VmNicSecurityPolicyVO policyVO = Q.New(VmNicSecurityPolicyVO.class).eq(VmNicSecurityPolicyVO_.vmNicUuid, oldNic.getUuid()).find();
                                            /* vmnic default policy has been changed */
                                            if (policyVO != null && (!policyVO.getIngressPolicy().equals(VmNicSecurityPolicy.DENY.toString())
                                                    || !policyVO.getEgressPolicy().equals(VmNicSecurityPolicy.ALLOW.toString()))) {
                                                cvmsg.getSystemTags().add(VmSystemTags.SECURITY_GROUP_POLICY.instantiateTag(
                                                        map(e(VmSystemTags.L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                                e(VmSystemTags.SECURITY_GROUP_INGRESS_POLICY_TOKEN, policyVO.getIngressPolicy()),
                                                                e(VmSystemTags.SECURITY_GROUP_EGRESS_POLICY_TOKEN, policyVO.getEgressPolicy()))));
                                            }
                                        }

                                        if (param.getIp() != null) {
                                            cvmsg.getSystemTags().add(VmSystemTags.STATIC_IP.instantiateTag(
                                                    map(e(VmSystemTags.STATIC_IP_L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                            e(VmSystemTags.STATIC_IP_TOKEN, param.getIp()))));
                                        }

                                        if (param.getIp6() != null) {
                                            String ip6Tag = IPv6NetworkUtils.ipv6AddessToTagValue(param.getIp6());
                                            cvmsg.getSystemTags().add(VmSystemTags.STATIC_IP.instantiateTag(
                                                    map(e(VmSystemTags.STATIC_IP_L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                            e(VmSystemTags.STATIC_IP_TOKEN, ip6Tag))));
                                        }

                                        L3NetworkInventory l3Inv = L3NetworkInventory.valueOf(
                                                dbf.findByUuid(param.getL3NetworkUuid(), L3NetworkVO.class));
                                        if (!l3Inv.enableIpAddressAllocation()) {
                                            if (param.getNetmask() != null) {
                                                cvmsg.getSystemTags().add(VmSystemTags.IPV4_NETMASK.instantiateTag(
                                                        map(e(VmSystemTags.IPV4_NETMASK_L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                                e(VmSystemTags.IPV4_NETMASK_TOKEN, param.getNetmask()))));
                                            }

                                            if (param.getGateway() != null) {
                                                cvmsg.getSystemTags().add(VmSystemTags.IPV4_GATEWAY.instantiateTag(
                                                        map(e(VmSystemTags.IPV4_GATEWAY_L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                                e(VmSystemTags.IPV4_GATEWAY_TOKEN, param.getGateway()))));
                                            }

                                            if (param.getPrefix6() != null) {
                                                cvmsg.getSystemTags().add(VmSystemTags.IPV6_PREFIX.instantiateTag(
                                                        map(e(VmSystemTags.IPV6_PREFIX_L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                                e(VmSystemTags.IPV6_PREFIX_TOKEN, param.getPrefix6()))));
                                            }

                                            if (param.getGateway6() != null) {
                                                String gw6Tag = IPv6NetworkUtils.ipv6AddessToTagValue(param.getGateway6());
                                                cvmsg.getSystemTags().add(VmSystemTags.IPV6_GATEWAY.instantiateTag(
                                                        map(e(VmSystemTags.IPV6_GATEWAY_L3_UUID_TOKEN, param.getL3NetworkUuid()),
                                                                e(VmSystemTags.IPV6_GATEWAY_TOKEN, gw6Tag))));
                                            }
                                        }
                                        nicSpecs.add(new VmNicSpec(asList(l3Inv), param.getDriverType()));
                                    }

                                }

                                cvmsg.setClusterUuid(msg.getClusterUuid());
                                cvmsg.setHostUuid(msg.getHostUuid());
                                cvmsg.setL3NetworkSpecs(nicSpecs);
                                cvmsg.setDisableL3Networks(disableL3Networks);
                                cvmsg.setDefaultL3NetworkUuid(defaultL3Uuid);
                                cvmsg.setType(vivo.getType());
                                cvmsg.setZoneUuid(vivo.getZoneUuid());
                                cvmsg.setInstanceOfferingUuid(vivo.getInstanceOfferingUuid());
                                cvmsg.setMemorySize(vivo.getMemorySize());
                                cvmsg.setCpuNum(vivo.getCpuNum());
                                cvmsg.setCpuSpeed(vivo.getCpuSpeed());
                                cvmsg.setDescription(String.format("cloned from vm[uuid:%s]", vivo.getUuid()));
                                cvmsg.setRootVolumeSystemTags(msg.getRootVolumeSystemTags());

                                List<VmCdRomVO> cdRomVOS = Q.New(VmCdRomVO.class)
                                        .eq(VmCdRomVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                                        .list();
                                if (cdRomVOS.isEmpty()) {
                                    cvmsg.getSystemTags().add(VmSystemTags.CREATE_WITHOUT_CD_ROM.instantiateTag(map(e(VmSystemTags.CREATE_WITHOUT_CD_ROM_TOKEN, true))));
                                } else {
                                    String[] cdRomConfigs = {
                                            VmInstanceConstant.NONE_CDROM,
                                            VmInstanceConstant.NONE_CDROM,
                                            VmInstanceConstant.NONE_CDROM,
                                    };
                                    for (VmCdRomVO cdRomVO : cdRomVOS) {
                                        String config = cdRomVO.getIsoUuid() != null ? cdRomVO.getIsoUuid() : VmInstanceConstant.EMPTY_CDROM;
                                        cdRomConfigs[cdRomVO.getDeviceId()] = config;
                                    }
                                    cvmsg.getSystemTags().add(VmSystemTags.CREATE_VM_CD_ROM_LIST.instantiateTag(
                                            map(e(VmSystemTags.CD_ROM_0, cdRomConfigs[0]),
                                                    e(VmSystemTags.CD_ROM_1, cdRomConfigs[1]),
                                                    e(VmSystemTags.CD_ROM_2, cdRomConfigs[2]))
                                    ));
                                }

                                if (msg.isFull() || fastCreate) {
                                    cvmsg.setPrimaryStorageUuidForRootVolume(vivo.getRootVolume().getPrimaryStorageUuid());
                                }
                                if (msg.getPrimaryStorageUuidForRootVolume() != null) {
                                    cvmsg.setPrimaryStorageUuidForRootVolume(msg.getPrimaryStorageUuidForRootVolume());
                                }
                                if (msg.getSystemTags() != null && !msg.getSystemTags().isEmpty()) {
                                    cvmsg.getSystemTags().addAll(msg.getSystemTags());
                                }

                                cvmsg.setStrategy(VmCreationStrategy.JustCreate.toString());
                                bus.makeLocalServiceId(cvmsg, VmInstanceConstant.SERVICE_ID);
                                return cvmsg;
                            }
                        });

                        new While<>(cmsgs).all((cmsg, completion) -> {
                            bus.send(cmsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    CloneVmInstanceInventory inv = new CloneVmInstanceInventory();
                                    if (reply.isSuccess()) {
                                        CreateVmInstanceReply vmreply = reply.castReply();
                                        inv.setInventory(vmreply.getInventory());
                                    } else {
                                        inv.setError(reply.getError());
                                    }
                                    results.addVmInstanceInventory(inv);
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                trigger.next();
                            }
                        });
                    }
                });
                // TODO(weiw): need code refactoring
                flow(new NoRollbackFlow() {
                    String __name__ = String.format("sync-tags-from-origin-vm-%s-before-start", msg.getVmInstanceUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        VmInstanceInventory origin = VmInstanceInventory.valueOf(vivo);
                        for (CloneVmInstanceInventory inventory : results.getInventoriesWithoutError()) {
                            VmInstanceInventory destVm = inventory.getInventory();

                            SystemTagUtils.cloneTag(VmSystemTags.VM_PRIORITY, VmSystemTags.VM_PRIORITY_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(MevocoVmSystemTags.VM_CPU_PINNING, MevocoVmSystemTags.VM_CPU_PINNING_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(MevocoSystemTags.VM_CONSOLE_MODE, MevocoSystemTags.VM_CONSOLE_MODE_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.RDP_ENABLE, VmSystemTags.RDP_ENABLE_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.USB_REDIRECT, VmSystemTags.USB_REDIRECT_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.SECURITY_ELEMENT_ENABLE, VmSystemTags.SECURITY_ELEMENT_ENABLE_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.USERDATA, VmSystemTags.USERDATA_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.SSHKEY, VmSystemTags.SSHKEY_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.CONSOLE_PASSWORD, VmSystemTags.CONSOLE_PASSWORD_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.ROOT_PASSWORD, VmSystemTags.ROOT_PASSWORD_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.VDI_MONITOR_NUMBER, VmSystemTags.VDI_MONITOR_NUMBER_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.MACHINE_TYPE, VmSystemTags.MACHINE_TYPE_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(HaSystemTags.HA, HaSystemTags.HA_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmSystemTags.VM_VRING_BUFFER_SIZE, origin.getUuid(), destVm.getUuid(), VmSystemTags.RX_SIZE_TOKEN, VmSystemTags.TX_SIZE_TOKEN);
                            SystemTagUtils.cloneTag(VmSystemTags.CREATED_BY_MARKETPLACE, origin.getUuid(), destVm.getUuid());

                            // clone vm cpu topology tags
                            SystemTagUtils.cloneTag(VmHardwareSystemTags.CPU_SOCKETS, VmHardwareSystemTags.CPU_SOCKETS_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmHardwareSystemTags.CPU_CORES, VmHardwareSystemTags.CPU_CORES_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(VmHardwareSystemTags.CPU_THREADS, VmHardwareSystemTags.CPU_THREADS_TOKEN, origin.getUuid(), destVm.getUuid());

                            cloneResourceConfig(VmGlobalConfig.VM_NIC_MULTIQUEUE_NUM, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(MevocoVmSystemTags.VM_NUMA_ENABLE, MevocoVmSystemTags.VM_NUMA_ENABLE_TOKEN, origin.getUuid(), destVm.getUuid());
                            SystemTagUtils.cloneTag(MevocoVmSystemTags.VM_EMULATOR_PINNING, MevocoVmSystemTags.VM_EMULATOR_PINNING_TOKEN, origin.getUuid(), destVm.getUuid());
                            cloneResourceConfig(VmGlobalConfig.VM_CLOCK_TRACK, origin.getUuid(), destVm.getUuid());
                            cloneResourceConfig(VmGlobalConfig.VM_CLOCK_SYNC_INTERVAL_IN_SECONDS, origin.getUuid(), destVm.getUuid());
                            cloneResourceConfig(VmGlobalConfig.VM_CLOCK_SYNC_AFTER_VM_RESUME, origin.getUuid(), destVm.getUuid());
                            cloneResourceConfig(KVMGlobalConfig.NESTED_VIRTUALIZATION, origin.getUuid(), destVm.getUuid());
                            cloneResourceConfig(KVMGlobalConfig.VM_HYPERV_CLOCK_FEATURE, origin.getUuid(), destVm.getUuid());
                            cloneResourceConfig(KVMGlobalConfig.VM_CPU_HYPERVISOR_FEATURE, origin.getUuid(), destVm.getUuid());

                        }
                        trigger.next();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("clone-ssh-key-pairs-from-origin-vm-%s-before-start", msg.getVmInstanceUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        VmInstanceInventory origin = VmInstanceInventory.valueOf(vivo);
                        for (CloneVmInstanceInventory inventory : results.getInventoriesWithoutError()) {
                            VmInstanceInventory destVm = inventory.getInventory();

                            extEmitter.cloneSshKeyPairsToVm(origin.getUuid(), destVm.getUuid());
                        }
                        trigger.next();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "start-vm";

                    @Override
                    public boolean skip(Map data) {
                        return VmCreationStrategy.JustCreate.name().equals(msg.getStrategy());
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        new While<>(results.getInventoriesWithoutError()).all((inv, completion) -> {
                            StartVmInstanceMsg startVmInstanceMsg = new StartVmInstanceMsg();
                            startVmInstanceMsg.setAccountUuid(msg.getSession().getAccountUuid());
                            startVmInstanceMsg.setVmInstanceUuid(inv.getInventory().getUuid());
                            startVmInstanceMsg.setStartPaused(VmCreationStrategy.CreateStopped.name().equals(msg.getStrategy()) ||
                                    VmCreationStrategy.CreatedPaused.name().equals(msg.getStrategy()));
                            bus.makeTargetServiceIdByResourceUuid(startVmInstanceMsg, VmInstanceConstant.SERVICE_ID, inv.getInventory().getUuid());
                            bus.send(startVmInstanceMsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        logger.debug(String.format("vm %s start failed", startVmInstanceMsg.getVmInstanceUuid()));
                                        inv.setError(reply.getError());
                                        completion.done();
                                        return;
                                    }

                                    StartVmInstanceReply reply1 = reply.castReply();
                                    inv.setInventory(reply1.getInventory());
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                trigger.next();
                            }
                        });
                    }
                });
            }

            private void setupAttachDataVolumesFlows() {
                flow(new NoRollbackFlow() {
                    String __name__ = String.format("stop-cloning-vm-%s-for-attach-volume", msg.getVmInstanceUuid());

                    @Override
                    public boolean skip(Map data) {
                        return !ImagePlatform.Other.name().equals(volumeImages.get(vivo.getRootVolumeUuid()).image.getPlatform());
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        new While<>(results.getInventoriesWithoutError()).all((inv, completion) -> {
                            String vmUuid = inv.getInventory().getUuid();
                            String level = HaSystemTags.HA.getTokenByResourceUuid(vmUuid, HaSystemTags.HA_TOKEN);
                            if (StringUtils.equals(VmHaLevel.NeverStop.toString(), level)) {
                                SystemTagCreator creator = HaSystemTags.INHIBIT_HA.newSystemTagCreator(vmUuid);
                                creator.inherent = false;
                                creator.recreate = true;
                                creator.create();
                            }

                            StopVmInstanceMsg stopMsg = new StopVmInstanceMsg();
                            stopMsg.setVmInstanceUuid(inv.getInventory().getUuid());
                            stopMsg.setGcOnFailure(true);
                            stopMsg.setType(StopVmType.cold.toString());
                            bus.makeTargetServiceIdByResourceUuid(stopMsg, VmInstanceConstant.SERVICE_ID, inv.getInventory().getUuid());
                            bus.send(stopMsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        logger.debug(String.format("vm %s stop failed", inv.getInventory().getUuid()));
                                        inv.setError(reply.getError());
                                        completion.done();
                                        return;
                                    }

                                    InstantiateNewCreatedVmInstanceReply reply1 = reply.castReply();
                                    inv.setInventory(reply1.getVmInventory());
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("attach-data-volumes-for-cloning-vm-%s", msg.getVmInstanceUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        ErrorCodeList errList = new ErrorCodeList();
                        int maxDeviceId = Collections.max(volumeImages.values(),
                                Comparator.comparing(it -> it.deviceId)).deviceId;

                        new While<>(results.getInventoriesWithoutError()).each((vmInstanceInventory, whileCompleteion) -> {
                            logger.debug("volumeImages here: " + JSONObjectUtil.toJsonString(volumeImages));

                            VolumeInventory[] dataVolumesIndexByDeviceId = new VolumeInventory[maxDeviceId + 1];
                            List<VolumeImageSpec> dataVolImageSpecs = volumeImages.values().stream()
                                    .filter(it -> it.type == VolumeType.Data)
                                    .collect(Collectors.toList());

                            new While<>(dataVolImageSpecs).all((spec, whileCompleteion1) -> {
                                NeedReplyMessage cmsg = buildCreateDataVolMsg(spec, vmInstanceInventory.getInventory());
                                bus.makeLocalServiceId(cmsg, VolumeConstant.SERVICE_ID);
                                bus.send(cmsg, new CloudBusCallBack(whileCompleteion1) {
                                    @Override
                                    public void run(MessageReply reply) {
                                        if (!reply.isSuccess()) {
                                            errList.getCauses().add(reply.getError());
                                            whileCompleteion1.done();
                                            return;
                                        }

                                        CreateDataVolumeReply cr = reply.castReply();
                                        if (spec.volumeQos != null) {
                                            SQL.New(VolumeVO.class).eq(VolumeVO_.uuid, cr.getInventory().getUuid())
                                                    .set(VolumeVO_.volumeQos, spec.volumeQos)
                                                    .update();
                                        }

                                        dataVolumesIndexByDeviceId[spec.deviceId] = cr.getInventory();
                                        cloneVolumeTag(spec.snapshot.getVolumeUuid(), cr.getInventory().getUuid());
                                        whileCompleteion1.done();
                                    }
                                });
                            }).run(new WhileDoneCompletion(whileCompleteion) {
                                @Override
                                public void done(ErrorCodeList errorCodeList) {
                                    if (!errList.getCauses().isEmpty()) {
                                        whileCompleteion.done();
                                        return;
                                    }

                                    List<VolumeInventory> dataVolumes = Arrays.stream(dataVolumesIndexByDeviceId)
                                            .filter(Objects::nonNull)
                                            .collect(Collectors.toList());
                                    new While<>(dataVolumes).each((dataVolume, whileCompleteion2) -> {
                                        AttachDataVolumeToVmMsg amsg = new AttachDataVolumeToVmMsg();
                                        amsg.setVolume(dataVolume);
                                        amsg.setVmInstanceUuid(vmInstanceInventory.getInventory().getUuid());
                                        bus.makeLocalServiceId(amsg, VmInstanceConstant.SERVICE_ID);
                                        bus.send(amsg, new CloudBusCallBack(whileCompleteion2) {
                                            @Override
                                            public void run(MessageReply reply) {
                                                if (!reply.isSuccess()) {
                                                    errList.getCauses().add(reply.getError());
                                                }

                                                whileCompleteion2.done();
                                            }
                                        });
                                    }).run(new WhileDoneCompletion(whileCompleteion) {
                                        @Override
                                        public void done(ErrorCodeList errorCodeList) {
                                            whileCompleteion.done();
                                        }
                                    });
                                }
                            });

                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (!errList.getCauses().isEmpty()) {
                                    trigger.fail(errList.getCauses().get(0));
                                } else {
                                    trigger.next();
                                }
                            }
                        });
                    }

                    private NeedReplyMessage buildCreateDataVolMsg(VolumeImageSpec spec, VmInstanceInventory vm) {
                        VolumeVO originVol = vivo.getVolume(it -> it.getUuid().equals(spec.snapshot.getVolumeUuid()));
                        if (fastCreate) {
                            CreateDataVolumeFromVolumeSnapshotMsg cmsg = new CreateDataVolumeFromVolumeSnapshotMsg();
                            cmsg.setName(originVol.getName() + "-clone");
                            cmsg.setDescription(String.format("for-clone-vm-%s-volume-%s", vivo.getUuid(),
                                    spec.snapshot.getVolumeUuid()));
                            cmsg.setVolumeSnapshotUuid(spec.snapshot.getUuid());
                            cmsg.setSystemTags(msg.getDataVolumeSystemTags());
                            cmsg.addSystemTag(VolumeSystemTags.FAST_CREATE.getTagFormat());
                            cmsg.setSession(msg.getSession());
                            return cmsg;
                        } else {
                            CreateDataVolumeFromVolumeTemplateMsg cmsg = new CreateDataVolumeFromVolumeTemplateMsg();
                            cmsg.setResourceUuid(getUuid());
                            cmsg.setAccountUuid(msg.getSession().getAccountUuid());
                            cmsg.setHostUuid(vm.getHostUuid() != null ? vm.getHostUuid() : vm.getLastHostUuid());
                            cmsg.setImageUuid(spec.image.getUuid());
                            cmsg.setName(originVol.getName() + "-clone");
                            cmsg.setDescription(String.format("for-clone-vm-%s-volume-%s", vivo.getUuid(),
                                    spec.snapshot.getVolumeUuid()));
                            if (msg.getPrimaryStorageUuidForDataVolume() != null) {
                                cmsg.setPrimaryStorageUuid(msg.getPrimaryStorageUuidForDataVolume());
                            } else {
                                String psUuid = originVol.getPrimaryStorageUuid();
                                cmsg.setPrimaryStorageUuid(psUuid);
                            }
                            cmsg.setSystemTags(msg.getDataVolumeSystemTags());
                            return cmsg;
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("start-cloning-vm-%s-after-attach-volume", msg.getVmInstanceUuid());

                    @Override
                    public boolean skip(Map data) {
                        return !ImagePlatform.Other.name().equals(volumeImages.get(vivo.getRootVolumeUuid()).image.getPlatform())
                                || VmCreationStrategy.CreateStopped.toString().equals(msg.getStrategy());
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        new While<>(results.getInventoriesWithoutError()).all((inv, completion) -> {
                            HaSystemTags.INHIBIT_HA.delete(inv.getInventory().getUuid());

                            StartVmInstanceMsg startVmInstanceMsg = new StartVmInstanceMsg();
                            startVmInstanceMsg.setAccountUuid(msg.getSession().getAccountUuid());
                            startVmInstanceMsg.setVmInstanceUuid(inv.getInventory().getUuid());
                            startVmInstanceMsg.setStartPaused(VmCreationStrategy.CreateStopped.name().equals(msg.getStrategy()) ||
                                    VmCreationStrategy.CreatedPaused.name().equals(msg.getStrategy()));
                            bus.makeTargetServiceIdByResourceUuid(startVmInstanceMsg, VmInstanceConstant.SERVICE_ID, inv.getInventory().getUuid());
                            bus.send(startVmInstanceMsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        logger.debug(String.format("vm %s start failed", startVmInstanceMsg.getVmInstanceUuid()));
                                        inv.setError(reply.getError());
                                        completion.done();
                                        return;
                                    }

                                    StartVmInstanceReply reply1 = reply.castReply();
                                    inv.setInventory(reply1.getInventory());
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                trigger.next();
                            }
                        });
                    }
                });
            }

            private void setupSyncQosFlows() {
                flow(new NoRollbackFlow() {
                    String __name__ = String.format("sync-qos-from-origin-vm-%s", msg.getVmInstanceUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        VmInstanceInventory origin = VmInstanceInventory.valueOf(dbf.reload(vivo));
                        new While<>(results.getInventoriesWithoutError()).all((inventory, completion) -> {
                            CloneVmSyncQosMsg cvmsg = new CloneVmSyncQosMsg();
                            cvmsg.setSrcVm(origin);
                            cvmsg.setDstVmUuid(inventory.getInventory().getUuid());
                            cvmsg.setFull(msg.isFull());
                            bus.makeLocalServiceId(cvmsg, VmInstanceConstant.SERVICE_ID);
                            bus.send(cvmsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess()) {
                                        inventory.setError(reply.getError());
                                    }
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                trigger.next();
                            }
                        });
                    }
                });
            }
        }).start();
    }

    private List<String> getRequiredBackupStorageTypes(String psUuid) {
        String psType = Q.New(PrimaryStorageVO.class).eq(PrimaryStorageVO_.uuid, psUuid)
                .select(PrimaryStorageVO_.type)
                .findValue();
        return hostAllocatorMgr.getBackupStorageTypesByPrimaryStorageTypeFromMetrics(psType);
    }

    // copy SystemTags from rootVolume/Nic which attached on origin vm

    /**
     * Step 1: find qos on rootVolume or Nic(notice: nics, not nic), copy it
     * Step 2: delete qos on vm(if user delete it, but clone will take it with instanceOffering, we need drop it)
     * Step 3: find qos on origin vm, copy it
     * Step 4: Anyway, sync it on kvm
     * @param msg
     */
    protected void handle(final CloneVmSyncQosMsg msg) {
        Map<String, Long> inbound = new HashMap<>();
        Map<String, Long> outbound = new HashMap<>();
        VmInstanceInventory destVm = VmInstanceInventory.valueOf(self);
        VmInstanceInventory srcVm = msg.getSrcVm();
        MessageReply reply = new MessageReply();
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("copy-system-tags-from-old-vm-%s-and-sync", self.getUuid()));
        chain.then(new ShareFlow() {
            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = "find-qos-on-volumes-or-nics";
                    @Override
                    public void run(FlowTrigger trigger, Map data){
                        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(srcVm.getType());
                        for (VmNicInventory nic: srcVm.getVmNics()) {
                            VmNicQosStruct struct = backend.getNicQos(srcVm.getUuid(), nic.getUuid());
                            if (struct.inboundBandwidth != -1L || struct.outboundBandwidth != -1L) {
                                // make sure copy systemTag to the same nick(same l3)
                                for (VmNicInventory vmni: destVm.getVmNics()) {
                                    if (vmni.getL3NetworkUuid().equals(nic.getL3NetworkUuid())) {
                                        backend.addNicQos(destVm.getUuid(), vmni.getUuid(), struct.outboundBandwidth, struct.inboundBandwidth);
                                        if (struct.inboundBandwidth != -1L) {
                                            inbound.put(vmni.getUuid(), struct.inboundBandwidth);
                                        }
                                        if (struct.outboundBandwidth != -1L) {
                                            outbound.put(vmni.getUuid(), struct.outboundBandwidth);
                                        }

                                        break;
                                    }
                                }
                            }
                        }

                        List<VolumeInventory> srcVols = srcVm.getAllDiskVolumes();
                        srcVols.sort(Comparator.comparing(VolumeInventory::getDeviceId));

                        List<VolumeInventory> dstVols = destVm.getAllDiskVolumes();
                        dstVols.sort(Comparator.comparing(VolumeInventory::getDeviceId));

                        for (VolumeInventory dstVol : dstVols) {
                            int index = dstVols.indexOf(dstVol);
                            VolumeInventory srcVol = srcVols.get(index);
                            String vUpThresholdQos = VolumeQosHelper.copyUpThresholdQosTagFromVolume(srcVol.getUuid(), dstVol.getUuid());
                            String iopsUpThresholdQos = VolumeQosHelper.copyUpThresholdIopsTagFromVolume(srcVol.getUuid(),
                                    dstVol.getUuid());
                            if (vUpThresholdQos == null && srcVol.getVolumeQos() == null) {
                                continue;
                            }

                            VolumeVO vvo = dbf.findByUuid(dstVol.getUuid(), VolumeVO.class);
                            String qos = srcVol.getVolumeQos() != null ? srcVol.getVolumeQos() :
                                    VolumeQosHelper.getVolumeQosString(VolumeQosHelper.getVolumeQosFromSystemTag(vUpThresholdQos, iopsUpThresholdQos));
                            vvo.setVolumeQos(qos);
                            dbf.updateAndRefresh(vvo);
                        }

                        trigger.next();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-qos-on-vm";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(srcVm.getType());
                        VmNicQosStruct struct = backend.getVmQos(srcVm.getUuid());
                        backend.deleteVmQos(destVm.getUuid(), "in");
                        backend.deleteVmQos(destVm.getUuid(), "out");
                        backend.addVmQos(destVm.getUuid(), struct.outboundBandwidthUpthreshold, struct.inboundBandwidthUpthreshold);

                        trigger.next();
                    }
                });

                flow(new NoRollbackFlow() {

                    @Override
                    public boolean skip(Map data) {
                        return destVm.getState().equals(VmInstanceState.Stopped.toString());
                    }

                    String __name__ = "sync-qos-on-volumes";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        VmInstanceInventory reloadVm = VmInstanceInventory.valueOf(dbf.findByUuid(destVm.getUuid(), VmInstanceVO.class));

                        ErrorCodeList errList = new ErrorCodeList();

                        new While<>(reloadVm.getAllDiskVolumes()).all((volume, coml) -> {
                            if (volume.getVolumeQos() == null || volume.getVolumeQos().isEmpty()) {
                                coml.done();
                                return;
                            }
                            SetVolumeQosOnKVMHostMsg hmsg = new SetVolumeQosOnKVMHostMsg();
                            VolumeQos qos = VolumeQosHelper.getVolumeQos(volume.getVolumeQos());

                            if (!VolumeQosHelper.hasQosLimit(qos)) {
                                coml.done();
                                return;
                            }

                            hmsg.setInstallPath(volume.getInstallPath());
                            hmsg.setVmUuid(volume.getVmInstanceUuid());
                            hmsg.setHostUuid(destVm.getHostUuid());
                            hmsg.setMode(OVERWRITE.getMode());
                            hmsg.setVolume(volume);
                            hmsg.setMsgQos(qos);

                            bus.makeTargetServiceIdByResourceUuid(hmsg, HostConstant.SERVICE_ID, hmsg.getHostUuid());
                            bus.send(hmsg, new CloudBusCallBack(msg) {
                                @Override
                                public void run(MessageReply reply) {
                                    coml.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (errList.getCauses().isEmpty()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(errList.getCauses().get(0));
                                }
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {

                    @Override
                    public boolean skip(Map data) {
                        return destVm.getState().equals(VmInstanceState.Stopped.toString());
                    }

                    String __name__ = "sync-it-on-nic";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(srcVm.getType());

                        if (CollectionUtils.isEmpty(destVm.getVmNics())) {
                            trigger.next();
                        }

                        for (VmNicInventory vmni: destVm.getVmNics()) {
                            VmNicQosStruct struct = backend.getNicQos(srcVm.getUuid(), vmni.getUuid());
                            VmNicVO vmNicVO = dbf.findByUuid(vmni.getUuid(), VmNicVO.class);
                            fireVmNicQosChangedEvent(vmNicVO);

                            L3NetworkVO l3NetworkVO = dbf.findByUuid(vmNicVO.getL3NetworkUuid(), L3NetworkVO.class);
                            L2NetworkVO l2NetworkVO = dbf.findByUuid(l3NetworkVO.getL2NetworkUuid(), L2NetworkVO.class);
                            SetNicQosOnKVMHostMsg hmsg2 = new SetNicQosOnKVMHostMsg();
                            hmsg2.setVmNicUuid(vmni.getUuid());
                            hmsg2.setInternalName(vmni.getInternalName());
                            hmsg2.setL2Uuid(l2NetworkVO.getUuid());
                            Long in = struct.inboundBandwidth;
                            Long out = struct.outboundBandwidth;
                            hmsg2.setInboundBandwidth(in);
                            hmsg2.setOutboundBandwidth(out);
                            hmsg2.setVmUuid(destVm.getUuid());
                            hmsg2.setHostUuid(destVm.getHostUuid());

                            bus.makeTargetServiceIdByResourceUuid(hmsg2, HostConstant.SERVICE_ID, hmsg2.getHostUuid());
                            bus.send(hmsg2, new CloudBusCallBack(trigger) {
                                @Override
                                public void run(MessageReply rpl) {
                                    if (!rpl.isSuccess()) {
                                        logger.warn(String.format("sync qos on vmNic[%s] qos failed, due to: %s",
                                                vmni.getUuid(), rpl.getError().getDetails()));
                                        trigger.fail(rpl.getError());
                                    }else {
                                        trigger.next();
                                    }
                                }
                            });
                        }
                    }
                });
                done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        bus.reply(msg, reply);
                    }
                });

                error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        reply.setError(errCode);
                        bus.reply(msg, reply);
                    }
                });
            }
        }).start();
    }

    private void setVmQga(final APISetVmQgaMsg msg, Completion completion) {
        FlowChain chain = new SimpleFlowChain();
        chain.then(new Flow() {
            String __name__ = "set-vm-qga";

            final String originQgaTag = VmSystemTags.VM_INJECT_QEMUGA.getTokenByResourceUuid(msg.getUuid(), VmSystemTags.VM_INJECT_QEMUGA_TOKEN);

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!msg.getEnable()) {
                    VmSystemTags.VM_INJECT_QEMUGA.delete(msg.getUuid());
                }else{
                    SystemTagCreator creator = VmSystemTags.VM_INJECT_QEMUGA.newSystemTagCreator(msg.getUuid());
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                }
                trigger.next();
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (!StringUtils.isEmpty(originQgaTag) && !msg.getEnable())  {
                    SystemTagCreator creator = VmSystemTags.VM_INJECT_QEMUGA.newSystemTagCreator(msg.getUuid());
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                } else {
                    VmSystemTags.VM_INJECT_QEMUGA.delete(msg.getUuid());
                }

                trigger.rollback();
            }
        });
        VmInstanceSpec spec = new VmInstanceSpec();
        spec.setVmInventory(getSelfInventory());
        spec.setCurrentVmOperation(VmInstanceConstant.VmOperation.SetVmQga);
        setAdditionalFlow(chain, spec);

        chain.error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).start();
    }

    private void handle(final APISetVmQgaMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("set-vm-%s-qga", msg.getVmInstanceUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                APISetVmQgaEvent evt = new APISetVmQgaEvent(msg.getId());
                setVmQga(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    protected void handle(final APIGetVmQgaMsg msg) {
        APIGetVmQgaReply reply = new APIGetVmQgaReply();
        String qemuga = VmSystemTags.VM_INJECT_QEMUGA.getTag(msg.getUuid());
        if (qemuga == null) {
            reply.setEnable(false);
        } else {
            reply.setEnable(true);
        }
        bus.reply(msg, reply);
    }

    private void setNicQosSystemTag(final Long outboundBandWidth, final Long inboundBandWidth, String nicUuid) {
        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(self.getType());
        backend.addNicQos(self.getUuid(), nicUuid, outboundBandWidth, inboundBandWidth);
        VmNicVO vmNicVO = dbf.findByUuid(nicUuid, VmNicVO.class);
        fireVmNicQosChangedEvent(vmNicVO);
    }

    private void deleteNicQosSystemTag(final APIDeleteNicQosMsg msg, String vmUuid) {
        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(self.getType());
        backend.deleteNicQos(vmUuid, msg.getUuid(), msg.getDirection());

        VmNicVO vmNicVO = dbf.findByUuid(msg.getUuid(), VmNicVO.class);
        fireVmNicQosChangedEvent(vmNicVO);
    }

    protected void handle(final APIDeleteNicQosMsg msg) {
        APIDeleteNicQosEvent evt = new APIDeleteNicQosEvent(msg.getId());
        VmNicVO nvo = dbf.findByUuid(msg.getUuid(), VmNicVO.class);

        if (nvo.getVmInstanceUuid() == null) {
            logger.debug("exit here: nvo.getVmInstanceUuid() == null");
            deleteNicQosSystemTag(msg, null);
            bus.publish(evt);
            return;
        }

        String sdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(nvo.getL3NetworkUuid());

        L3NetworkVO l3NetworkVO = dbf.findByUuid(nvo.getL3NetworkUuid(), L3NetworkVO.class);
        L2NetworkVO l2NetworkVO = dbf.findByUuid(l3NetworkVO.getL2NetworkUuid(), L2NetworkVO.class);
        /*no primate to delete the Qos when the offering with Qos miao zhanyong*/
        VmInstanceVO ivo = dbf.findByUuid(nvo.getVmInstanceUuid(), VmInstanceVO.class);
        if (!acntMgr.isAdmin(msg.getSession())) {
            VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(self.getType());
            VmNicQosStruct struct = backend.getVmQos(self.getUuid());
            if (struct.inboundBandwidthUpthreshold != null || struct.outboundBandwidthUpthreshold != null) {

                //evt.setSuccess(false);
                //evt.setError(operr("No privilege to delete NicQos %s.", msg.getUuid()));
                bus.publish(evt);
                logger.debug(String.format("DeleteNicQos [%s] ignored because of account privilege.", msg.getUuid()));
                return;
            }
        }

        if ((sdnControllerUuid == null) && (ivo.getHostUuid() == null
                || (ivo.getState() != VmInstanceState.Running && ivo.getState() != VmInstanceState.Paused))) {
            deleteNicQosSystemTag(msg, ivo.getUuid());
            bus.publish(evt);
        } else {
            SetNicQosOnKVMHostMsg hmsg = new SetNicQosOnKVMHostMsg();
            hmsg.setVmNicUuid(msg.getUuid());
            hmsg.setInternalName(nvo.getInternalName());
            hmsg.setL2Uuid(l2NetworkVO.getUuid());
            if (msg.getDirection().equals("in")) {
                hmsg.setInboundBandwidth(Long.valueOf("0"));
            } else if (msg.getDirection().equals("out")) {
                hmsg.setOutboundBandwidth(Long.valueOf("0"));
            } else {
                evt.setSuccess(false);
                evt.setError(argerr("direction must be set to in or out"));
                bus.publish(evt);
                return;
            }
            hmsg.setVmUuid(nvo.getVmInstanceUuid());
            hmsg.setHostUuid(ivo.getHostUuid());

            bus.makeTargetServiceIdByResourceUuid(hmsg, HostConstant.SERVICE_ID, hmsg.getHostUuid());
            bus.send(hmsg, new CloudBusCallBack(msg) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        evt.setSuccess(false);
                        evt.setError(reply.getError());
                    } else {
                        deleteNicQosSystemTag(msg, ivo.getUuid());
                    }
                    bus.publish(evt);
                }
            });
        }
    }

    protected void handle(final SetVmNicQosMsg msg) {
        SetVmNicQosReply reply = new SetVmNicQosReply();
        VmNicVO nvo = dbf.findByUuid(msg.getUuid(), VmNicVO.class);

        if (nvo.getVmInstanceUuid() == null) {
            setNicQosSystemTag(msg.getOutboundBandwidth(), msg.getInboundBandwidth(), msg.getUuid());
            bus.reply(msg, reply);
            return;
        }
        VmInstanceVO ivo = self;

        if (ivo.getHostUuid() == null
                || (ivo.getState() != VmInstanceState.Running && ivo.getState() != VmInstanceState.Paused)) {
            setNicQosSystemTag(msg.getOutboundBandwidth(), msg.getInboundBandwidth(), msg.getUuid());
            bus.reply(msg, reply);
        } else {
            L3NetworkVO l3NetworkVO = dbf.findByUuid(nvo.getL3NetworkUuid(), L3NetworkVO.class);
            L2NetworkVO l2NetworkVO = dbf.findByUuid(l3NetworkVO.getL2NetworkUuid(), L2NetworkVO.class);

            SetNicQosOnKVMHostMsg hmsg = new SetNicQosOnKVMHostMsg();
            hmsg.setL2Uuid(l2NetworkVO.getUuid());
            hmsg.setInternalName(nvo.getInternalName());
            hmsg.setInboundBandwidth(msg.getInboundBandwidth());
            hmsg.setOutboundBandwidth(msg.getOutboundBandwidth());
            hmsg.setVmUuid(nvo.getVmInstanceUuid());
            hmsg.setHostUuid(ivo.getHostUuid());
            hmsg.setVmNicUuid(nvo.getUuid());

            bus.makeTargetServiceIdByResourceUuid(hmsg, HostConstant.SERVICE_ID, hmsg.getHostUuid());
            bus.send(hmsg, new CloudBusCallBack(msg) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        reply.setError(reply.getError());
                    } else {
                        setNicQosSystemTag(msg.getOutboundBandwidth(), msg.getInboundBandwidth(), msg.getUuid());
                    }
                    bus.reply(msg, reply);
                }
            });
        }
    }

    protected void handle(final APISetNicQosMsg msg) {
        APISetNicQosEvent evt = new APISetNicQosEvent(msg.getId());
        VmNicVO nvo = dbf.findByUuid(msg.getUuid(), VmNicVO.class);

        if (nvo.getVmInstanceUuid() == null) {
            setNicQosSystemTag(msg.getOutboundBandwidth(), msg.getInboundBandwidth(), msg.getUuid());
            bus.publish(evt);
            return;
        }
        VmInstanceVO ivo = self;

        String sdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(nvo.getL3NetworkUuid());

        if (!acntMgr.isAdmin(msg.getSession())) {
            VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(self.getType());
            VmNicQosStruct struct = backend.getVmQos(self.getUuid());
            if (msg.getInboundBandwidth() != null) {
                //get the bandwidth from vmInstance's tag copy from instanceOffering during creating vm
                if (struct.inboundBandwidthUpthreshold != null) {
                    if (struct.inboundBandwidthUpthreshold < msg.getInboundBandwidth()) {
                        evt.setSuccess(false);
                        evt.setError(argerr("inboundBandwidth must be set no more than %s.", struct.inboundBandwidthUpthreshold));
                        bus.publish(evt);
                        return;
                    }
                }
            }
            if (msg.getOutboundBandwidth() != null) {
                if (struct.outboundBandwidthUpthreshold != null) {
                    if (struct.outboundBandwidthUpthreshold < msg.getOutboundBandwidth()) {
                        evt.setSuccess(false);
                        evt.setError(argerr("outboundBandwidth must be set no more than %s.", struct.outboundBandwidthUpthreshold));
                        bus.publish(evt);
                        return;
                    }
                }
            }
        }
        if ((sdnControllerUuid == null) && (ivo.getHostUuid() == null ||
                (ivo.getState() != VmInstanceState.Running && ivo.getState() != VmInstanceState.Paused))) {
            setNicQosSystemTag(msg.getOutboundBandwidth(), msg.getInboundBandwidth(), msg.getUuid());
            bus.publish(evt);
        } else {
            L3NetworkVO l3NetworkVO = dbf.findByUuid(nvo.getL3NetworkUuid(), L3NetworkVO.class);
            L2NetworkVO l2NetworkVO = dbf.findByUuid(l3NetworkVO.getL2NetworkUuid(), L2NetworkVO.class);

            SetNicQosOnKVMHostMsg hmsg = new SetNicQosOnKVMHostMsg();
            hmsg.setL2Uuid(l2NetworkVO.getUuid());
            hmsg.setInternalName(nvo.getInternalName());
            hmsg.setInboundBandwidth(msg.getInboundBandwidth());
            hmsg.setOutboundBandwidth(msg.getOutboundBandwidth());
            hmsg.setVmUuid(nvo.getVmInstanceUuid());
            hmsg.setHostUuid(ivo.getHostUuid());
            hmsg.setVmNicUuid(nvo.getUuid());

            bus.makeTargetServiceIdByResourceUuid(hmsg, HostConstant.SERVICE_ID, hmsg.getHostUuid());
            bus.send(hmsg, new CloudBusCallBack(msg) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        evt.setSuccess(false);
                        evt.setError(reply.getError());
                    } else {
                        setNicQosSystemTag(msg.getOutboundBandwidth(), msg.getInboundBandwidth(), msg.getUuid());
                    }
                    bus.publish(evt);
                }
            });
        }
    }

    private void fireVmNicQosChangedEvent(VmNicVO vmNicVO) {
        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(self.getType());
        VmNicQosStruct struct = backend.getNicQos(self.getUuid(), vmNicVO.getUuid());
        VmInstanceVO vmInstanceVO = self;

        VmNicQosCanonicalEvents.VmNicQosEventData vmNicQosEventData = new VmNicQosCanonicalEvents.VmNicQosEventData();
        vmNicQosEventData.setInventory(VmNicInventory.valueOf(vmNicVO));
        vmNicQosEventData.setCurrentStatus(vmInstanceVO.getState().toString());
        vmNicQosEventData.setDate(new Date());
        Long inbound = struct.inboundBandwidth;
        Long outbound = struct.outboundBandwidth;
        if (outbound != null) {
            vmNicQosEventData.setBandwidthOut(outbound);
        }
        if (inbound != null) {
            vmNicQosEventData.setBandwidthIn(inbound);
        }
        evtf.fire(VmNicQosCanonicalEvents.VM_NIC_QOS_CHANGE_PATH, vmNicQosEventData);
    }

    private void getNiqQosOnHost(String nicUuid, final ReturnValueCompletion<GetNicQosOnKVMHostReply> completion) {
        if (self.getState() != VmInstanceState.Running && self.getState() != VmInstanceState.Paused) {
            completion.fail(operr("vm [%s]' state must be Running or Paused to sync nic qos", self.getUuid()));
            return;
        }

        if (self.getHostUuid() == null) {
            completion.fail(operr("vm [%s]'s HostUuid is null, cannot sync nic qos"));
            return;
        }

        VmNicVO nvo = dbf.findByUuid(nicUuid, VmNicVO.class);
        GetNicQosOnKVMHostMsg gmsg = new GetNicQosOnKVMHostMsg();
        gmsg.setInternalName(nvo.getInternalName());
        gmsg.setVmUuid(self.getUuid());
        gmsg.setHostUuid(self.getHostUuid());

        bus.makeTargetServiceIdByResourceUuid(gmsg, HostConstant.SERVICE_ID, self.getHostUuid());
        bus.send(gmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    GetNicQosOnKVMHostReply r = reply.castReply();
                    completion.success(r);
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    protected void handle(final GetVmNicQosMsg msg) {
        GetVmNicQosReply reply = new GetVmNicQosReply();
        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(self.getType());
        VmNicQosStruct struct = backend.getNicQos(self.getUuid(), msg.getUuid());

        if (msg.getForceSync()) {
            getNiqQosOnHost(msg.getUuid(), new ReturnValueCompletion<GetNicQosOnKVMHostReply>(msg) {
                @Override
                public void success(GetNicQosOnKVMHostReply r) {
                    if (r.getInbound() != null) {
                        reply.setInboundBandwidth(r.getInbound());
                    }

                    if (r.getOutbound() != null) {
                        reply.setOutboundBandwidth(r.getOutbound());
                    }

                    setNicQosSystemTag(r.getOutbound(), r.getInbound(), msg.getUuid());
                    bus.reply(msg, reply);
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    reply.setError(errorCode);
                    bus.reply(msg, reply);
                }
            });
        } else {
            reply.setInboundBandwidth(struct.inboundBandwidth);
            reply.setOutboundBandwidth(struct.outboundBandwidth);
            bus.reply(msg, reply);
        }
    }

    protected void handle(final APIGetNicQosMsg msg) {
        APIGetNicQosReply reply = new APIGetNicQosReply();

        VmNicQosConfigBackend backend = vmMgr.getVmNicQosConfigBackend(self.getType());
        VmNicQosStruct struct = backend.getNicQos(self.getUuid(), msg.getUuid());

        if (!acntMgr.isAdmin(msg.getSession())) {
            if (struct.inboundBandwidthUpthreshold != null && struct.inboundBandwidthUpthreshold != -1L) {
                reply.setInboundBandwidthUpthreshold(struct.inboundBandwidthUpthreshold);
            }
            if (struct.outboundBandwidthUpthreshold != null && struct.outboundBandwidthUpthreshold != -1L) {
                reply.setOutboundBandwidthUpthreshold(struct.outboundBandwidthUpthreshold);
            }
        }

        if (msg.getForceSync()) {
            getNiqQosOnHost(msg.getUuid(), new ReturnValueCompletion<GetNicQosOnKVMHostReply>(msg){
                @Override
                public void success(GetNicQosOnKVMHostReply r) {
                    if (r.getInbound() != null) {
                        reply.setInboundBandwidth(r.getInbound());
                    }

                    if (r.getOutbound() != null) {
                        reply.setOutboundBandwidth(r.getOutbound());
                    }

                    setNicQosSystemTag(r.getOutbound(), r.getInbound(), msg.getUuid());
                    bus.reply(msg, reply);
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    reply.setError(errorCode);
                    bus.reply(msg, reply);
                }
            });
        } else {
            reply.setInboundBandwidth(struct.inboundBandwidth);
            reply.setOutboundBandwidth(struct.outboundBandwidth);
            bus.reply(msg, reply);
        }
    }

    private void handle(final ChangeVmPasswordMsg msg) {
        ErrorCode allowed = validateOperationByState(msg, self.getState(), null);
        if (allowed != null) {
            bus.replyErrorByMessageType(msg, allowed);
            return;
        }

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return syncThreadName;
            }

            @Override
            public void run(SyncTaskChain chain) {
                ChangeVmPasswordReply reply = new ChangeVmPasswordReply();
                changepasswd(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("change-vm-password-%s", self.getUuid());
            }
        });
    }

    protected void handle(final APIChangeVmPasswordMsg msg) {
        APIChangeVmPasswordEvent evt = new APIChangeVmPasswordEvent(msg.getId());

        ChangeVmPasswordMsg cmsg = new ChangeVmPasswordMsg();
        cmsg.setAccount(msg.getAccount());
        cmsg.setPassword(msg.getPassword());
        cmsg.setUuid(msg.getUuid());
        bus.makeTargetServiceIdByResourceUuid(cmsg, MevocoConstants.SERVICE_ID, cmsg.getVmInstanceUuid());
        bus.send(cmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    evt.setError(err(VmErrors.CHANGE_VM_PASSWORD_ERROR, reply.getError(), reply.getError().getDetails()));
                    bus.publish(evt);
                    return;
                }

                bus.publish(evt);
            }
        });
    }

    private void handle(APISetVmUsbRedirectMsg msg) {
        APISetVmUsbRedirectEvent evt = new APISetVmUsbRedirectEvent(msg.getId());
        SystemTagCreator creator = VmSystemTags.USB_REDIRECT.newSystemTagCreator(self.getUuid());
        creator.setTagByTokens(map(e(VmSystemTags.USB_REDIRECT_TOKEN, String.valueOf(msg.isEnable()))));
        creator.recreate = true;
        creator.create();
        bus.publish(evt);
    }

    private void handle(APIGetVmUsbRedirectMsg msg) {
        APIGetVmUsbRedirectReply reply = new APIGetVmUsbRedirectReply();
        String usbRedirect = VmSystemTags.USB_REDIRECT.getTokenByResourceUuid(self.getUuid(),
                VmSystemTags.USB_REDIRECT_TOKEN);
        reply.setEnable(Boolean.parseBoolean(usbRedirect));
        bus.reply(msg, reply);
    }

    private void handle(APISetVmRDPMsg msg) {
        APISetVmRDPEvent evt = new APISetVmRDPEvent(msg.getId());
        SystemTagCreator creator = VmSystemTags.RDP_ENABLE.newSystemTagCreator(self.getUuid());
        creator.setTagByTokens(map(e(VmSystemTags.RDP_ENABLE_TOKEN, String.valueOf(msg.isEnable()))));
        creator.recreate = true;
        creator.create();
        bus.publish(evt);
    }

    private void handle(APIGetVmRDPMsg msg) {
        APIGetVmRDPReply reply = new APIGetVmRDPReply() ;
        String rdpEnable = VmSystemTags.RDP_ENABLE.getTokenByResourceUuid(self.getUuid(),
                VmSystemTags.RDP_ENABLE_TOKEN);
        reply.setEnable(Boolean.parseBoolean(rdpEnable));
        bus.reply(msg, reply);
    }

    private void handle(APIGetVmMonitorNumberMsg msg) {
        APIGetVmMonitorNumberReply reply = new APIGetVmMonitorNumberReply() ;
        String VDIMonitorNumber = VmSystemTags.VDI_MONITOR_NUMBER.getTokenByResourceUuid(self.getUuid(),
                VmSystemTags.VDI_MONITOR_NUMBER_TOKEN);
        if (VDIMonitorNumber == null) {
            VDIMonitorNumber = VmInstanceConstant.VM_MONITOR_NUMBER.toString();
        }
        reply.setMonitorNumber(Integer.valueOf(VDIMonitorNumber));
        bus.reply(msg, reply);
    }

    private void handle(APISetVmMonitorNumberMsg msg) {
        APISetVmMonitorNumberEvent evt = new APISetVmMonitorNumberEvent(msg.getId());
        SystemTagCreator creator = VmSystemTags.VDI_MONITOR_NUMBER.newSystemTagCreator(self.getUuid());
        creator.setTagByTokens(map(e(VmSystemTags.VDI_MONITOR_NUMBER_TOKEN, String.valueOf(msg.getMonitorNumber()))));
        creator.recreate = true;
        creator.create();
        bus.publish(evt);
    }

    private void handle(APIGetImageCandidatesForVmToChangeMsg msg) {
        APIGetImageCandidatesForVmToChangeReply apiReply = new APIGetImageCandidatesForVmToChangeReply();

        GetImageCandidatesForVmToChangeMsg gmsg = new GetImageCandidatesForVmToChangeMsg();
        gmsg.setVmInstanceUuid(msg.getVmInstanceUuid());
        bus.makeTargetServiceIdByResourceUuid(gmsg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
        bus.send(gmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                GetImageCandidatesForVmToChangeReply rly = reply.castReply();
                if (rly.getInventories() == null || rly.getInventories().isEmpty()) {
                    apiReply.setInventories(null);
                    bus.reply(msg, apiReply);
                    return;
                }

                List<ImageInventory> candidates = new ArrayList<>();
                if (acntMgr.isAdmin(msg.getSession())) {
                    apiReply.setInventories(rly.getInventories());
                    bus.reply(msg, apiReply);
                    return;
                }

                List<String> myImages = acntMgr.getResourceUuidsCanAccessByAccount(msg.getSession().getAccountUuid(), ImageVO.class);
                if (myImages == null || myImages.isEmpty()) {
                    apiReply.setInventories(null);
                    bus.reply(msg, apiReply);
                    return;
                }

                // get intersection
                for (ImageInventory inv : rly.getInventories()) {
                    for (String myImage : myImages) {
                        if (myImage.equals(inv.getUuid())) {
                            candidates.add(inv);
                            break;
                        }
                    }
                }
                apiReply.setInventories(candidates);
                bus.reply(msg, apiReply);
            }
        });
    }

    private void handle(final APISetVmConsoleModeMsg msg) {
        APISetVmConsoleModeEvent evt = new APISetVmConsoleModeEvent(msg.getId());
        SystemTagCreator creator = MevocoSystemTags.VM_CONSOLE_MODE.newSystemTagCreator(self.getUuid());
        creator.setTagByTokens(map(e(MevocoSystemTags.VM_CONSOLE_MODE_TOKEN, msg.getMode())));
        creator.recreate = true;
        creator.create();
        evt.setInventory(getSelfInventory());
        bus.publish(evt);
    }

    private void handle(final APIUpdateVmNicMacMsg msg) {
        APIUpdateVmNicMacEvent event = new APIUpdateVmNicMacEvent(msg.getId());
        VmNicVO vo = new SQLBatchWithReturn<VmNicVO>() {
            @Override
            protected VmNicVO scripts() {
                VmNicVO vo = dbf.findByUuid(msg.getVmNicUuid(), VmNicVO.class);
                vo.setMac(msg.getMac());
                vo = dbf.updateAndRefresh(vo);
                return vo;
            }
        }.execute();
        event.setInventory(VmNicInventory.valueOf(vo));
        bus.publish(event);
    }

    private void handle(APIGetVmInstanceFirstBootDeviceMsg msg) {
        ErrorCode allowed = validateOperationByState(msg, self.getState(), null);
        if (allowed != null) {
            bus.replyErrorByMessageType(msg, allowed);
            return;
        }

        String hostUuid = Q.New(VmInstanceVO.class)
                .eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid())
                .select(VmInstanceVO_.hostUuid)
                .findValue();

        APIGetVmInstanceFirstBootDeviceReply reply = new APIGetVmInstanceFirstBootDeviceReply();
        GetVmFirstBootDeviceOnHypervisorMsg gmsg = new GetVmFirstBootDeviceOnHypervisorMsg();
        gmsg.setVmInstanceUuid(msg.getVmInstanceUuid());
        gmsg.setHostUuid(hostUuid);
        bus.makeTargetServiceIdByResourceUuid(gmsg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(gmsg, new CloudBusCallBack(reply) {
            @Override
            public void run(MessageReply rly) {
                if (!rly.isSuccess()) {
                    reply.setError(rly.getError());
                }

                GetVmFirstBootDeviceOnHypervisorReply grly = rly.castReply();
                if (!grly.isSuccess()) {
                    reply.setError(grly.getError());
                }

                if (reply.isSuccess()) {
                    reply.setFirstBootDevice(grly.getFirstBootDevice());
                }

                bus.reply(msg, reply);
            }
        });
    }

    private void handle(APISetVmCleanTrafficMsg msg) {
        APISetVmCleanTrafficEvent event = new APISetVmCleanTrafficEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return syncThreadName;
            }

            private boolean changeTag(APISetVmCleanTrafficMsg msg){
                String currentValue = VmSystemTags.CLEAN_TRAFFIC.getTokenByResourceUuid(msg.getUuid(), VmSystemTags.CLEAN_TRAFFIC_TOKEN);
                boolean needChange = currentValue == null || msg.isEnable() != Boolean.valueOf(currentValue);
                if (needChange) {
                    doUpdateTag(msg.getUuid(), msg.isEnable());
                }
                return needChange;
            }

            private void doUpdateTag(String vmUuid, boolean enable){
                SystemTagCreator creator = VmSystemTags.CLEAN_TRAFFIC.newSystemTagCreator(vmUuid);
                creator.setTagByTokens(map(e(VmSystemTags.CLEAN_TRAFFIC_TOKEN, String.valueOf(enable))));
                creator.recreate = true;
                creator.create();
            }

            @Override
            public void run(SyncTaskChain chain) {
                boolean changed = changeTag(msg);
                String hostUuid = Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, msg.getUuid()).select(VmInstanceVO_.hostUuid).findValue();
                if (!changed || hostUuid == null) {
                    bus.publish(event);
                    chain.next();
                    return;
                }

                VmUpdateNicOnHypervisorMsg cmsg = new VmUpdateNicOnHypervisorMsg();
                cmsg.setVmInstanceUuid(msg.getUuid());
                cmsg.setHostUuid(hostUuid);
                bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, msg.getUuid());
                bus.send(cmsg, new CloudBusCallBack(chain, event) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            doUpdateTag(msg.getUuid(), !msg.isEnable());
                            event.setError(reply.getError());
                        }

                        bus.publish(event);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("change-vm-clean-traffic-state-%s", self.getUuid());
            }
        });
    }

    private void handle(APIChangeVmImageMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                APIChangeVmImageEvent evt = new APIChangeVmImageEvent(msg.getId());

                ChangeVmImageMsg cmsg = new ChangeVmImageMsg();
                cmsg.setVmInstanceUuid(msg.getVmInstanceUuid());
                cmsg.setImageUuid(msg.getImageUuid());
                cmsg.setResourceUuid(msg.getResourceUuid());
                bus.makeTargetServiceIdByResourceUuid(cmsg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());

                ChangeVmImageOverlayMsg imsg = new ChangeVmImageOverlayMsg();
                imsg.setMessage(cmsg);
                imsg.setImageUuid(msg.getImageUuid());
                bus.makeTargetServiceIdByResourceUuid(imsg, ImageConstant.SERVICE_ID, msg.getImageUuid());
                bus.send(imsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            evt.setError(reply.getError());
                        } else {
                            String imageUuid = msg.getImageUuid();
                            String vmUuid = msg.getVmInstanceUuid();
                            if (VmSystemTags.VM_INJECT_QEMUGA.hasTag(imageUuid, ImageVO.class)) {
                                SystemTagCreator creator = VmSystemTags.VM_INJECT_QEMUGA.newSystemTagCreator(vmUuid);
                                creator.inherent = false;
                                creator.recreate = true;
                                creator.create();
                            } else {
                                VmSystemTags.VM_INJECT_QEMUGA.delete(vmUuid);
                            }

                            if (!ApplianceVmSystemTags.APPLIANCEVM_DEPLOY_AGENT_ON_START.hasTag(msg.getVmInstanceUuid()) &&
                                    Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, vmUuid).eq(VmInstanceVO_.type, VmInstanceConstant.APPLIANCE_VM_TYPE).isExists()) {
                                SystemTagCreator creator = ApplianceVmSystemTags.APPLIANCEVM_DEPLOY_AGENT_ON_START.newSystemTagCreator(vmUuid);
                                creator.ignoreIfExisting = true;
                                creator.inherent = true;
                                creator.create();
                            }

                            ChangeVmImageReply rly = reply.castReply();
                            evt.setInventory(rly.getInventory());
                        }
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("api-change-vm-%s-image", msg.getVmInstanceUuid());
            }
        });
    }

    @SuppressWarnings("unchecked")
    private void changepasswd(final Message msg, final Completion completion){
        ErrorCode allowed = validateOperationByState(msg, self.getState(), null);
        if (allowed != null) {
            completion.fail(allowed);
            return;
        }

        ChangeVmPasswordMsg amsg = (ChangeVmPasswordMsg)msg;
        VmAccountPreference account = new VmAccountPreference(amsg.getVmInstanceUuid(),
                amsg.getAccount(), amsg.getPassword());

        logger.debug("vm instance is " + amsg.getVmInstanceUuid());

        final VmInstanceSpec spec = new VmInstanceSpec();
        spec.setVmInventory(VmInstanceInventory.valueOf(dbf.findByUuid(amsg.getUuid(), VmInstanceVO.class)));
        spec.setCurrentVmOperation(VmInstanceConstant.VmOperation.ChangePassword);
        spec.setAccountPerference(account);

        ErrorCode noHostErr = operr("not dest host found in db by uuid: %s, can't" +
                " send change password cmd to the host!", amsg.getVmInstanceUuid());

        VmInstanceVO viVo = dbf.findByUuid(amsg.getVmInstanceUuid(), VmInstanceVO.class);

        if (viVo == null) {
            completion.fail(noHostErr);
            return;
        }
        VmInstanceState vmState = viVo.getState();

        VmInstanceInventory inv = VmInstanceInventory.valueOf(viVo);

        String hostid = inv.getHostUuid() == null ? inv.getLastHostUuid() : inv.getHostUuid();
        HostVO hvo = dbf.findByUuid(hostid, HostVO.class);
        if (hvo != null) {
            spec.setDestHost(HostInventory.valueOf(hvo));
            spec.setDestRootVolume(inv.getRootVolume());
        } else {
            completion.fail(noHostErr);
            return;
        }

        FlowChain chain;
        if(vmState.equals(VmInstanceState.Running)) {
            chain = getChangeVmPasswordWorkFlowChain();
            setAdditionalFlow(chain, spec);

            logger.debug("flow numbers are " + chain.getFlows().size());
        } else {
            completion.fail(operr("state is not correct while change password."));
            return;
        }

        chain.setName(String.format("change-vm-password-%s", amsg.getVmInstanceUuid()));
        chain.getData().put(VmInstanceConstant.Params.VmInstanceSpec.toString(), spec);
        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(final ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    protected FlowChain getChangeVmPasswordWorkFlowChain() {
        return mimpl.getChangeVmPasswordWorkFlowChain();
    }

    private FlowChain getChangeVmImageWorkFlowChain() {
        return mimpl.getChangeVmImageWorkFlowChain();
    }

    private Boolean isLocalStorage(String psUuid) {
        return Q.New(PrimaryStorageVO.class)
                .eq(PrimaryStorageVO_.type, LocalStorageConstants.LOCAL_STORAGE_TYPE)
                .eq(PrimaryStorageVO_.uuid, psUuid)
                .isExists();
    }

    private void cloneResourceConfig(GlobalConfig config, String srcUuid, String destUuid) {
        if (rcf.getResourceConfigValue(config, srcUuid, String.class).equals(config.defaultValue(String.class))) {
            return;
        }

        ResourceConfig resourceConfig = rcf.getResourceConfig(config.getIdentity());
        resourceConfig.updateValue(destUuid, rcf.getResourceConfigValue(config, srcUuid, String.class));
    }

    private void cloneVolumeTag(String srcVolumeUuid, String dstVolumeUuid) {
        if (!tagMgr.hasSystemTag(srcVolumeUuid, KVMSystemTags.VOLUME_VIRTIO_SCSI.getTagFormat())) {
            return;
        }

        SystemTagCreator creator = KVMSystemTags.VOLUME_VIRTIO_SCSI.newSystemTagCreator(dstVolumeUuid);
        creator.ignoreIfExisting = true;
        creator.inherent = false;
        creator.tag = KVMSystemTags.VOLUME_VIRTIO_SCSI.getTagFormat();
        creator.create();

        creator = KVMSystemTags.VOLUME_WWN.newSystemTagCreator(dstVolumeUuid);
        creator.ignoreIfExisting = true;
        creator.inherent = true;
        creator.setTagByTokens(map(e(KVMSystemTags.VOLUME_WWN_TOKEN, new WwnUtils().getRandomWwn())));
        creator.create();
    }
}
