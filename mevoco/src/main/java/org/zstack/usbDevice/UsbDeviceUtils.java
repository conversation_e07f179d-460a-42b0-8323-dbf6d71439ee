package org.zstack.usbDevice;

import org.zstack.core.db.Q;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;

import java.util.HashMap;
import java.util.Map;

import static org.zstack.core.Platform.operr;

/**
 * @Author: DaoDao
 * @Date: 2023/9/6
 */
public class UsbDeviceUtils {

     static class UsbUsageCounter {
         private final String vmInstanceUuid;
         Map<UsbVersion, Long> counters = new HashMap<>();
         Map<UsbVersion, Long> usages = new HashMap<>();

         public UsbUsageCounter(String vmInstanceUuid) {
             this.vmInstanceUuid = vmInstanceUuid;
             counters.put(UsbVersion.USB1, UsbDeviceConstants.MAX_USB_1_DEVICE_PER_VM);
             counters.put(UsbVersion.USB2, UsbDeviceConstants.MAX_USB_2_DEVICE_PER_VM);
             counters.put(UsbVersion.USB3, UsbDeviceConstants.MAX_USB_3_DEVICE_PER_VM);
             init();
         }

         private void init() {
             long usb1Device = Q.New(UsbDeviceVO.class)
                     .eq(UsbDeviceVO_.vmInstanceUuid, vmInstanceUuid)
                     .like(UsbDeviceVO_.usbVersion, "1%")
                     .count();

             long usb2Device = Q.New(UsbDeviceVO.class)
                     .eq(UsbDeviceVO_.vmInstanceUuid, vmInstanceUuid)
                     .like(UsbDeviceVO_.usbVersion, "2%")
                     .count();

             long usb3Device = Q.New(UsbDeviceVO.class)
                     .eq(UsbDeviceVO_.vmInstanceUuid, vmInstanceUuid)
                     .like(UsbDeviceVO_.usbVersion, "3%")
                     .count();

             acquireTimes(UsbVersion.USB1, usb1Device);
             acquireTimes(UsbVersion.USB2, usb2Device);
             acquireTimes(UsbVersion.USB3, usb3Device);
         }

         enum UsbVersion {
             USB1(0),
             USB2(1),
             USB3(2);

             private final int busNum;

             UsbVersion(int busNum) {
                 this.busNum = busNum;
             }

             boolean isCompatible(UsbVersion version) {
                 if (version.equals(this)) {
                     return true;
                 }

                 return USB3.equals(this);
             }

             public static UsbVersion fromString(String version) {
                 if (version.contains("1")) {
                     return USB1;
                 } else if (version.contains("2")) {
                     return USB2;
                 } else if (version.contains("3")) {
                     return USB3;
                 } else {
                     throw new IllegalArgumentException("unknown usb version: " + version);
                 }
             }

             public int toBusNum() {
                 return busNum;
             }
         }

         public boolean acquireTimes(UsbVersion version, long times) {
             for (int i = 0; i < times; i++) {
                 if (!acquire(version)) {
                     return false;
                 }
             }

             return true;
         }

         public boolean acquire(UsbVersion version) {
             for (UsbVersion v : UsbVersion.values()) {
                 if (v.isCompatible(version) && counters.get(v) > 0) {
                     counters.put(v, counters.get(v) - 1);
                     usages.putIfAbsent(v, 0L);
                     usages.put(v, usages.get(v) + 1);
                     return true;
                 }
             }

             return false;
         }

         public int getAttachableBusNum(UsbVersion version) {
             for (UsbVersion v : UsbVersion.values()) {
                 if (v.isCompatible(version) && counters.get(v) > 0) {
                     return v.toBusNum();
                 }
             }

             return -1;
         }

         public String printUsages() {
             StringBuilder sb = new StringBuilder();
             for (UsbVersion v : UsbVersion.values()) {
                 sb.append(String.format("%s: %s, ", v, usages.getOrDefault(v, 0L)));
             }

             return sb.toString();
         }
    }

    public static int getAttachableBusNum(String usbVersion, String vmUuid) {
        UsbUsageCounter counter = new UsbUsageCounter(vmUuid);
        int busNum = counter.getAttachableBusNum(UsbUsageCounter.UsbVersion.fromString(usbVersion));
        if (busNum < 0) {
            throw new OperationFailureException(operr("the number of usb device attached to vm has reached the limit. Current usage: \n %s", counter.printUsages()));
        }

        return busNum;
    }

    public static ErrorCode checkNumberOfUsbDeviceAlreadyAttachedTOVm(String usbVersion, String vmUuid) {
        UsbUsageCounter counter = new UsbUsageCounter(vmUuid);
        if (counter.getAttachableBusNum(UsbUsageCounter.UsbVersion.fromString(usbVersion)) < 0) {
            return operr("the number of usb device attached to vm has reached the limit. " +
                    "Current usage: \n %s", counter.printUsages());
        }

        return null;
    }
}
