package org.zstack.usbDevice;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.apimediator.StopRoutingException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.host.HostState;
import org.zstack.header.host.HostStatus;
import org.zstack.header.host.HostVO;
import org.zstack.header.message.APIMessage;
import org.zstack.header.storage.primary.APIAttachPrimaryStorageToClusterMsg;
import org.zstack.header.storage.snapshot.group.MemorySnapshotValidatorExtensionPoint;
import org.zstack.header.vm.APICreateVmInstanceMsg;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.identity.AccountManager;
import org.zstack.network.service.lb.LoadBalancerSystemTags;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;
import static org.zstack.core.Platform.argerr;

/**
 * Created by GuoYi on 10/21/17.
 */
public class UsbDeviceApiInterceptor implements ApiMessageInterceptor, MemorySnapshotValidatorExtensionPoint, GlobalApiMessageInterceptor {
    public static List<VmInstanceState> allowedVmInstanceAttachableState = asList(VmInstanceState.Running, VmInstanceState.Stopped);
    public static List<VmInstanceState> allowedVmInstanceDetachableState = asList(VmInstanceState.Running, VmInstanceState.Stopped);
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private AccountManager acmgr;

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIQueryUsbDeviceMsg) {
            return msg;
        }

        if (msg instanceof APICreateVmInstanceMsg) {
            validate((APICreateVmInstanceMsg) msg);
            return msg;
        }

        if (!acmgr.isAdmin(msg.getSession())) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "%s can only be called by admin account",
                    msg.getClass().getSimpleName()
            ));
        }
        if (msg instanceof APIAttachUsbDeviceToVmMsg) {
            validate((APIAttachUsbDeviceToVmMsg) msg);
        } else if (msg instanceof APIDetachUsbDeviceFromVmMsg) {
            validate((APIDetachUsbDeviceFromVmMsg) msg);
        } else if (msg instanceof APIGetUsbDeviceCandidatesForAttachingVmMsg) {
            validate((APIGetUsbDeviceCandidatesForAttachingVmMsg) msg);
        } else if (msg instanceof APIUpdateUsbDeviceMsg) {
            validate((APIUpdateUsbDeviceMsg) msg);
        } 
        return msg;
    }

    private void validate(APICreateVmInstanceMsg msg) {
        if (CollectionUtils.isEmpty(msg.getSystemTags())) {
            return;
        }

        List<String> systemtags = msg.getSystemTags().stream()
                .filter(tag -> VmSystemTags.VM_ATTACH_USB.isMatch(tag))
                .collect(Collectors.toList());
        List<String> usbDeviceUuids = new ArrayList<>();
        for (String tag : systemtags) {
            Map<String, String> token = VmSystemTags.VM_ATTACH_USB.getTokensByTag(tag);
            usbDeviceUuids.add(token.get(VmSystemTags.USBDEVICE_UUID_TOKEN));
        }

        if (usbDeviceUuids.isEmpty()) {
            return;
        }

        if (Q.New(UsbDeviceVO.class).in(UsbDeviceVO_.uuid, usbDeviceUuids)
                .like(UsbDeviceVO_.usbVersion, "1%")
                .count() > UsbDeviceConstants.MAX_USB_1_DEVICE_PER_VM) {
            throw new ApiMessageInterceptionException(Platform.operr(
                    "You can attach at most %s USB 1.0 devices to one vm instance.",
                    UsbDeviceConstants.MAX_USB_1_DEVICE_PER_VM
            ));
        }

        if (Q.New(UsbDeviceVO.class).in(UsbDeviceVO_.uuid, usbDeviceUuids)
                .like(UsbDeviceVO_.usbVersion, "2%")
                .count() > UsbDeviceConstants.MAX_USB_2_DEVICE_PER_VM) {
            throw new ApiMessageInterceptionException(Platform.operr(
                    "You can attach at most %s USB 2.0 devices to one vm instance.",
                    UsbDeviceConstants.MAX_USB_2_DEVICE_PER_VM
            ));
        }

        if (Q.New(UsbDeviceVO.class).in(UsbDeviceVO_.uuid, usbDeviceUuids)
                .like(UsbDeviceVO_.usbVersion, "3%")
                .count() > UsbDeviceConstants.MAX_USB_3_DEVICE_PER_VM) {
            throw new ApiMessageInterceptionException(Platform.operr(
                    "You can attach at most %s USB 3.0 devices to one vm instance.",
                    UsbDeviceConstants.MAX_USB_1_DEVICE_PER_VM
            ));
        }
    }

    private void validate(APIAttachUsbDeviceToVmMsg msg) {
        UsbDeviceVO usb = dbf.findByUuid(msg.getUsbDeviceUuid(), UsbDeviceVO.class);

        if (msg.getVmInstanceUuid().equals(usb.getVmInstanceUuid())) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "the usb device[uuid:%s] has already been attached to same vm[uuid:%s]",
                    msg.getUsbDeviceUuid(), usb.getVmInstanceUuid()
            ));
        }

        if (usb.getVmInstanceUuid() != null) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "the usb device[uuid:%s] has already been attached to another vm[uuid:%s]",
                    msg.getUsbDeviceUuid(), usb.getVmInstanceUuid()
            ));
        }

        if (usb.getState() != UsbDeviceState.Enabled) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "the usb device[uuid:%s] is not in attachable state of %s",
                    msg.getUsbDeviceUuid(), UsbDeviceState.Enabled
            ));
        }

        VmInstanceVO vm = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        if (!(allowedVmInstanceAttachableState.contains(vm.getState()))) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "the vm instance[uuid:%s] is not in attachable state of %s for usb device",
                    msg.getVmInstanceUuid(), allowedVmInstanceAttachableState
            ));
        }

        HostVO host = dbf.findByUuid(usb.getHostUuid(), HostVO.class);
        if (!host.getState().equals(HostState.Enabled) || !host.getStatus().equals(HostStatus.Connected)) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "the host that the usb device[uuid:%s] pluged in is not in valid state[%s] or status[%s]",
                    usb.getHostUuid(), msg.getUsbDeviceUuid(), HostState.Enabled, HostStatus.Connected
            ));
        }
    }

    private void validate(APIDetachUsbDeviceFromVmMsg msg) {
        UsbDeviceVO usb = dbf.findByUuid(msg.getUsbDeviceUuid(), UsbDeviceVO.class);

        if (usb.getVmInstanceUuid() == null) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "the usb device[uuid:%s] is not attached to any vm instance.",
                    usb.getUuid()
            ));
        }

        VmInstanceVO vm = dbf.findByUuid(usb.getVmInstanceUuid(), VmInstanceVO.class);
        if (!(allowedVmInstanceDetachableState.contains(vm.getState()))) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "the vm instance that the usb device[uuid:%s] is attached to is not in detachable state of %s",
                    usb.getUuid(), allowedVmInstanceDetachableState
            ));
        }
    }

    private void validate(APIGetUsbDeviceCandidatesForAttachingVmMsg msg) {
        VmInstanceVO vm = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        if (!(allowedVmInstanceAttachableState.contains(vm.getState()))) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "vm instance[uuid:%s] not in attachable state of %s for usb device",
                    vm.getUuid(),
                    allowedVmInstanceAttachableState
            ));
        }
    }

    private void validate(APIUpdateUsbDeviceMsg msg) {
        UsbDeviceVO usb = dbf.findByUuid(msg.getUuid(), UsbDeviceVO.class);
        if (usb.getVmInstanceUuid() != null && msg.getState() != null && msg.getState().equals("Disabled")) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "cannot disable usb device[uuid:%s] when it's attached to a vm instance",
                    msg.getUuid()
            ));
        }
    }

    @Override
    public ErrorCode checkVmWhereMemorySnapshotExistExternalDevices(String VmInstanceUuid) {
        if (Q.New(UsbDeviceVO.class)
                .eq(UsbDeviceVO_.vmInstanceUuid, VmInstanceUuid)
                .isExists()) {
            return argerr("please umount all usb devices of the vm[%s] and try again", VmInstanceUuid);
        }
        return null;
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        return CollectionDSL.list(APICreateVmInstanceMsg.class);
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.END;
    }
}
