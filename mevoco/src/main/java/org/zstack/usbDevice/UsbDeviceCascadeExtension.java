package org.zstack.usbDevice;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.header.vm.VmDeletionStruct;
import org.zstack.header.vm.VmInstanceInventory;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by Guo<PERSON>i on 10/21/17.
 */
public class UsbDeviceCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(UsbDeviceCascadeExtension.class);
    private static final String NAME = UsbDeviceVO.class.getSimpleName();

    @Autowired
    private DatabaseFacade dbf;

    @Autowired
    private CloudBus bus;

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    private void handleDeletion(CascadeAction action, Completion completion) {
        final List<UsbDeviceInventory> invs = getUsbDeviceFromAction(action);
        if (invs == null) {
            completion.success();
            return;
        }

        new While<>(invs).all((inv, cmpl) -> {
            DetachUsbDeviceMsg dmsg = new DetachUsbDeviceMsg();
            dmsg.setUsbDeviceUuid(inv.getUuid());

            bus.makeTargetServiceIdByResourceUuid(dmsg, UsbDeviceConstants.SERVICE_ID, inv.getUuid());
            bus.send(dmsg, new CloudBusCallBack(cmpl) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        logger.debug(String.format("successfully detached usb device[uuid:%s]", dmsg.getUsbDeviceUuid()));
                    } else {
                        logger.warn(reply.getError().toString());
                        UsbDeviceVO vo = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, inv.getUuid()).find();
                        vo.setVmInstanceUuid(null);
                        vo.setAttachType(null);
                        dbf.update(vo);
                        UsbSystemTags.USB_REDIRECT_PORT.delete(vo.getUuid());
                    }
                    cmpl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                completion.success();
            }
        });
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        dbf.eoCleanup(UsbDeviceVO.class);
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(VmInstanceVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<UsbDeviceInventory> ctx = getUsbDeviceFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }

    private List<UsbDeviceInventory> getUsbDeviceFromAction(CascadeAction action) {
        if (VmInstanceVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<VmDeletionStruct> structs = action.getParentIssuerContext();
            List<UsbDeviceVO> usbs = new ArrayList<>();
            for (VmDeletionStruct struct : structs) {
                VmInstanceInventory inv = struct.getInventory();
                usbs.addAll(Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.vmInstanceUuid, inv.getUuid()).list());
            }
            if (!usbs.isEmpty()) {
                return UsbDeviceInventory.valueOf(usbs);
            }
        } else if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }

        return null;
    }
}
