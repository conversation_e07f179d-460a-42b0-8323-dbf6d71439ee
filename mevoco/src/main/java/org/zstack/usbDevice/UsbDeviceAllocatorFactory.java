package org.zstack.usbDevice;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.compute.allocator.DesignatedHostAllocatorStrategyFactory;
import org.zstack.core.Platform;
import org.zstack.core.db.Q;
import org.zstack.core.errorcode.ErrorFacade;
import org.zstack.header.allocator.*;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.host.HostVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.err;

/**
 * Created by GuoYi on 10/21/17.
 */
public class UsbDeviceAllocatorFactory extends DesignatedHostAllocatorStrategyFactory
        implements HostAllocatorFilterExtensionPoint, HostAllocatorStrategyExtensionPoint {
    private static final CLogger logger = Utils.getLogger(UsbDeviceAllocatorFactory.class);
    private HostAllocatorStrategyType type = new HostAllocatorStrategyType(UsbDeviceConstants.USB_DEVICE_ALLOCATOR_STRATEGY, false);

    @Autowired
    private ErrorFacade errf;

    @Override
    public List<HostVO> filterHostCandidates(List<HostVO> candidates, HostAllocatorSpec spec) {
        List<UsbDeviceVO> usbDeviceVOS = Q.New(UsbDeviceVO.class)
                .eq(UsbDeviceVO_.vmInstanceUuid, spec.getVmInstance().getUuid())
                .eq(UsbDeviceVO_.attachType, UsbAttachType.PassThrough.toString())
                .list();
        if (usbDeviceVOS == null || usbDeviceVOS.isEmpty()) {
            return candidates;
        }

        List<HostVO> left = new ArrayList<>();
        logger.debug(String.format("there are usb devices[%s] attached on vm[uuid:%s], filter now",
                usbDeviceVOS.stream().map(vo -> vo.getUuid()).collect(Collectors.toList()), spec.getVmInstance().getUuid()));
        for (HostVO host : candidates) {
            if (host.getUuid().equals(usbDeviceVOS.get(0).getHostUuid())) {
                left.add(host);
            }
        }
        if (!left.isEmpty()) {
            return left;
        }

        logger.debug(String.format("all candidates [%s] don't have usb devices [%s], so throw exception",
                candidates.stream().map(host -> host.getUuid()).collect(Collectors.toList()),
                usbDeviceVOS.stream().map(usb -> usb.getUuid()).collect(Collectors.toList())));
        throw new OperationFailureException(err(HostAllocatorError.NO_AVAILABLE_HOST,
                "the vm[uuid:%s] using usb device[uuid:%s] can only be started on the host[uuid:%s], " +
                        "but the host cannot start the vm, maybe the host have no enough CPU/MEM, or it's not Enabled and Connected",
                spec.getVmInstance().getUuid(), usbDeviceVOS.get(0).getUuid(), usbDeviceVOS.get(0).getHostUuid())
        );
    }

    @Override
    public String getHostAllocatorStrategyName(HostAllocatorSpec spec) {
        List<UsbDeviceVO> usbDeviceVOS = Q.New(UsbDeviceVO.class)
                .eq(UsbDeviceVO_.vmInstanceUuid, spec.getVmInstance().getUuid())
                .list();

        if (usbDeviceVOS == null || usbDeviceVOS.isEmpty()) {
            return null;
        }

        return UsbDeviceConstants.USB_DEVICE_ALLOCATOR_STRATEGY;
    }

    @Override
    public HostAllocatorStrategyType getHostAllocatorStrategyType() {
        return type;
    }

    @Override
    public String filterErrorReason() {
        return Platform.i18n("no candidate host with the usb device have enough cpu / memory or Enabled/Connected status");
    }
}