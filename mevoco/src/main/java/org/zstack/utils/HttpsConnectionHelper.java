package org.zstack.utils;

import org.zstack.utils.logging.CLogger;

import javax.net.ssl.*;

public class HttpsConnectionHelper  {
    private static final CLogger logger = Utils.getLogger(HttpsConnectionHelper.class);

    public final static HostnameVerifier DO_NOT_VERIFY = (hostname, session) -> true;

    public static void trustAllHosts() {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new java.security.cert.X509Certificate[]{};
                    }

                    public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType)
                            throws java.security.cert.CertificateException {
                    }

                    public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType)
                            throws java.security.cert.CertificateException {
                    }
                }
        };

        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        } catch (Exception ex) {
            logger.warn("[vc] failed to set default SSL socket factory", ex);
        }
    }
}
