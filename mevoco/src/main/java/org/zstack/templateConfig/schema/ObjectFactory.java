//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.02 at 01:27:06 PM GMT-08:00 
//


package org.zstack.templateConfig.schema;


import javax.xml.bind.annotation.XmlRegistry;

@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.zstack.templateConfig.schema
     * 
     */
    public ObjectFactory() {
    }


    public GlobalConfigTemplate createGlobalConfigTemplate() { return new GlobalConfigTemplate(); }

    public TemplateConfig createTemplateConfig() {
        return new TemplateConfig();
    }

    /**
     * Create an instance of {@link TemplateConfig.Config }
     * 
     */
    public GlobalConfigTemplate.Config createGlobalConfigTemplateConfig() {
        return new GlobalConfigTemplate.Config();
    }

    public TemplateConfig.Config createTemplateConfigConfig() {
        return new TemplateConfig.Config();
    }



}
