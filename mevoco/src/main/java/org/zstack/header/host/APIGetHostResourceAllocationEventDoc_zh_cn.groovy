package org.zstack.header.host

import org.zstack.header.errorcode.ErrorCode

doc {

	title "CPU分配信息"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.header.host.APIGetHostResourceAllocationEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
	field {
		name "name"
		desc "物理机名称"
		type "String"
		since "4.3.12"
	}
	field {
		name "uuid"
		desc "物理机uuid"
		type "String"
		since "4.3.12"
	}
	field {
		name "vCPUPin"
		desc "CPU分配信息"
		type "List"
		since "4.3.12"
	}
}
