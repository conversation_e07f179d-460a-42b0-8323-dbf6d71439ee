package org.zstack.header.host

import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "在物理网口配置ip"

	ref {
		name "inventory"
		path "org.zstack.header.host.APISetIpOnHostNetworkInterfaceEvent.inventory"
		desc "物理网口清单"
		type "HostNetworkInterfaceInventory"
		since "4.7"
		clz HostNetworkInterfaceInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.7.0"
	}
	ref {
		name "error"
		path "org.zstack.header.host.APISetIpOnHostNetworkInterfaceEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.7.0"
		clz ErrorCode.class
	}
}
