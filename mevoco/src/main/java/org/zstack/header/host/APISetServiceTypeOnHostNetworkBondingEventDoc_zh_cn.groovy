package org.zstack.header.host

import org.zstack.header.host.HostNetworkBondingServiceRefInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "在bond网口配置网络服务类型"

	ref {
		name "inventory"
		path "org.zstack.header.host.APISetServiceTypeOnHostNetworkBondingEvent.inventory"
		desc "null"
		type "List"
		since "4.7.11"
		clz HostNetworkBondingServiceRefInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.7.11"
	}
	ref {
		name "error"
		path "org.zstack.header.host.APISetServiceTypeOnHostNetworkBondingEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.7.11"
		clz ErrorCode.class
	}
}
