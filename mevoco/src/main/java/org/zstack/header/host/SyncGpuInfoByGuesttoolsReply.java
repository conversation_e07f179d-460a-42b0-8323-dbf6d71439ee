package org.zstack.header.host;

import org.zstack.header.message.MessageReply;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: qiuyu.zhang
 * @Date: 2024/8/20 09:52
 */
public class SyncGpuInfoByGuesttoolsReply extends MessageReply {
    private List<GpuInfo> gpuInfos = new ArrayList<>();

    public List<GpuInfo> getGpuInfos() {
        return gpuInfos;
    }

    public void setGpuInfos(List<GpuInfo> gpuInfos) {
        this.gpuInfos = gpuInfos;
    }
}
