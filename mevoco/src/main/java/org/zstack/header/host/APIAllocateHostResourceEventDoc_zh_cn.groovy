package org.zstack.header.host

import org.zstack.header.errorcode.ErrorCode

doc {

	title "分配的物理机计算资源信息"

	field {
		name "success"
		desc ""
		type "boolean"
		since "4.7.11"
	}
	ref {
		name "error"
		path "org.zstack.header.host.APIAllocateHostResourceEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.7.11"
		clz ErrorCode.class
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "4.7.11"
	}
	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "4.7.11"
	}
	field {
		name "vCPUPin"
		desc "为对应vCPU数量分配的pCPU信息"
		type "List"
		since "4.7.11"
	}
}
