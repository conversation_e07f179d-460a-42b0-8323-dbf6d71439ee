package org.zstack.header.host

import java.lang.Integer
import java.sql.Timestamp

doc {

	title "物理机Bond服务类型详细信息"

	field {
		name "bondingUuid"
		desc "bond网口Uuid"
		type "String"
		since "4.7.11"
	}
	field {
		name "vlanId"
		desc "vlan子接口id"
		type "Integer"
		since "4.7.11"
	}
	ref {
		name "serviceType"
		desc "网路服务类型"
		type "String"
		since "4.7.11"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "4.7.11"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "4.7.11"
	}
}
