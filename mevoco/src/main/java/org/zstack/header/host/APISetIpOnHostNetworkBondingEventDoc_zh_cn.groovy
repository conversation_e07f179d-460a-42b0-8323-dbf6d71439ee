package org.zstack.header.host

import org.zstack.network.hostNetworkInterface.HostNetworkBondingInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "在bond网口配置ip"

	ref {
		name "inventory"
		path "org.zstack.header.host.APISetIpOnHostNetworkBondingEvent.inventory"
		desc "bond网口清单"
		type "HostNetworkBondingInventory"
		since "4.7.0"
		clz HostNetworkBondingInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.7.0"
	}
	ref {
		name "error"
		path "org.zstack.header.host.APISetIpOnHostNetworkBondingEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.7.0"
		clz ErrorCode.class
	}
}
