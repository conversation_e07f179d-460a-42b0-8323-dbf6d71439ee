package org.zstack.header.host

import org.zstack.header.errorcode.ErrorCode
import org.zstack.header.host.HostPhysicalMemoryInventory

doc {

	title "获取物理机内存卡信息清单"

	ref {
		name "inventories"
		path "org.zstack.header.host.APIGetHostPhysicalMemoryFactsReply.inventories"
		desc "null"
		type "List"
		since "4.2.0"
		clz HostPhysicalMemoryInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.2.0"
	}
	ref {
		name "error"
		path "org.zstack.header.host.APIGetHostPhysicalMemoryFactsReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.2.0"
		clz ErrorCode.class
	}
}
