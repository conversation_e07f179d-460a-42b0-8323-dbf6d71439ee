package org.zstack.header.host

import org.zstack.header.host.APIGetCandidateNetworkInterfacesReply

doc {
    title "GetCandidateNetworkInterfaces"

    category "host"

    desc """获取物理机交集网卡信息"""

    rest {
        request {
			url "GET /v1/cluster/hosts-network-interfaces"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIGetCandidateNetworkInterfacesMsg.class

            desc """"""
            
			params {

				column {
					name "hostUuids"
					enclosedIn ""
					desc "物理机UUIDs"
					location "query"
					type "List"
					optional false
					since "4.7.0"
				}
				column {
					name "interfaceType"
					enclosedIn ""
					desc "网卡类型"
					location "query"
					type "String"
					optional true
					since "4.7.21"
					values ("interface","bonding","all")
				}
				column {
					name "limit"
					enclosedIn ""
					desc ""
					location "query"
					type "Integer"
					optional true
					since "4.7.0"
				}
				column {
					name "start"
					enclosedIn ""
					desc ""
					location "query"
					type "Integer"
					optional true
					since "4.7.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "query"
					type "List"
					optional true
					since "4.7.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "query"
					type "List"
					optional true
					since "4.7.0"
				}
			}
        }

        response {
            clz APIGetCandidateNetworkInterfacesReply.class
        }
    }
}