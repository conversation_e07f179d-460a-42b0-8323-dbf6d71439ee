package org.zstack.header.host;

import org.zstack.header.message.NeedReplyMessage;

import java.util.List;

/**
 * @Author: qiuyu.zhang
 * @Date: 2024/8/20 09:50
 */
public class SyncGpuInfoByGuesttoolsMsg extends NeedReplyMessage implements HostMessage {
    private String vmInstanceUuid;
    private String hostUuid;
    private List<String> vendors;

    @Override
    public String getHostUuid() {
        return hostUuid;
    }

    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    public void setHostUuid(String hostUuid) {
        this.hostUuid = hostUuid;
    }

    public List<String> getVendors() {
        return vendors;
    }

    public void setVendors(List<String> vendors) {
        this.vendors = vendors;
    }
}
