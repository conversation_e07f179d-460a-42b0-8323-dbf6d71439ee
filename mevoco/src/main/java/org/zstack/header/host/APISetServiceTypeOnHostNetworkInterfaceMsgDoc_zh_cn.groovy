package org.zstack.header.host

import org.zstack.header.host.APISetServiceTypeOnHostNetworkInterfaceEvent

doc {
    title "SetServiceTypeOnHostNetworkInterface"

    category "host"

    desc """在物理网口配置网络服务类型"""

    rest {
        request {
			url "POST /v1/hosts/nics/service-types"

			header (Authorization: 'OAuth the-session-uuid')

            clz APISetServiceTypeOnHostNetworkInterfaceMsg.class

            desc """"""
            
			params {

				column {
					name "interfaceUuids"
					enclosedIn "params"
					desc "物理网口Uuids"
					location "body"
					type "List"
					optional false
					since "4.7.11"
				}
				column {
					name "vlanIds"
					enclosedIn "params"
					desc "vlan接口Ids"
					location "body"
					type "List"
					optional true
					since "4.7.11"
				}
				column {
					name "serviceTypes"
					enclosedIn "params"
					desc "网络服务类型"
					location "body"
					type "List"
					optional true
					since "4.7.11"
					values ("ManagementNetwork","TenantNetwork","StorageNetwork","BackupNetwork","MigrationNetwork")
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "4.7.11"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "4.7.11"
				}
			}
        }

        response {
            clz APISetServiceTypeOnHostNetworkInterfaceEvent.class
        }
    }
}