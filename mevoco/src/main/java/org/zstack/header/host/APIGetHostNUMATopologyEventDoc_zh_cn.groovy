package org.zstack.header.host

import org.zstack.header.errorcode.ErrorCode
import org.zstack.header.host.HostNUMANode

doc {

	title "物理机NUMA拓扑信息"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.header.host.APIGetHostNUMATopologyEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "4.3.12"
	}
	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "4.3.12"
	}
	ref {
		name "topology"
		path "org.zstack.header.host.APIGetHostNUMATopologyEvent.topology"
		desc "物理机NUMA拓扑信息"
		type "Map"
		since "4.3.12"
		clz HostNUMANode.class
	}
}
