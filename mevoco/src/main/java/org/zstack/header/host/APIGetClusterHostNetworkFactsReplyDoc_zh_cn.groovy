package org.zstack.header.host


import org.zstack.network.hostNetworkInterface.HostNetworkBondingInventory
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "获取集群物理机物理网络信息清单"

	ref {
		name "error"
		path "org.zstack.header.host.APIGetClusterHostNetworkFactsReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.3.6"
		clz ErrorCode.class
	}
	ref {
		name "bondings"
		path "org.zstack.header.host.APIGetClusterHostNetworkFactsReply.bondings"
		desc "bond设备"
		type "List"
		since "4.3.6"
		clz HostNetworkBondingInventory.class
	}
	ref {
		name "nics"
		path "org.zstack.header.host.APIGetClusterHostNetworkFactsReply.nics"
		desc "网卡设备"
		type "List"
		since "4.3.6"
		clz HostNetworkInterfaceInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.3.6"
	}
}
