package org.zstack.header.sriov;

import org.zstack.header.message.APIReply;
import org.zstack.header.rest.RestResponse;

import java.util.*;

/**
 * Created by boce.wang on 01/15/2025.
 */
@RestResponse(fieldsTo = {"l2VfAvailableClusters"})
public class APIGetVfPciDeviceAvailableInL2NetworkReply extends APIReply {
    private Map<String, List<ClusterHostAvailabilityVfData>> l2VfAvailableClusters = new HashMap<>();

    public Map<String, List<ClusterHostAvailabilityVfData>> getL2VfAvailableClusters() {
        return l2VfAvailableClusters;
    }

    public void setL2VfAvailableClusters(Map<String, List<ClusterHostAvailabilityVfData>> l2VfAvailableClusters) {
        this.l2VfAvailableClusters = l2VfAvailableClusters;
    }

    public static APIGetVfPciDeviceAvailableInL2NetworkReply __example__() {
        APIGetVfPciDeviceAvailableInL2NetworkReply reply = new APIGetVfPciDeviceAvailableInL2NetworkReply();
        // Host -> Available VFs
        Map<String, Integer> hostAvailableVfs = new HashMap<>();
        hostAvailableVfs.put(uuid(), 10);
        // Cluster -> hostVfAvailable
        ClusterHostAvailabilityVfData clusterHostAvailableVfs = new ClusterHostAvailabilityVfData();
        clusterHostAvailableVfs.setClusterHostAvailableVfs(Collections.singletonMap(uuid(), hostAvailableVfs));
        // L2 -> clusterVfAvailable
        Map<String, List<ClusterHostAvailabilityVfData>> l2VfAvailableClusters = new HashMap<>();
        l2VfAvailableClusters.put(uuid(), Collections.singletonList(clusterHostAvailableVfs));
        reply.setL2VfAvailableClusters(l2VfAvailableClusters);
        return reply;
    }
}
