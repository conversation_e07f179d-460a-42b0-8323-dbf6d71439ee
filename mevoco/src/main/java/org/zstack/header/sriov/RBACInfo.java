package org.zstack.header.sriov;

import org.zstack.header.identity.rbac.RBACDescription;

public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("sriov")
                .normalAPIs("org.zstack.header.sriov.**")
                .build();
    }

    @Override
    public void contributeToRoles() {

    }

    @Override
    public void roles() {
        roleBuilder()
                .uuid("f19ac66cd876472096c0e01ba2b7bac4")
                .name("sriov")
                .permissionsByName("sriov")
                .build();
    }

    @Override
    public void globalReadableResources() {

    }
}
