package org.zstack.header.sriov

import org.zstack.header.errorcode.ErrorCode

doc {

	title "二层网络可用SRIOV VF清单"

	field {
		name "success"
		desc ""
		type "boolean"
		since "5.3.0"
	}
	ref {
		name "error"
		path "org.zstack.header.sriov.APIGetVfPciDeviceAvailableInL2NetworkReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.3.0"
		clz ErrorCode.class
	}
	field {
		name "l2VfAvailableClusters"
		desc ""
		type "Map"
		since "5.3.0"
	}
}
