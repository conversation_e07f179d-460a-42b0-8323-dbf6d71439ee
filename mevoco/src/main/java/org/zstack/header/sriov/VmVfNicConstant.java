package org.zstack.header.sriov;

import org.zstack.header.configuration.PythonClass;
import org.zstack.header.network.l2.L2NetworkConstant;

import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/28/19.
 */
@PythonClass
public interface VmVfNicConstant {
    String SERVICE_ID = "sriov";
    String ACTION_CATEGORY = "sriov";

    String VIRTUAL_FUNCTION_TYPE = "VF";
    String ALLOCATE_VF_NIC_PCI_DEVICE = "allocate-vf-nic-pci-device";

    String VF_NIC_PCI_DEVICE_ERROR_REV = "FF";

    List<String> SRIOVABLE_L2_NETWORK_TYPES = Arrays.asList(
            L2NetworkConstant.L2_NO_VLAN_NETWORK_TYPE,
            L2NetworkConstant.L2_VLAN_NETWORK_TYPE
    );

    enum Params {
        VmVfNicInventory,
        VmVfNicHaState,
    }
}
