package org.zstack.header.sriov;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by boce.wang on 01/15/2025.
 */
public class ClusterHostAvailabilityVfData {

    //HashMap<ClusterUuid(String), HashMap<HostUuid(String), VfAvailableNumber(Integer)>>
    private Map<String, Map<String, Integer>> clusterHostAvailableVfs = new HashMap<>();

    public Map<String, Map<String, Integer>> getClusterHostAvailableVfs() {
        return clusterHostAvailableVfs;
    }

    public void setClusterHostAvailableVfs(Map<String, Map<String, Integer>> clusterHostAvailableVfs) {
        this.clusterHostAvailableVfs = clusterHostAvailableVfs;
    }
}
