package org.zstack.header.sriov

import org.zstack.header.sriov.APIGetVfPciDeviceAvailableInL2NetworkReply

doc {
    title "GetVfPciDeviceAvailableInL2Network"

    category "sriov"

    desc """获取2层网络可用的SRIOV VF"""

    rest {
        request {
			url "GET /v1/l2-networks/vf-pci-devices-available"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIGetVfPciDeviceAvailableInL2NetworkMsg.class

            desc """"""
            
			params {

				column {
					name "l2NetworkUuids"
					enclosedIn ""
					desc "2层网络Uuid"
					location "query"
					type "List"
					optional false
					since "5.3.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "query"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "query"
					type "List"
					optional true
					since "5.3.0"
				}
			}
        }

        response {
            clz APIGetVfPciDeviceAvailableInL2NetworkReply.class
        }
    }
}