package org.zstack.header.sriov

import org.zstack.header.sriov.EthernetVfStatus
import org.zstack.pciDevice.PciDeviceType
import org.zstack.pciDevice.PciDeviceState
import org.zstack.pciDevice.PciDeviceStatus
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus
import org.zstack.pciDevice.PciDeviceChooser
import org.zstack.pciDevice.PciDeviceMetaData
import java.sql.Timestamp
import org.zstack.pciDevice.PciDevicePciDeviceOfferingRefInventory
import org.zstack.pciDevice.specification.mdev.PciDeviceMdevSpecRefInventory

doc {

	title "网卡VF清单"

	field {
		name "hostDevUuid"
		desc "物理机Uuid"
		type "String"
		since "5.0.0"
	}
	field {
		name "interfaceName"
		desc "物理网卡名称"
		type "String"
		since "5.0.0"
	}
	field {
		name "vmUuid"
		desc "云主机Uuid"
		type "String"
		since "5.0.0"
	}
	field {
		name "l3NetworkUuid"
		desc "三层网络UUID"
		type "String"
		since "5.0.0"
	}
	ref {
		name "vfStatus"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.vfStatus"
		desc "网卡VF使用状态"
		type "EthernetVfStatus"
		since "5.0.0"
		clz EthernetVfStatus.class
	}
	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "5.0.0"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "5.0.0"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "5.0.0"
	}
	field {
		name "hostUuid"
		desc "物理机UUID"
		type "String"
		since "5.0.0"
	}
	field {
		name "parentUuid"
		desc ""
		type "String"
		since "5.0.0"
	}
	field {
		name "vmInstanceUuid"
		desc "云主机UUID"
		type "String"
		since "5.0.0"
	}
	field {
		name "pciSpecUuid"
		desc ""
		type "String"
		since "5.0.0"
	}
	ref {
		name "type"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.type"
		desc "null"
		type "PciDeviceType"
		since "5.0.0"
		clz PciDeviceType.class
	}
	ref {
		name "state"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.state"
		desc "null"
		type "PciDeviceState"
		since "5.0.0"
		clz PciDeviceState.class
	}
	ref {
		name "status"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.status"
		desc "null"
		type "PciDeviceStatus"
		since "5.0.0"
		clz PciDeviceStatus.class
	}
	ref {
		name "virtStatus"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.virtStatus"
		desc "null"
		type "PciDeviceVirtStatus"
		since "5.0.0"
		clz PciDeviceVirtStatus.class
	}
	ref {
		name "chooser"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.chooser"
		desc "null"
		type "PciDeviceChooser"
		since "5.0.0"
		clz PciDeviceChooser.class
	}
	field {
		name "vendorId"
		desc ""
		type "String"
		since "5.0.0"
	}
	field {
		name "deviceId"
		desc ""
		type "String"
		since "5.0.0"
	}
	field {
		name "subvendorId"
		desc ""
		type "String"
		since "5.0.0"
	}
	field {
		name "subdeviceId"
		desc ""
		type "String"
		since "5.0.0"
	}
	field {
		name "pciDeviceAddress"
		desc ""
		type "String"
		since "5.0.0"
	}
	field {
		name "iommuGroup"
		desc ""
		type "String"
		since "5.0.0"
	}
	ref {
		name "metaData"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.metaData"
		desc "null"
		type "PciDeviceMetaData"
		since "5.0.0"
		clz PciDeviceMetaData.class
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "5.0.0"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "5.0.0"
	}
	ref {
		name "matchedPciDeviceOfferingRef"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.matchedPciDeviceOfferingRef"
		desc "null"
		type "List"
		since "5.0.0"
		clz PciDevicePciDeviceOfferingRefInventory.class
	}
	ref {
		name "mdevSpecRefs"
		path "org.zstack.header.sriov.EthernetVfPciDeviceInventory.mdevSpecRefs"
		desc "null"
		type "List"
		since "5.0.0"
		clz PciDeviceMdevSpecRefInventory.class
	}
}
