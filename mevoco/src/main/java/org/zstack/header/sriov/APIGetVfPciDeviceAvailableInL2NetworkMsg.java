package org.zstack.header.sriov;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.network.l2.L2Network;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.rest.RestRequest;

import java.util.Arrays;
import java.util.List;

/**
 * Created by boce.wang on 01/15/2025.
 */
@Action(category = VmVfNicConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/l2-networks/vf-pci-devices-available",
        responseClass = APIGetVfPciDeviceAvailableInL2NetworkReply.class,
        method = HttpMethod.GET
)
public class APIGetVfPciDeviceAvailableInL2NetworkMsg extends APISyncCallMessage {
    @APIParam(resourceType = L2NetworkVO.class, nonempty = true, checkAccount = true)
    private List<String> l2NetworkUuids;

    public List<String> getL2NetworkUuids() {
        return l2NetworkUuids;
    }

    public void setL2NetworkUuids(List<String> l2NetworkUuids) {
        this.l2NetworkUuids = l2NetworkUuids;
    }

    public static APIGetVfPciDeviceAvailableInL2NetworkMsg __example__() {
        APIGetVfPciDeviceAvailableInL2NetworkMsg msg = new APIGetVfPciDeviceAvailableInL2NetworkMsg();
        msg.setL2NetworkUuids(Arrays.asList(uuid()));
        return msg;
    }
}
