package org.zstack.header.vm

import org.zstack.header.vm.APIDetachUserDefinedXmlHookScriptFromVmEvent

doc {
    title "DetachUserDefinedXmlHookScriptFromVm"

    category "mevoco"

    desc """从云主机卸载用户自定义xml hook"""

    rest {
        request {
			url "DELETE /v1/xmlhook/vm-instances/{vmInstanceUuid}/detach"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDetachUserDefinedXmlHookScriptFromVmMsg.class

            desc """从云主机卸载用户自定义xml hook"""
            
			params {

				column {
					name "vmInstanceUuid"
					enclosedIn ""
					desc "云主机UUID"
					location "url"
					type "String"
					optional false
					since "5.2.0"
				}
				column {
					name "startupStrategy"
					enclosedIn ""
					desc "启动策略"
					location "body"
					type "String"
					optional true
					since "5.2.0"
					values ("Reboot","None")
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc "删除模式(Permissive / Enforcing，Permissive)"
					location "body"
					type "String"
					optional true
					since "5.2.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.2.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.2.0"
				}
			}
        }

        response {
            clz APIDetachUserDefinedXmlHookScriptFromVmEvent.class
        }
    }
}