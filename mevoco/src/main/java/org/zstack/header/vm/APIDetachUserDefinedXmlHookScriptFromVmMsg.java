package org.zstack.header.vm;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;

@Action(category = VmInstanceConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/xmlhook/vm-instances/{vmInstanceUuid}/detach",
        method = HttpMethod.DELETE,
        responseClass = APIDetachUserDefinedXmlHookScriptFromVmEvent.class
)
public class APIDetachUserDefinedXmlHookScriptFromVmMsg extends APIDeleteMessage implements APIAuditor, VmInstanceMessage {
    @APIParam(resourceType = VmInstanceVO.class, checkAccount = true, operationTarget = true)
    private String vmInstanceUuid;

    @APIParam(validValues = {"Reboot", "None"}, required = false)
    private String startupStrategy;

    public String getStartupStrategy() {
        return startupStrategy;
    }

    public void setStartupStrategy(String startupStrategy) {
        this.startupStrategy = startupStrategy;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    @Override
    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(((APIDetachUserDefinedXmlHookScriptFromVmMsg) msg).getVmInstanceUuid(), VmInstanceVO.class);
    }

    public static APIDetachUserDefinedXmlHookScriptFromVmMsg __example__() {
        APIDetachUserDefinedXmlHookScriptFromVmMsg msg = new APIDetachUserDefinedXmlHookScriptFromVmMsg();
        msg.setVmInstanceUuid(uuid());
        msg.setStartupStrategy("None");
        return msg;
    }
}
