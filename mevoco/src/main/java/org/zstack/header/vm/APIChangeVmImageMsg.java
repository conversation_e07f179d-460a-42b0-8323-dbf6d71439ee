package org.zstack.header.vm;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.image.ImageVO;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.DefaultTimeout;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/2/17.
 */
@Action(category = VmInstanceConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/vm-instances/{vmInstanceUuid}/actions",
        isAction = true,
        method = HttpMethod.PUT,
        responseClass = APIChangeVmImageEvent.class
)
@DefaultTimeout(timeunit = TimeUnit.HOURS, value = 24)
public class APIChangeVmImageMsg extends APICreateMessage implements VmInstanceMessage {
    @APIParam(resourceType = VmInstanceVO.class, checkAccount = true, operationTarget = true)
    private String vmInstanceUuid;

    @APIParam(resourceType = ImageVO.class, checkAccount = true)
    private String imageUuid;

    @Override
    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    public String getImageUuid() {
        return imageUuid;
    }

    public void setImageUuid(String imageUuid) {
        this.imageUuid = imageUuid;
    }

    public static APIChangeVmImageMsg __example__() {
        APIChangeVmImageMsg msg = new APIChangeVmImageMsg();
        msg.setVmInstanceUuid(uuid());
        msg.setImageUuid(uuid());
        return msg;
    }
}
