package org.zstack.header.vm;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

@Action(category = VmInstanceConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/vm-instances/{vmInstanceUuid}/xml-hook-script",
        method = HttpMethod.DELETE,
        responseClass = APIDeleteVmUserDefinedXmlHookScriptEvent.class
)
public class APIDeleteVmUserDefinedXmlHookScriptMsg extends APIDeleteMessage implements VmInstanceMessage {
    @APIParam(resourceType = VmInstanceVO.class, checkAccount = true, operationTarget = true)
    private String vmInstanceUuid;

    @Override
    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    public static APIDeleteVmUserDefinedXmlHookScriptMsg __example__() {
        APIDeleteVmUserDefinedXmlHookScriptMsg msg = new APIDeleteVmUserDefinedXmlHookScriptMsg();
        msg.setVmInstanceUuid(uuid());
        return msg;
    }
}
