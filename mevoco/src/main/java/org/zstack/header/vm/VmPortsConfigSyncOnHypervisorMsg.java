package org.zstack.header.vm;

import org.zstack.header.host.HostMessage;
import org.zstack.header.message.NeedReplyMessage;

public class VmPortsConfigSyncOnHypervisorMsg extends NeedReplyMessage implements HostMessage {
    private String hostUuid;

    private String vmInstanceUuid;

    private VmConfigSyncStruct.VmPortsConfig portsConfig;

    private String hostname;

    private String defaultIP;

    @Override
    public String getHostUuid() {
        return hostUuid;
    }

    public void setHostUuid(String hostUuid) {
        this.hostUuid = hostUuid;
    }

    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    public VmConfigSyncStruct.VmPortsConfig getPortsConfig() {return portsConfig;}

    public void setPortsConfig(VmConfigSyncStruct.VmPortsConfig portsConfig) {this.portsConfig = portsConfig;}

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public String getDefaultIP() {
        return defaultIP;
    }

    public void setDefaultIP(String defaultIP) {
        this.defaultIP = defaultIP;
    }
}
