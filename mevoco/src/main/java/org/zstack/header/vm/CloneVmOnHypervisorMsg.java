package org.zstack.header.vm;

import org.zstack.header.host.HostMessage;
import org.zstack.header.message.NeedReplyMessage;


public class CloneVmOnHypervisorMsg extends NeedReplyMessage implements HostMessage {
    private VmInstanceInventory vmInventory;
    private String name;
    private String strategy;
    private String accountUuid;

    public VmInstanceInventory getVmInventory() {
        return vmInventory;
    }

    public void setVmInventory(VmInstanceInventory vmInventory) {
        this.vmInventory = vmInventory;
    }

    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }

    public void setStrategy(String strategy){
        this.strategy = strategy;
    }

    public String getStrategy(){
        return strategy;
    }

    public void setAccountUuid(String accountUuid){
        this.accountUuid = accountUuid;
    }

    public String getAccountUuid(){
        return accountUuid;
    }

    @Override
    public String getHostUuid() {
        if (vmInventory.getHostUuid() != null) {
            return vmInventory.getHostUuid();
        } else {
            return vmInventory.getLastHostUuid();
        }
    }
}

