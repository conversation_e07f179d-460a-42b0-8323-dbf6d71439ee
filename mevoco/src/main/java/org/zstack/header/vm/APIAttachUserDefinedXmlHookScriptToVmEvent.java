package org.zstack.header.vm;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse
public class APIAttachUserDefinedXmlHookScriptToVmEvent extends APIEvent {
    public APIAttachUserDefinedXmlHookScriptToVmEvent() {
    }

    public APIAttachUserDefinedXmlHookScriptToVmEvent(String apiId) {
        super(apiId);
    }

    public static APIAttachUserDefinedXmlHookScriptToVmEvent __example__() {
        APIAttachUserDefinedXmlHookScriptToVmEvent event = new APIAttachUserDefinedXmlHookScriptToVmEvent();
        return event;
    }
}
