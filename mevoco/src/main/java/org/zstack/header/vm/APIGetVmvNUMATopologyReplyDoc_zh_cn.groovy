package org.zstack.header.vm

import org.zstack.header.errorcode.ErrorCode

doc {

	title "云主机NUMA拓扑信息"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.header.vm.APIGetVmvNUMATopologyReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
	field {
		name "name"
		desc "云主机名称"
		type "String"
		since "4.3.12"
	}
	field {
		name "uuid"
		desc "云主机uuid"
		type "String"
		since "4.3.12"
	}
	field {
		name "hostUuid"
		desc "物理机UUID"
		type "String"
		since "4.3.12"
	}
	field {
		name "topology"
		desc "云主机NUMA拓扑信息"
		type "List"
		since "4.3.12"
	}
}
