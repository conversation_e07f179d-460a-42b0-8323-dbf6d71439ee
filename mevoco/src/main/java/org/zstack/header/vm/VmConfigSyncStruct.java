package org.zstack.header.vm;

import org.zstack.network.service.NetworkServiceHelper.HostRouteInfo;

import java.util.List;

public class VmConfigSyncStruct {

    public static class VmConfigSyncBase {
        private String vmUUid;

        public String getVmUUid() {
            return vmUUid;
        }

        public void setVmUUid(String vmUUid) {
            this.vmUUid = vmUUid;
        }
    }

    public static class VmIpConfig{
        private int version;         // v4 or v6
        private String proto;         // dhcp or static
        private String ip;
        private String netmask;
        private List<String> dns;
        private String gateway;
        private List<HostRouteInfo> routes;

        public List<HostRouteInfo> getRoutes() {
            return routes;
        }

        public void setRoutes(List<HostRouteInfo> routes) {
            this.routes = routes;
        }

        public int getVersion() { return version;}

        public void setVersion(int version) {this.version = version;}

        public String getProto() {return proto;}

        public void setProto(String proto) {this.proto = proto;}

        public String getIp() {return ip;}

        public void setIp(String ip) {this.ip = ip;}

        public String getNetmask() {return netmask;}

        public void setNetmask(String netmask) {this.netmask = netmask;}

        public List<String> getDns() {return dns;}

        public void setDns(List<String> dns) {this.dns = dns;}

        public String getGateway() {return gateway;}

        public void setGateway(String gateway) {this.gateway = gateway;}
    }

    public static class VmPortConfig {
        private List<VmIpConfig> vmIps;
        private String mac;
        private Integer mtu;
        private Boolean isDefault;
        private String haState;

        public List<VmIpConfig> getVmIps() {return vmIps;}

        public void setVmIps(List<VmIpConfig> vmIps) {this.vmIps = vmIps;}

        public String getMac() {return mac;}

        public void setMac(String mac) {this.mac = mac;}

        public Integer getMtu() {return mtu;}

        public void setMtu(Integer mtu) {this.mtu = mtu;}

        public Boolean getDefault() {
            return isDefault;
        }

        public void setDefault(Boolean aDefault) {
            isDefault = aDefault;
        }

        public String getHaState() {
            return this.haState;
        }

        public void setHaState(String haState) {
            this.haState = haState;
        }
    }

    public static class VmHostNameConfig extends VmConfigSyncBase {
        private String hostName;

        public String getHostName() {return hostName;}

        public void setHostName(String hostName) {this.hostName = hostName;}
    }

    public static class VmPortsConfig extends VmConfigSyncBase {
        private List<VmPortConfig> ports;

        public List<VmPortConfig> getPorts() {return ports;}

        public void setPorts(List<VmPortConfig> ports) {this.ports = ports;}
    }

}
