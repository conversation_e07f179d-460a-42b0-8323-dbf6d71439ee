package org.zstack.header.vm;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.DefaultTimeout;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;

import java.util.concurrent.TimeUnit;

@Action(category = VmInstanceConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/xmlhook/{xmlHookUuid}/vm-instances/{vmInstanceUuid}",
        method = HttpMethod.POST,
        parameterName = "params",
        responseClass = APIAttachUserDefinedXmlHookScriptToVmEvent.class
)
public class APIAttachUserDefinedXmlHookScriptToVmMsg extends APIMessage implements APIAuditor, VmInstanceMessage {
    @APIParam(resourceType = VmInstanceVO.class, checkAccount = true, operationTarget = true)
    private String vmInstanceUuid;

    @APIParam(required = true)
    private String xmlHookUuid;

    @APIParam(validValues = {"Reboot", "None"}, required = false)
    private String startupStrategy;

    public String getXmlHookUuid() {
        return xmlHookUuid;
    }

    public void setXmlHookUuid(String xmlHookUuid) {
        this.xmlHookUuid = xmlHookUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    @Override
    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(((APIAttachUserDefinedXmlHookScriptToVmMsg) msg).getVmInstanceUuid(), VmInstanceVO.class);
    }

    public String getStartupStrategy() {
        return startupStrategy;
    }

    public void setStartupStrategy(String startupStrategy) {
        this.startupStrategy = startupStrategy;
    }

    public static APIAttachUserDefinedXmlHookScriptToVmMsg __example__() {
        APIAttachUserDefinedXmlHookScriptToVmMsg msg = new APIAttachUserDefinedXmlHookScriptToVmMsg();
        msg.setVmInstanceUuid(uuid());
        msg.setXmlHookUuid(uuid());
        msg.setStartupStrategy("None");
        return msg;
    }
}
