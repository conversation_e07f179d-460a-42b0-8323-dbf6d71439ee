package org.zstack.header.vm

import org.zstack.header.vm.APIAttachUserDefinedXmlHookScriptToVmEvent

doc {
    title "AttachUserDefinedXmlHookScriptToVm"

    category "mevoco"

    desc """挂载用户自定义xml hook到云主机"""

    rest {
        request {
			url "POST /v1/xmlhook/{xmlHookUuid}/vm-instances/{vmInstanceUuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIAttachUserDefinedXmlHookScriptToVmMsg.class

            desc """挂载用户自定义xml hook到云主机"""
            
			params {

				column {
					name "vmInstanceUuid"
					enclosedIn "params"
					desc "云主机UUID"
					location "url"
					type "String"
					optional false
					since "5.2.0"
				}
				column {
					name "xmlHookUuid"
					enclosedIn "params"
					desc "xml hook UUID"
					location "url"
					type "String"
					optional false
					since "5.2.0"
				}
				column {
					name "startupStrategy"
					enclosedIn "params"
					desc "启动策略"
					location "body"
					type "String"
					optional true
					since "5.2.0"
					values ("Reboot","None")
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.2.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.2.0"
				}
			}
        }

        response {
            clz APIAttachUserDefinedXmlHookScriptToVmEvent.class
        }
    }
}