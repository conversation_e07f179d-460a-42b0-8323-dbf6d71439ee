package org.zstack.header.vm;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse
public class APIDetachUserDefinedXmlHookScriptFromVmEvent extends APIEvent {
    public APIDetachUserDefinedXmlHookScriptFromVmEvent() {
    }

    public APIDetachUserDefinedXmlHookScriptFromVmEvent(String apiId) {
        super(apiId);
    }

    public static APIDetachUserDefinedXmlHookScriptFromVmEvent __example__() {
        APIDetachUserDefinedXmlHookScriptFromVmEvent event = new APIDetachUserDefinedXmlHookScriptFromVmEvent();
        return event;
    }
}
