package org.zstack.header.vm;

import org.zstack.header.identity.rbac.RBACDescription;

public class RBAInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .adminOnlyAPIs(
                        APISetVmUserDefinedXmlHookScriptMsg.class,
                        APIDeleteVmUserDefinedXmlHookScriptMsg.class,
                        APIAttachUserDefinedXmlHookScriptToVmMsg.class,
                        APIDetachUserDefinedXmlHookScriptFromVmMsg.class
                )
                .build();
    }

    @Override
    public void contributeToRoles() {

    }

    @Override
    public void roles() {

    }

    @Override
    public void globalReadableResources() {

    }
}
