package org.zstack.header.vm;

import org.zstack.header.identity.SessionInventory;
import org.zstack.header.message.NeedReplyMessage;

import java.util.ArrayList;
import java.util.List;

public class CloneVmInstanceMsg extends NeedReplyMessage implements VmInstanceMessage {
    private String vmInstanceUuid;
    private List<String> names;
    private String strategy;
    private boolean full = false;
    private SessionInventory session;
    private String primaryStorageUuidForRootVolume;
    private String primaryStorageUuidForDataVolume;
    private List<String> rootVolumeSystemTags;
    private List<String> dataVolumeSystemTags;
    private String clusterUuid;
    private String hostUuid;
    private List<VmNicParam> vmNicParms = new ArrayList<>();

    @Override
    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid){
        this.vmInstanceUuid = vmInstanceUuid;
    }

    public void setNames(List<String> names){
        this.names = names;
    }

    public List<String> getNames(){
        return names;
    }

    public void setStrategy(String strategy){
        this.strategy = strategy;
    }

    public String getStrategy(){
        return strategy;
    }

    public boolean isFull() {
        return full;
    }

    public void setFull(boolean full) {
        this.full = full;
    }

    public SessionInventory getSession() {
        return session;
    }

    public void setSession(SessionInventory session) {
        this.session = session;
    }

    public String getPrimaryStorageUuidForRootVolume() {
        return primaryStorageUuidForRootVolume;
    }

    public void setPrimaryStorageUuidForRootVolume(String primaryStorageUuidForRootVolume) {
        this.primaryStorageUuidForRootVolume = primaryStorageUuidForRootVolume;
    }

    public String getPrimaryStorageUuidForDataVolume() {
        return primaryStorageUuidForDataVolume;
    }

    public void setPrimaryStorageUuidForDataVolume(String primaryStorageUuidForDataVolume) {
        this.primaryStorageUuidForDataVolume = primaryStorageUuidForDataVolume;
    }

    public List<String> getRootVolumeSystemTags() {
        return rootVolumeSystemTags;
    }

    public void setRootVolumeSystemTags(List<String> rootVolumeSystemTags) {
        this.rootVolumeSystemTags = rootVolumeSystemTags;
    }

    public List<String> getDataVolumeSystemTags() {
        return dataVolumeSystemTags;
    }

    public void setDataVolumeSystemTags(List<String> dataVolumeSystemTags) {
        this.dataVolumeSystemTags = dataVolumeSystemTags;
    }

    public String getClusterUuid() {
        return clusterUuid;
    }

    public void setClusterUuid(String clusterUuid) {
        this.clusterUuid = clusterUuid;
    }

    public String getHostUuid() {
        return hostUuid;
    }

    public void setHostUuid(String hostUuid) {
        this.hostUuid = hostUuid;
    }

    public List<VmNicParam> getVmNicParms() {
        return vmNicParms;
    }

    public void setVmNicParms(List<VmNicParam> vmNicParms) {
        this.vmNicParms = vmNicParms;
    }
}
