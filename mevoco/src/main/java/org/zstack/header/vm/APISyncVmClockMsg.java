package org.zstack.header.vm;

import org.springframework.http.HttpMethod;
import org.zstack.core.Platform;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

@Action(category = VmInstanceConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/vm-instances/{uuid}/actions",
        isAction = true,
        method = HttpMethod.PUT,
        responseClass = APISyncVmClockEvent.class
)
public class APISyncVmClockMsg extends APIMessage implements VmInstanceMessage {
    @APIParam(resourceType = VmInstanceVO.class, checkAccount = true, operationTarget = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public String getVmInstanceUuid() {
        return uuid;
    }

    public static APISyncVmClockMsg __example__() {
        APISyncVmClockMsg msg = new APISyncVmClockMsg();
        msg.setUuid(Platform.getUuid());
        return msg;
    }
}
