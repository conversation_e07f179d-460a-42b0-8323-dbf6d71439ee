package org.zstack.header.vm;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;


/**
 * <NAME_EMAIL> on 7/7/17
 */
@Action(category = VmInstanceConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/vm-instances/{uuid}/actions",
        isAction = true,
        method = HttpMethod.PUT,
        responseClass = APISetVmMonitorNumberEvent.class
)
public class APISetVmMonitorNumberMsg extends APIMessage implements VmInstanceMessage {
    @APIParam(resourceType = VmInstanceVO.class, checkAccount = true, operationTarget = true)
    private String uuid;
    @APIParam
    private Integer monitorNumber;

    @Override
    public String getVmInstanceUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public Integer getMonitorNumber() {
        return monitorNumber;
    }

    public void setMonitorNumber(Integer monitorNumber) {
        this.monitorNumber = monitorNumber;
    }

    public static APISetVmMonitorNumberMsg __example__() {
        APISetVmMonitorNumberMsg msg = new APISetVmMonitorNumberMsg();
        msg.uuid = uuid();
        msg.monitorNumber = 2;
        return msg;
    }
}
