package org.zstack.monitoring;

import org.zstack.header.identity.rbac.RBACDescription;
import org.zstack.monitoring.prometheus.APIPrometheusQueryPassThroughMsg;

/**
 * Created by kayo on 2018/7/10.
 */
public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .adminOnlyAPIs("org.zstack.monitoring.**")
                .normalAPIs(APIPrometheusQueryPassThroughMsg.class)
                .build();
    }

    @Override
    public void contributeToRoles() {

    }

    @Override
    public void roles() {

    }

    @Override
    public void globalReadableResources() {

    }
}
