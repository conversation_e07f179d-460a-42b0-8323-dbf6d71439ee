package org.zstack.monitoring.media;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.monitoring.MonitorConstants;

/**
 * Created by xing5 on 2017/6/11.
 */
@RestRequest(
        path = "/media/{uuid}",
        method = HttpMethod.DELETE,
        responseClass = APIDeleteMediaEvent.class
)
@Action(category = MonitorConstants.ACTION_CATEGORY)
@Deprecated
public class APIDeleteMediaMsg extends APIDeleteMessage implements MediaMessage {
    @APIParam(resourceType = MediaVO.class, successIfResourceNotExisting = true, checkAccount = true, operationTarget = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public String getMediaMessageUuid() {
        return uuid;
    }

    public static APIDeleteMediaMsg __example__() {
        APIDeleteMediaMsg msg = new APIDeleteMediaMsg();
        msg.uuid = uuid();
        return msg;
    }
}
