package org.zstack.monitoring.prometheus;

import org.zstack.header.identity.rbac.RBACDescription;

public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("prometheus-query")
                .adminOnlyAPIs("org.zstack.monitoring.prometheus.**")
                .normalAPIs(APIPrometheusQueryPassThroughMsg.class)
                .build();
    }

    @Override
    public void contributeToRoles() {
        roleContributorBuilder()
                .roleName("other")
                .actionsByPermissionName("prometheus-query")
                .build();
    }

    @Override
    public void roles() {

    }

    @Override
    public void globalReadableResources() {

    }
}
