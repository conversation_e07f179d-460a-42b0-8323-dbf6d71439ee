package org.zstack.zwatch.namespace;

import org.zstack.license.LicenseExpiredTimeInfo;
import org.zstack.zwatch.alarm.AlarmAction;
import org.zstack.zwatch.datatype.*;
import org.zstack.zwatch.datatype.metric.CountMetric;
import org.zstack.zwatch.datatype.metric.Metric;
import org.zstack.zwatch.driver.DatabaseDriver;
import org.zstack.zwatch.utils.ParserUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class LicenseNamespace extends AbstractNamespace {
    public static final String NAME = "License";

    private static final List<Metric> metrics = new ArrayList<>();
    protected static final List<String> disableMetrics = getDisableMetrics(NAME);

    public static final Metric LicenseEnabledDays = new CountMetric("LicenseEnabledDays", metrics,
            LabelNames.UUID, LabelNames.ResourceType
    );

    public LicenseNamespace() {
    }

    public LicenseNamespace(DatabaseDriver driver) {
        super(driver);
    }

    public enum LabelNames {
        UUID,
        ResourceType
    }

    @Override
    protected String getSubNamespaceName() {
        return NAME;
    }

    @Override
    public List<Metric> getMetrics() {
        return metrics.stream().filter(m -> !disableMetrics.contains(m.getName())).collect(Collectors.toList());
    }

    @Override
    public List<EventFamily> getEvents() {
        return null;
    }

    @Override
    public String getResourceType() {
        return LicenseExpiredTimeInfo.class.getSimpleName();
    }

    @Override
    public String getIdentityLabelName() {
        return LabelNames.UUID.toString();
    }

    @Override
    public String getResourceNameIfNull(AlarmAction.TakeAlarmActionParam param) {
        String filterLabel = String.format("%s=%s",
                LabelNames.UUID, ParserUtils.parseIdentifyLabel(param.identifyLabel).get(getIdentityLabelName())
        );
        List<String> filterLabels = Collections.singletonList(filterLabel);
        List<String> labels = Collections.singletonList(LabelNames.ResourceType.toString());

        List<Label> allLabels = filterLabels.stream().map(Label::new).collect(Collectors.toList());
        List<Label> filters = allLabels.stream()
                .filter(l -> l.getOp() == Label.Operator.Filter)
                .collect(Collectors.toList());
        List<Label> metricLabels = allLabels.stream().filter(l -> l.getOp() == Label.Operator.Equal ||
                l.getOp() == Label.Operator.Regex).collect(Collectors.toList());

        LabelValueQueryObject qo = new LabelValueQueryObject();
        qo.setMetricName(LicenseEnabledDays.getName());
        qo.setLabelNames(labels);
        qo.setAccountUuid(param.alarmAccountUuid);
        qo.setNamespaceName(getName());
        qo.setFilteredLabels(metricLabels);
        qo.setFilters(filters);

        List<Map> ret = queryLabelValues(qo);
        return !ret.isEmpty() ? (String) ret.get(0).get(LabelNames.ResourceType.toString()) : null;
    }
}
