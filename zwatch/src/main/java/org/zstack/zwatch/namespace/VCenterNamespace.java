package org.zstack.zwatch.namespace;

import org.zstack.header.core.StaticInit;
import org.zstack.vmware.VCenterVO;
import org.zstack.zwatch.datatype.EventFamily;
import org.zstack.zwatch.datatype.metric.*;
import org.zstack.zwatch.driver.DatabaseDriver;
import org.zstack.zwatch.namespace.event.VCenterNamespaceEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class VCenterNamespace extends AbstractNamespace {
    public static final String NAME = "VCenter";
    private static final List<Metric> metrics = new ArrayList<>();
    protected static final List<String> disableMetrics = getDisableMetrics(NAME);
    private static final List<EventFamily> events = new ArrayList<>();

    public enum LabelNames {
        VMUuid,
        HostUuid,
        CPUNum,
        DiskDeviceLetter,
        NetworkDeviceLetter,
    }

    public static final Metric VmCPUUsage = new PercentMetric("VmCPUUsage", metrics, LabelNames.VMUuid);
    public static final Metric VmCPUUsageMHZ = new CountMetric("VmCPUUsageMHZ", metrics, LabelNames.VMUuid,
            LabelNames.CPUNum
    );
    public static final Metric VmCPUIdle = new PercentMetric("VmCPUIdle", metrics, LabelNames.VMUuid,
            LabelNames.CPUNum
    );
    public static final Metric VmCPUUsed = new PercentMetric("VmCPUUsed", metrics, LabelNames.VMUuid,
            LabelNames.CPUNum
    );

    public static final Metric VmMemoryUsage = new PercentMetric("VmMemoryUsage", metrics, LabelNames.VMUuid);
    public static final Metric VmMemoryGranted = new ByteSizeMetric("VmMemoryGranted", metrics, LabelNames.VMUuid);
    public static final Metric VmMemoryActive = new ByteSizeMetric("VmMemoryActive", metrics, LabelNames.VMUuid);
    public static final Metric VmMemoryVmMemCtl = new ByteSizeMetric("VmMemoryVmMemCtl", metrics, LabelNames.VMUuid);
    public static final Metric VmMemoryConsumed = new ByteSizeMetric("VmMemoryConsumed", metrics, LabelNames.VMUuid);
    public static final Metric VmMemoryEntitlement = new ByteSizeMetric("VmMemoryEntitlement", metrics,
            LabelNames.VMUuid
    );

    public static final Metric VmDiskUsage = new ByteSizeMetric("VmDiskUsage", metrics, LabelNames.VMUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric VmDiskRead = new ByteRateMetric("VmDiskRead", metrics, LabelNames.VMUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric VmDiskWrite = new ByteRateMetric("VmDiskWrite", metrics, LabelNames.VMUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric VmDiskMaxTotalLatency = new CountMetric("VmDiskMaxTotalLatency", metrics,
            LabelNames.VMUuid, LabelNames.DiskDeviceLetter
    );

    public static final Metric VmVirtualDiskNumberReadAveraged = new OperationRateMetric(
            "VmVirtualDiskNumberReadAveraged", metrics, LabelNames.VMUuid, LabelNames.DiskDeviceLetter);
    public static final Metric VmVirtualDiskNumberWriteAveraged = new OperationRateMetric(
            "VmVirtualDiskNumberWriteAveraged", metrics, LabelNames.VMUuid, LabelNames.DiskDeviceLetter);
    public static final Metric VmVirtualDiskRead = new ByteRateMetric("VmVirtualDiskRead", metrics, LabelNames.VMUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric VmVirtualDiskWrite = new ByteRateMetric("VmVirtualDiskWrite", metrics, LabelNames.VMUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric VmVirtualDiskTotalReadLatency = new CountMetric("VmVirtualDiskTotalReadLatency", metrics,
            LabelNames.VMUuid, LabelNames.DiskDeviceLetter
    );
    public static final Metric VmVirtualDiskTotalWriteLatency = new CountMetric("VmVirtualDiskTotalWriteLatency",
            metrics, LabelNames.VMUuid, LabelNames.DiskDeviceLetter
    );

    public static final Metric VmNetworkUsage = new ByteRateMetric("VmNetworkUsage", metrics, LabelNames.VMUuid,
            LabelNames.NetworkDeviceLetter
    );
    public static final Metric VmNetworkPacketRx = new PacketRateMetric("VmNetworkPacketRx", metrics, LabelNames.VMUuid,
            LabelNames.NetworkDeviceLetter
    );
    public static final Metric VmNetworkPacketTx = new PacketRateMetric("VmNetworkPacketTx", metrics, LabelNames.VMUuid,
            LabelNames.NetworkDeviceLetter
    );
    public static final Metric VmNetworkReceived = new ByteRateMetric("VmNetworkReceived", metrics, LabelNames.VMUuid,
            LabelNames.NetworkDeviceLetter
    );
    public static final Metric VmNetworkTransmitted = new ByteRateMetric("VmNetworkTransmitted", metrics,
            LabelNames.VMUuid, LabelNames.NetworkDeviceLetter
    );
    public static final Metric VmNetworkByteRx = new ByteRateMetric("VmNetworkByteRx", metrics, LabelNames.VMUuid,
            LabelNames.NetworkDeviceLetter
    );
    public static final Metric VmNetworkByteTx = new ByteRateMetric("VmNetworkByteTx", metrics, LabelNames.VMUuid,
            LabelNames.NetworkDeviceLetter
    );

    public static final Metric HostCPUUsage = new PercentMetric("HostCPUUsage", metrics, LabelNames.HostUuid);
    public static final Metric HostCPUUsageMHZ = new CountMetric("HostCPUUsageMHZ", metrics, LabelNames.HostUuid,
            LabelNames.CPUNum
    );
    public static final Metric HostCPUIdle = new PercentMetric("HostCPUIdle", metrics, LabelNames.HostUuid,
            LabelNames.CPUNum
    );
    public static final Metric HostCPUUsed = new PercentMetric("HostCPUUsed", metrics, LabelNames.HostUuid,
            LabelNames.CPUNum
    );

    public static final Metric HostMemoryUsage = new PercentMetric("HostMemoryUsage", metrics, LabelNames.HostUuid);
    public static final Metric HostMemoryGranted = new ByteSizeMetric("HostMemoryGranted", metrics,
            LabelNames.HostUuid
    );
    public static final Metric HostMemoryActive = new ByteSizeMetric("HostMemoryActive", metrics, LabelNames.HostUuid);
    public static final Metric HostMemoryHostMemCtl = new ByteSizeMetric("HostMemoryHostMemCtl", metrics,
            LabelNames.HostUuid
    );
    public static final Metric HostMemoryConsumed = new ByteSizeMetric("HostMemoryConsumed", metrics,
            LabelNames.HostUuid
    );
    public static final Metric HostMemoryEntitlement = new ByteSizeMetric("HostMemoryEntitlement", metrics,
            LabelNames.HostUuid
    );

    public static final Metric HostDiskUsage = new ByteSizeMetric("HostDiskUsage", metrics, LabelNames.HostUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric HostDiskRead = new ByteRateMetric("HostDiskRead", metrics, LabelNames.HostUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric HostDiskWrite = new ByteRateMetric("HostDiskWrite", metrics, LabelNames.HostUuid,
            LabelNames.DiskDeviceLetter
    );
    public static final Metric HostDiskMaxTotalLatency = new CountMetric("HostDiskMaxTotalLatency", metrics,
            LabelNames.HostUuid, LabelNames.DiskDeviceLetter
    );

    public static final Metric HostVirtualDiskNumberReadAveraged = new OperationRateMetric(
            "HostVirtualDiskNumberReadAveraged", metrics, LabelNames.HostUuid, LabelNames.DiskDeviceLetter);
    public static final Metric HostVirtualDiskNumberWriteAveraged = new OperationRateMetric(
            "HostVirtualDiskNumberWriteAveraged", metrics, LabelNames.HostUuid, LabelNames.DiskDeviceLetter);
    public static final Metric HostVirtualDiskRead = new ByteRateMetric("HostVirtualDiskRead", metrics,
            LabelNames.HostUuid, LabelNames.DiskDeviceLetter
    );
    public static final Metric HostVirtualDiskWrite = new ByteRateMetric("HostVirtualDiskWrite", metrics,
            LabelNames.HostUuid, LabelNames.DiskDeviceLetter
    );
    public static final Metric HostVirtualDiskTotalReadLatency = new CountMetric("HostVirtualDiskTotalReadLatency",
            metrics, LabelNames.HostUuid, LabelNames.DiskDeviceLetter
    );
    public static final Metric HostVirtualDiskTotalWriteLatency = new CountMetric("HostVirtualDiskTotalWriteLatency",
            metrics, LabelNames.HostUuid, LabelNames.DiskDeviceLetter
    );

    public static final Metric HostNetworkUsage = new ByteRateMetric("HostNetworkUsage", metrics, LabelNames.HostUuid,
            LabelNames.NetworkDeviceLetter
    );
    public static final Metric HostNetworkPacketRx = new PacketRateMetric("HostNetworkPacketRx", metrics,
            LabelNames.HostUuid, LabelNames.NetworkDeviceLetter
    );
    public static final Metric HostNetworkPacketTx = new PacketRateMetric("HostNetworkPacketTx", metrics,
            LabelNames.HostUuid, LabelNames.NetworkDeviceLetter
    );
    public static final Metric HostNetworkReceived = new ByteRateMetric("HostNetworkReceived", metrics,
            LabelNames.HostUuid, LabelNames.NetworkDeviceLetter
    );
    public static final Metric HostNetworkTransmitted = new ByteRateMetric("HostNetworkTransmitted", metrics,
            LabelNames.HostUuid, LabelNames.NetworkDeviceLetter
    );
    public static final Metric HostNetworkByteRx = new ByteRateMetric("HostNetworkByteRx", metrics, LabelNames.HostUuid,
            LabelNames.NetworkDeviceLetter
    );
    public static final Metric HostNetworkByteTx = new ByteRateMetric("HostNetworkByteTx", metrics, LabelNames.HostUuid,
            LabelNames.NetworkDeviceLetter
    );

    @StaticInit
    static void staticInit() {
        new VCenterNamespaceEvent();
    }

    public enum EventLabelNames {
        TimeDifferenceInHour,
    }

    public enum ResourceEventLabelNames {
        Description,
        Severity,
        Time,
        Target,
        User,
    }

    public static final EventFamily VCenterHostWrongDateTime = new EventFamily("VCenterHostWrongDateTime", events,
            EventLabelNames.TimeDifferenceInHour
    ).setEmergencyLevel(EventFamily.EmergencyLevel.Emergent);

    public static final EventFamily VCenterResourceEvent = new EventFamily("VCenterResourceEvent", events,
            ResourceEventLabelNames.Description,
            ResourceEventLabelNames.Severity, ResourceEventLabelNames.Time, ResourceEventLabelNames.Target,
            ResourceEventLabelNames.User
    ).setEmergencyLevel(EventFamily.EmergencyLevel.Normal);

    public VCenterNamespace(DatabaseDriver driver) {
        super(driver);
    }

    public VCenterNamespace() {
        super();
    }

    @Override
    protected String getSubNamespaceName() {
        return NAME;
    }

    @Override
    public List<Metric> getMetrics() {
        return metrics.stream().filter(m -> !disableMetrics.contains(m.getName())).collect(Collectors.toList());
    }

    @Override
    public List<EventFamily> getEvents() {
        return events;
    }

    @Override
    public String getResourceType() {
        return VCenterVO.class.getSimpleName();
    }

    @Override
    public String getIdentityLabelName() {
        return null;
    }



}
