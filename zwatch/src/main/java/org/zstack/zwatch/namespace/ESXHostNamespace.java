package org.zstack.zwatch.namespace;

import org.zstack.header.core.StaticInit;
import org.zstack.vmware.ESXHostVO;
import org.zstack.zwatch.datatype.EventFamily;
import org.zstack.zwatch.datatype.metric.CountMetric;
import org.zstack.zwatch.datatype.metric.Metric;
import org.zstack.zwatch.datatype.metric.PercentMetric;
import org.zstack.zwatch.driver.DatabaseDriver;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/23 13:38
 */
public class ESXHostNamespace extends HostAbstractNamespace{
    public static final String NAME = "ESXHost";

    public static final List<Metric> esxMetrics = new ArrayList<>();
    protected static final List<String> disableMetrics = getDisableMetrics(NAME);
    private static final List<EventFamily> esxEvents = new ArrayList<>();

    @StaticInit
    static void staticInit() {
    }

    public enum LabelNames {
        ESXHostUuid,
        CPUNum,
        DiskDeviceLetter,
        NetworkDeviceLetter,
        NetworkServiceType,
        MountPoint,
        HypervisorType,
        VolumeGroupName,
        TargetId,
        SlotNumber,
        DiskGroup,
        PowerSupplyId,
        InterfaceName,
        InterfaceSpeed,
        FSType,
        Wwid,
        FanSpeedName,
        SerialNumber
    }

    //ESX
    public static final Metric ESXHostTotal = new CountMetric("ESXHostTotal", esxMetrics, LabelNames.ESXHostUuid);
    public static final Metric ESXConnectedHostCount = new CountMetric("ESXConnectedHostCount", esxMetrics, LabelNames.ESXHostUuid);
    public static final Metric ESXConnectedHostInPercent = new PercentMetric("ESXConnectedHostInPercent", esxMetrics, LabelNames.ESXHostUuid);
    public static final Metric ESXDisconnectedHostCount = new CountMetric("ESXDisconnectedHostCount", esxMetrics, LabelNames.ESXHostUuid);
    public static final Metric ESXDisconnectedHostInPercent = new PercentMetric("ESXDisconnectedHostInPercent",
            esxMetrics, LabelNames.ESXHostUuid
    );

    public ESXHostNamespace() {
        super();
    }

    public ESXHostNamespace(DatabaseDriver driver) {
        super(driver);
    }

    @Override
    public List<Metric> getMetrics() {
        List<Metric> allMetrics = new ArrayList<>();
        allMetrics.addAll(esxMetrics);
        allMetrics.addAll(metrics);

        return allMetrics.stream()
                .filter(m -> !disableMetrics.contains(m.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<EventFamily> getEvents() {
        return esxEvents;
    }

    @Override
    public String getResourceType() {
        return ESXHostVO.class.getSimpleName();
    }

    @Override
    public String getIdentityLabelName() {
        return LabelNames.ESXHostUuid.toString();
    }

    @Override
    public String getSubNamespaceName() {
        return NAME;
    }

}
