package org.zstack.zwatch.namespace;

import org.zstack.header.core.StaticInit;
import org.zstack.header.storage.cdp.CdpTaskVO;
import org.zstack.zwatch.datatype.EventFamily;
import org.zstack.zwatch.datatype.metric.CountMetric;
import org.zstack.zwatch.datatype.metric.Metric;
import org.zstack.zwatch.datatype.metric.PercentMetric;
import org.zstack.zwatch.driver.DatabaseDriver;
import org.zstack.zwatch.namespace.event.CdpTaskNamespaceEvent;

import java.util.ArrayList;
import java.util.List;

public class CdpTaskNamespace extends AbstractNamespace {
    public static final String NAME = "CdpTask";

    private static final List<Metric> metrics = new ArrayList<>();
    protected static final List<String> disableMetrics = getDisableMetrics(NAME);
    private static final List<EventFamily> events = new ArrayList<>();

    @StaticInit
    static void staticInit() {
        new CdpTaskNamespaceEvent();
    }

    public CdpTaskNamespace(DatabaseDriver driver) {
        super(driver);
    }

    public CdpTaskNamespace() {
        super();
    }

    public enum EventLabelNames {
        CdpTaskUuid,
        CdpTaskStatus,
        Error
    }

    public static final Metric CdpTaskUsedCapacityInPercent = new PercentMetric("CdpTaskUsedCapacityInPercent", metrics,
            EventLabelNames.CdpTaskUuid, EventLabelNames.CdpTaskStatus
    );
    public static final Metric CdpTaskLatency = new CountMetric("CdpTaskLatency", metrics, EventLabelNames.CdpTaskUuid, EventLabelNames.CdpTaskStatus);

    public static final EventFamily CdpTaskFailed = new EventFamily("CdpTaskFailed", events,
            CdpTaskNamespace.EventLabelNames.Error
    ).setEmergencyLevel(EventFamily.EmergencyLevel.Emergent);
    public static final EventFamily CdpTaskStatusAbnormallyChanged = new EventFamily("CdpTaskStatusAbnormallyChanged",
            events, CdpTaskNamespace.EventLabelNames.Error
    ).setEmergencyLevel(EventFamily.EmergencyLevel.Emergent);


    @Override
    protected String getSubNamespaceName() {
        return NAME;
    }

    @Override
    public List<Metric> getMetrics() {
        return metrics;
    }

    @Override
    public List<EventFamily> getEvents() {
        return events;
    }

    @Override
    public String getResourceType() {
        return CdpTaskVO.class.getSimpleName();
    }

    @Override
    public String getIdentityLabelName() {
        return CdpTaskNamespace.EventLabelNames.CdpTaskUuid.toString();
    }
}