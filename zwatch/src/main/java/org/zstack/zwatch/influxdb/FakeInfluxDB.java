package org.zstack.zwatch.influxdb;

import org.influxdb.BatchOptions;
import org.influxdb.InfluxDB;
import org.influxdb.dto.*;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import static org.zstack.utils.gson.JSONObjectUtil.toJsonString;

/**
 * Created by Wenhao.Zhang on 2020/9/28
 * Only use in Unit Test
 */
class FakeInfluxDB implements InfluxDB {
    
    private static final CLogger logger = Utils.getLogger(FakeInfluxDB.class);
    
    @Override
    public InfluxDB setLogLevel(LogLevel logLevel) {
        return this;
    }
    
    @Override
    public InfluxDB enableGzip() {
        return this;
    }
    
    @Override
    public InfluxDB disableGzip() {
        return this;
    }
    
    @Override
    public boolean isGzipEnabled() {
        return false;
    }

    @Override
    public InfluxDB enableBatch() {
        return this;
    }

    @Override
    public InfluxDB enableBatch(BatchOptions batchOptions) {
        return this;
    }

    @Override
    public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit) {
        return this;
    }
    
    @Override
    public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit, ThreadFactory threadFactory) {
        return this;
    }

    @Override
    public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit, ThreadFactory threadFactory, BiConsumer<Iterable<Point>, Throwable> biConsumer, ConsistencyLevel consistencyLevel) {
        return this;
    }

    @Override
    public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit, ThreadFactory threadFactory, BiConsumer<Iterable<Point>, Throwable> biConsumer) {
        return this;
    }
    
    @Override
    public void disableBatch() {
        
    }
    
    @Override
    public boolean isBatchEnabled() {
        return false;
    }
    
    @Override
    public Pong ping() {
        Pong pong = new Pong();
        pong.setVersion(version());
        return pong;
    }
    
    @Override
    public String version() {
        return "Simulator:" + getClass().getName();
    }
    
    @Override
    public void write(Point point) {
        logger.debug(String.format("The fake influxDB instance received : %s", toJsonString(point)));
    }
    
    @Override
    public void write(String s) {
        logger.debug(String.format("The fake influxDB instance received : %s", s));
    }
    
    @Override
    public void write(List<String> list) {
        logger.debug(String.format("The fake influxDB instance received : %s", toJsonString(list)));
    }
    
    @Override
    public void write(String s, String s1, Point point) {
        logger.debug(String.format("The fake influxDB instance received : s=%s, s1=%s, point=%s",
            s, s1, toJsonString(point)));
    }
    
    @Override
    public void write(int i, Point point) {
        logger.debug(String.format("The fake influxDB instance received : i=%d, point=%s",
            i, toJsonString(point)));
    }
    
    @Override
    public void write(BatchPoints batchPoints) {
        logger.debug(String.format("The fake influxDB instance received : batchPoints=%s",
            toJsonString(batchPoints)));
    }

    @Override
    public void writeWithRetry(BatchPoints batchPoints) {

    }

    @Override
    public void write(String s, String s1, ConsistencyLevel consistencyLevel, String s2) {
        logger.debug(String.format("The fake influxDB instance received : " +
                "s=%s, s1=%s, consistencyLevel=%s, s2=%s",
            s, s1, consistencyLevel == null ? "null" : consistencyLevel.name(), s2));
    }

    @Override
    public void write(String s, String s1, ConsistencyLevel consistencyLevel, TimeUnit timeUnit, String s2) {

    }

    @Override
    public void write(String s, String s1, ConsistencyLevel consistencyLevel, List<String> list) {
        logger.debug(String.format("The fake influxDB instance received : " +
                "s=%s, s1=%s, consistencyLevel=%s, list=%s",
            s, s1, consistencyLevel == null ? "null" : consistencyLevel.name(), list));
    }

    @Override
    public void write(String s, String s1, ConsistencyLevel consistencyLevel, TimeUnit timeUnit, List<String> list) {

    }

    @Override
    public void write(int i, String s) {
        logger.debug(String.format("The fake influxDB instance received : i=%d, s=%s",
            i, s));
    }
    
    @Override
    public void write(int i, List<String> list) {
        logger.debug(String.format("The fake influxDB instance received : i=%d, list=%s",
            i, list));
    }
    
    @Override
    public QueryResult query(Query query) {
        logger.warn(String.format("The fake influxDB instance ignored the query request : query=%s",
            query == null ? null : query.getCommand() + ", in " + query.getDatabase()));
        
        QueryResult r = new QueryResult();
        r.setResults(Collections.emptyList());
        return r;
    }

    @Override
    public void query(Query query, Consumer<QueryResult> consumer, Consumer<Throwable> consumer1) {

    }

    @Override
    public void query(Query query, int i, Consumer<QueryResult> consumer) {
        logger.warn(String.format("The fake influxDB instance ignored the query request : " +
                "i=%d, query=%s", i,
            query == null ? null : query.getCommand() + ", in " + query.getDatabase()));
        
        QueryResult r = new QueryResult();
        r.setResults(Collections.emptyList());
        consumer.accept(r);
    }

    @Override
    public void query(Query query, int i, BiConsumer<Cancellable, QueryResult> biConsumer) {

    }

    @Override
    public void query(Query query, int i, Consumer<QueryResult> consumer, Runnable runnable) {

    }

    @Override
    public void query(Query query, int i, BiConsumer<Cancellable, QueryResult> biConsumer, Runnable runnable) {

    }

    @Override
    public void query(Query query, int i, BiConsumer<Cancellable, QueryResult> biConsumer, Runnable runnable, Consumer<Throwable> consumer) {

    }

    @Override
    public QueryResult query(Query query, TimeUnit timeUnit) {
        logger.warn(String.format("The fake influxDB instance ignored the query request : " +
                "query=%s, timeUnit=%s", query == null ? null : query.getCommand() + ", in " + query.getDatabase(),
            timeUnit));
        
        QueryResult r = new QueryResult();
        r.setResults(Collections.emptyList());
        return r;
    }
    
    @Override
    public void createDatabase(String s) {
        logger.debug(String.format("The fake influxDB instance ignored the create database request : %s", s));
    }
    
    @Override
    public void deleteDatabase(String s) {
        logger.debug(String.format("The fake influxDB instance ignored the delete database request : %s", s));
    }
    
    @Override
    public List<String> describeDatabases() {
        return Collections.emptyList();
    }
    
    @Override
    public boolean databaseExists(String s) {
        return false;
    }
    
    @Override
    public void flush() {
        // do-nothing
    }
    
    @Override
    public void close() {
        // do-nothing
    }
    
    @Override
    public InfluxDB setConsistency(ConsistencyLevel consistencyLevel) {
        return this;
    }
    
    @Override
    public InfluxDB setDatabase(String s) {
        return this;
    }
    
    @Override
    public InfluxDB setRetentionPolicy(String s) {
        return this;
    }

    @Override
    public void createRetentionPolicy(String s, String s1, String s2, String s3, int i, boolean b) {

    }

    @Override
    public void createRetentionPolicy(String s, String s1, String s2, int i, boolean b) {

    }

    @Override
    public void createRetentionPolicy(String s, String s1, String s2, String s3, int i) {

    }

    @Override
    public void dropRetentionPolicy(String s, String s1) {

    }
}
