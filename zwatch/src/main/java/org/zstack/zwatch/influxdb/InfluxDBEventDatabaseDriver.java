package org.zstack.zwatch.influxdb;

import okhttp3.OkHttpClient;
import org.apache.commons.lang.StringUtils;
import org.influxdb.*;
import org.influxdb.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.thread.PeriodicTask;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.Component;
import org.zstack.header.core.ExceptionSafe;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.zwatch.AuditData;
import org.zstack.header.zwatch.AuditDataV1;
import org.zstack.header.zwatch.AuditDataV2;
import org.zstack.identity.AccountManager;
import org.zstack.portal.managementnode.ManagementNodeManager;
import org.zstack.premium.externalservice.influxdb.InfluxDBServiceGlobalProperty;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.zwatch.ZWatchConstants;
import org.zstack.zwatch.datatype.*;
import org.zstack.zwatch.driver.EventDatabaseDriver;
import org.zstack.zwatch.driver.PagedQueryResult;
import org.zstack.zwatch.driver.PagedQueryResultHandler;
import org.zstack.zwatch.ha.InfluxProxy;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.operr;

public class InfluxDBEventDatabaseDriver implements EventDatabaseDriver, Component {
    private static final CLogger logger = Utils.getLogger(InfluxDBEventDatabaseDriver.class);

    private InfluxDB db;

    private org.zstack.premium.externalservice.influxdb.InfluxDB server;
    @Autowired
    private org.zstack.premium.externalservice.influxdb.InfluxDBFactory factory;
    @Autowired
    private ManagementNodeManager mngtNodeMgr;
    @Autowired
    private ThreadFacade thdf;
    @Autowired
    private InfluxProxy influxProxy;
    @Autowired
    private AccountManager acmgr;
    @Autowired
    private PluginRegistry pluginRgty;
    protected List<InfluxdbDriverExtensionPoint> driverExts = new ArrayList<>();
    @Autowired
    private ResourceDestinationMaker destinationMaker;
    @Autowired
    private DatabaseFacade dbf;

    private static final String DB_NAME = "zstack";
    private static final String RETENTION_NAME = "zstack";
    private static final String MESSAGE_RETENTION_NAME = "messages";

    public static final String AUDIT_MEASUREMENT = "audits";

    public static final String EVENT_MEASUREMENT = "events";
    public static final String EVENT_RETENTION_MEASUREMENT = "events_retention";

    public static final String ALARM_MEASUREMENT = "alarms";
    public static final String ALARM_RETENTION_MEASUREMENT = "alarms_retention";

    private static final String PROMETHEUS_DB_NAME = "prometheus";
    private static final String PROMETHEUS_RETENTION_NAME = "prometheus";

    public static final String IGNORE_FIELD_END_SYMBOL = "_1";

    // grouped by event name which may be duplicated across different namespace
    private Map<String, List<EventFamily>> eventFamilies = new HashMap<>();

    private interface EventConsumer extends Consumer<EventData> {
        EventSubscriber getEventSubscriber();
    }

    private Map<String, ConcurrentLinkedDeque<EventConsumer>> eventSubscribers = new ConcurrentHashMap<>();

    public void flush() {
        db.flush();
    }

    public void setDb(InfluxDB db) {
        this.db = db;
    }

    public InfluxDB getDb() {
        return db;
    }

    private void writeToInflux(Point p, String measurement) {
        if (CoreGlobalProperty.UNIT_TEST_ON && !Platform.isSimulatorOn()) {
            try {
                db.write(p);
            } catch (InfluxDBException e) {
                logger.debug(String.format("Fail to write data to InfluxDB, because %s", e.getMessage()));
            }
        } else {
            try {
                proxyWrite(getRetentionName(measurement), p, measurement);
            } catch (InfluxDBException e) {
                logger.debug(String.format("Fail to write data to InfluxDB, because %s", e.getMessage()));
            }
        }
    }

    @Override
    @ExceptionSafe
    public void persist(List<EventData> data) {
        data.forEach(d -> writeToInflux(new InfluxEventDataV2(d).toPoint(), EVENT_MEASUREMENT));
    }

    @Override
    public void audit(List<AuditDataV2> data) {
        long time = System.currentTimeMillis();
        data.forEach(d -> {
            try {
                Point.Builder b = Point.measurement(AUDIT_MEASUREMENT)
                        .time(time, TimeUnit.MILLISECONDS)
                        .tag(d.asTags())
                        .fields(d.asFields());

                Point p = b.build();
                writeToInflux(p, AUDIT_MEASUREMENT);
            } catch (Throwable t) {
                logger.warn(String.format("unable to persist audit data:\n%s", JSONObjectUtil.toJsonString(d)), t);
            }
        });
    }

    @Override
    @ExceptionSafe
    public void alarm(AlarmDataV2 data) {
        Point.Builder b = Point.measurement(ALARM_MEASUREMENT)
                .time(data.getTime(), TimeUnit.MILLISECONDS)
                .tag(data.asTags())
                .fields(data.asFields());
        writeToInflux(b.build(), ALARM_MEASUREMENT);
    }

    private boolean isMoreThanOneEventFamilyWithName(String name) {
        List<EventFamily> efs = eventFamilies.get(name);
        if (efs == null) {
            throw new OperationFailureException(argerr("cannot find EventFamily[name:%s]", name));
        }

        return efs.size() > 1;
    }

    private EventFamily getEventFamilyByNameThenByNamespace(String name, String namespace) {
        List<EventFamily> efs = eventFamilies.get(name);
        if (efs == null) {
            throw new OperationFailureException(argerr("cannot find EventFamily[name:%s, namespace:%s]", name, namespace));
        }

        if (efs.size() == 1) {
            return efs.get(0);
        }

        if (namespace == null) {
            throw new CloudRuntimeException(String.format("namespace cannot be null as there are multiple EventFamily with the name[%s]", name));
        }

        Optional<EventFamily> opt = efs.stream().filter(e->e.getNamespace().equals(namespace)).findFirst();
        if (!opt.isPresent()) {
            throw new OperationFailureException(argerr("cannot find EventFamily[name:%s, namespace:%s]", name, namespace));
        }

        return opt.get();
    }

    @Override
    public void consumeEvents(List<EventData> data) {
        List<EventData> subscribeEvents = new ArrayList<>();

        data.forEach(event -> {
            Queue<EventConsumer> queue = eventSubscribers.get(String.format("%s.%s", event.getNamespace(), event.getName()));
            if (queue == null) {
                return;
            }

            queue.forEach(consumer-> {
                EventSubscriber subscriber = consumer.getEventSubscriber();

                if (event.getAccountUuid() != null) {
                    String accountUuid = acmgr.getOwnerAccountUuidOfResource(subscriber.uuid);
                    if (!AccountConstant.isAdminPermission(accountUuid)) {
                        if (!accountUuid.equals(event.getAccountUuid())) {
                            return;
                        }
                    }
                }

                if (subscriber.labels == null || subscriber.labels.isEmpty()) {
                    subscribeEvents.add(event);
                    consumer.accept(new EventData(event));
                    return;
                }

                boolean isMatch = true;
                Map<String, String> eventLabels = event.asQueryLabels();
                for (Label label : subscriber.labels) {
                    String labelValue = eventLabels.get(label.getKey());
                    if (!label.match(labelValue)) {
                        isMatch = false;
                        break;
                    }
                }

                if (isMatch) {
                    subscribeEvents.add(event);
                    consumer.accept(new EventData(event));
                }
            });
        });

        List<EventData> noSubscribeEvents = new ArrayList<>(data);
        noSubscribeEvents.removeAll(subscribeEvents);
        if (noSubscribeEvents.size() == 0) {
            return;
        }
        noSubscribeEvents = noSubscribeEvents.stream()
                .filter(eventData -> eventData.getDataUuid() != null && destinationMaker.isManagedByUs(eventData.getResourceId()))
                .collect(Collectors.toList());
        // no necessary to write normal events
        if (!InfluxDBGlobalProperty.INFLUXDB_WRITE_NORMAL_EVENTS) {
            noSubscribeEvents = noSubscribeEvents.stream().filter(eventData -> eventData.getEmergencyLevel() != EventFamily.EmergencyLevel.Normal)
                    .collect(Collectors.toList());
        }
        for (EventData eventData : noSubscribeEvents) {
            if (eventData.getEmergencyLevel() == EventFamily.EmergencyLevel.Emergent) {
                eventData.setEmergencyLevel(EventFamily.EmergencyLevel.Important.name());
            }
        }
        //persist(noSubscribeEvents);
    }

    @Override
    public List<String> getAllSubscriberUuids() {
        List<String> uuids = new ArrayList<>();
        for (ConcurrentLinkedDeque<EventConsumer> queue : eventSubscribers.values()) {
            Iterator<EventConsumer> it = queue.iterator();
            while (it.hasNext()) {
                EventConsumer c = it.next();
                uuids.add(c.getEventSubscriber().uuid);
            }
        }

        return uuids;
    }

    class InfluxAlarmQueryObject extends AlarmQueryObject {
        public InfluxAlarmQueryObject(AlarmQueryObject other) {
            super(other);
        }

        private String labelToConditionV1andV2(Label label) {
            return CompatibleOldVersion.getCompatibleLabel(label);
        }

        private String labelToConditionV2(Label label) {
            if (label.getOp() == Label.Operator.Equal) {
                return String.format("\"%s\"='%s'", label.getKey(), label.getValue());
            } else if (label.getOp() == Label.Operator.Regex) {
                // the spaces between =~ are needed
                return String.format("\"%s\" =~ /%s/", label.getKey(), label.getValue());
            } else if (label.getOp() == Label.Operator.NotEqual) {
                return String.format("\"%s\"!='%s'", label.getKey(), label.getValue());
            } else if (label.getOp() == Label.Operator.RegexAgainst) {
                return String.format("\"%s\" !~ /%s/", label.getKey(), label.getValue());
            } else {
                throw new CloudRuntimeException(String.format("unknown operator[%s] of the condition[%s]", label.getOp(), label.toString()));
            }
        }

        private String labelToCondition(Label label) {
            if (label.isCompatible()) {
                return labelToConditionV1andV2(label);
            } else {
                return labelToConditionV2(label);
            }
        }

        String toQueryString() {
            List<String> builder = new ArrayList<>();
            builder.add(String.format("SELECT * FROM %s.%s.alarms", DB_NAME, getRetentionName()));

            boolean needWhere = !getLabels().isEmpty() || getStartTime() != null || getEndTime() != null || getTime() != null || !getWhereConditions().isEmpty();
            if (needWhere) {
                builder.add("WHERE");
                List<String> conditions = new ArrayList<>();

                if (!getLabels().isEmpty()) {
                    getLabels().forEach(l -> {
                        List<Label> labels = CompatibleOldVersion.getAlarmConditionLabels(l);
                        labels.forEach(label -> conditions.add(labelToCondition(label)));
                    });
                }

                if (getStartTime() != null) {
                    conditions.add(String.format("time >= %s", secToInfluxTimePrecisionString(getStartTime())));
                }
                if (getEndTime() != null) {
                    conditions.add(String.format("time <= %s", secToInfluxTimePrecisionString(getEndTime())));
                }

                if (getTime() != null) {
                    conditions.add(String.format("time = %s", secToInfluxTimePrecisionString(getTime())));
                }

                if (!getWhereConditions().isEmpty()) {
                    conditions.addAll(getWhereConditions());
                }

                builder.add(StringUtils.join(conditions, " and "));
            }

            builder.add("ORDER BY time DESC");
            if (getLimit() != -1) {
                builder.add(String.format("LIMIT %s", getLimit()));
            }

            builder.add(String.format("OFFSET %s", getOffset()));

            return StringUtils.join(builder, " ");
        }

        private AlarmData createEmptyAlarmDataWithVersion(QueryResult.Series s, List<Object> values) {
            for (int i = 0; i < values.size(); i ++) {
                String colName = s.getColumns().get(i);
                if (colName.equals("namespace_1")) {
                    if (values.get(i) != null) {
                        return new AlarmDataV1();
                    }
                    break;
                }
            }
            return new AlarmDataV2();
        }

        private List<AlarmData> seriesToAlarmData(QueryResult.Series s) {
            List<AlarmData> data = new ArrayList<>();
            logger.debug("start to analyse series");
            s.getValues().forEach(values -> {
                AlarmData d = createEmptyAlarmDataWithVersion(s, values);

                for (int i=0; i<values.size(); i++) {
                    Object value = values.get(i);
                    String colName = s.getColumns().get(i);

                    if ("time".equals(colName)) {
                        String time = millisecondBitCompletion((String) value);
                        d.setTime(millsFromInfluxDBTimeFormat(time));
                    } else if (colName.endsWith(IGNORE_FIELD_END_SYMBOL)) {
                        // fix ZSTAC-25194
                        // fix ZSTAC-25347
                        CompatibleOldVersion.compatibleOldVersionData(d, colName, value);
                    } else {
                        d.setProperty(colName, AlarmDataV2.convertValue(colName, value));
                    }
                }

                data.add(d);
            });

            return data;
        }

        List<AlarmData> query() {
            String qstr = toQueryString();
            if (logger.isTraceEnabled()) {
                logger.trace(qstr);
            }

            QueryResult result = dbQuery(new Query(qstr, DB_NAME), "alarms");

            if (result.hasError()) {
                throw new OperationFailureException(operr("%s", result.getError()));
            }

            List<AlarmData> data = new ArrayList<>();
            result.getResults().forEach(r -> {
                if (r.hasError()) {
                    throw new OperationFailureException(operr("%s", r.getError()));
                }

                if (r.getSeries() != null) {
                    r.getSeries().forEach(s -> data.addAll(seriesToAlarmData(s)));
                }
            });

            return data;
        }

        void query(PagedQueryResultHandler<PagedQueryResult<AlarmData>> handler) {
            String qstr = toQueryString();
            if (logger.isTraceEnabled()) {
                logger.trace(qstr);
            }

            dbQuery(new Query(qstr, DB_NAME), "alarms", 1000, new Consumer<QueryResult>() {
                @Override
                public void accept(QueryResult result) {
                    PagedQueryResult<AlarmData> alarmDataResult = new PagedQueryResult<>();

                    if (result.hasError()) {
                        if ("DONE".equals(result.getError())) {
                            handler.handle(alarmDataResult);
                            return;
                        }

                        alarmDataResult.setError(operr("%s", result.getError()));
                        handler.handle(alarmDataResult);
                        return;
                    }

                    List<AlarmData> data = new ArrayList<>();

                    result.getResults().forEach(r -> {
                        if (r.hasError()) {
                            throw new OperationFailureException(operr("%s", r.getError()));
                        }

                        if (r.getSeries() != null) {
                            r.getSeries().forEach(s -> data.addAll(seriesToAlarmData(s)));
                        }
                    });
                    alarmDataResult.setData(data);

                    if (!data.isEmpty()) {
                        handler.handle(alarmDataResult);
                    }
                }
            });
        }

        String toQueryCountString() {
            List<String> builder = new ArrayList<>();
            builder.add(String.format("SELECT COUNT(\"%s\") FROM %s.%s.alarms", "alarmName", DB_NAME, getRetentionName()));

            boolean needWhere = !getLabels().isEmpty() || getStartTime() != null || getEndTime() != null;
            if (needWhere) {
                builder.add("WHERE");
                List<String> conditions = new ArrayList<>();

                if (!getLabels().isEmpty()) {
                    getLabels().forEach(l -> {
                        List<Label> labels = CompatibleOldVersion.getAlarmConditionLabels(l);
                        labels.forEach(label -> conditions.add(labelToCondition(label)));
                    });
                }

                if (getStartTime() != null) {
                    conditions.add(String.format("time >= %s", secToInfluxTimePrecisionString(getStartTime())));
                }
                if (getEndTime() != null) {
                    conditions.add(String.format("time <= %s", secToInfluxTimePrecisionString(getEndTime())));
                }

                builder.add(StringUtils.join(conditions, " and "));
            }

            return StringUtils.join(builder, " ");
        }

        Long count() {
            String qstr = toQueryCountString();
            if (logger.isTraceEnabled()) {
                logger.trace(qstr);
            }
            QueryResult result = dbQuery(new Query(qstr, DB_NAME), "alarms");

            if (result.hasError()) {
                throw new OperationFailureException(operr("%s", result.getError()));
            }

            if (result.getResults() == null || result.getResults().isEmpty()) {
                return 0L;
            }

            QueryResult.Result r = result.getResults().get(0);
            if (r.hasError()) {
                throw new OperationFailureException(operr("%s", r.getError()));
            }
            if (r.getSeries() == null || r.getSeries().isEmpty()) {
                return 0L;
            }

            Double total = (Double) r.getSeries().get(0).getValues().get(0).get(1);
            return total.longValue();
        }
    }

    class InfluxAuditQueryObject extends AuditQueryObject {
        public InfluxAuditQueryObject(AuditQueryObject other) {
            super(other);
        }

        private String labelToConditionV1andV2(Label label) {
            if (label.getKey().trim().equalsIgnoreCase("error") && label.getOp() == Label.Operator.Equal && label.getValue().trim().isEmpty()) {
                // hardcode for influxdb SQL
                return "(error::tag='' and success!='failed')";
            }
            return CompatibleOldVersion.getCompatibleLabel(label);
        }

        private String labelToConditionV2(Label label) {
            if (label.getOp() == Label.Operator.Equal) {
                return String.format("\"%s\"='%s'", label.getKey(), label.getValue());
            } else if (label.getOp() == Label.Operator.Regex) {
                // the spaces between =~ are needed
                return String.format("\"%s\" =~ /%s/", label.getKey(), label.getValue());
            } else if (label.getOp() == Label.Operator.NotEqual) {
                return String.format("\"%s\"!='%s'", label.getKey(), label.getValue());
            } else if (label.getOp() == Label.Operator.RegexAgainst) {
                return String.format("\"%s\" !~ /%s/", label.getKey(), label.getValue());
            } else {
                throw new CloudRuntimeException(String.format("unknown operator[%s] of the condition[%s]", label.getOp(), label.toString()));
            }
        }

        private String labelToCondition(Label label) {
            if (label.isCompatible()) {
                return labelToConditionV1andV2(label);
            } else {
                return labelToConditionV2(label);
            }
        }

        /** compatible with old versions before(not include) ZStack V3.8.0
         *  default it is false and do replace,
         *  nor it is true and no replace.
         *  This code could works even ui do nothing
         */
        private void replaceError(Label label) {
            if (InfluxDBGlobalProperty.INFLUXDB_META_VERSION_SINCE.equalsIgnoreCase("v2") && label.getKey().equals(AuditDataV2.FIELD_API_ERROR)) {
                label.setKey(AuditDataV2.TAG_SUCCESS);
                if (label.getValue().trim().equals("")) {
                    label.setValue(ZWatchConstants.AUDIT_SUCCESS);
                } else {
                    label.setValue(ZWatchConstants.AUDIT_FAILED);
                }
            }
        }

        String toQueryString() {
            List<String> builder = new ArrayList<>();
            builder.add(String.format("SELECT * FROM %s.%s.audits", DB_NAME, RETENTION_NAME));

            boolean needWhere = !getLabels().isEmpty() || getStartTime() != null || getEndTime() != null;
            if (needWhere) {
                builder.add("WHERE");
                List<String> conditions = new ArrayList<>();

                if (!getLabels().isEmpty()) {
                    getLabels().forEach(l -> {
                        replaceError(l);
                        List<Label> labels = CompatibleOldVersion.getAuditConditionLabels(l);
                        labels.forEach(label -> conditions.add(labelToCondition(label)));
                    });
                }

                if (getStartTime() != null) {
                    conditions.add(String.format("time >= %s", secToInfluxTimePrecisionString(getStartTime())));
                }
                if (getEndTime() != null) {
                    conditions.add(String.format("time <= %s", secToInfluxTimePrecisionString(getEndTime())));
                }

                builder.add(StringUtils.join(conditions, " and "));
            }

            builder.add("ORDER BY time DESC");
            builder.add(String.format("LIMIT %s", getLimit()));

            return StringUtils.join(builder, " ");
        }

        private AuditData createEmptyAuditDataWithVersion(QueryResult.Series s, List<Object> values) {
            for (int i = 0; i < values.size(); i ++) {
                String colName = s.getColumns().get(i);
                if (colName.equals("apiName_1")) {
                    if (values.get(i) != null) {
                        return new AuditDataV1();
                    }
                    break;
                }
            }
            return new AuditDataV2();
        }

        private List<AuditData> seriesToAuditData(QueryResult.Series s) {
            List<AuditData> data = new ArrayList<>();
            s.getValues().forEach(values -> {
                AuditData d = createEmptyAuditDataWithVersion(s, values);

                for (int i=0; i<values.size(); i++) {
                    Object value = values.get(i);
                    String colName = s.getColumns().get(i);

                    if ("time".equals(colName)) {
                        d.setTime(millsFromInfluxDBTimeFormat((String)value));
                    } else if(colName.endsWith(IGNORE_FIELD_END_SYMBOL)) {
                        // fix ZSTAC-25194
                        // fix ZSTAC-25347
                        CompatibleOldVersion.compatibleOldVersionData(d, colName, value);
                    } else {
                        if ((d instanceof AuditDataV1) && (colName.equalsIgnoreCase(AuditDataV2.TAG_SUCCESS))) {
                            continue;
                        }
                        d.setProperty(colName, AuditDataV2.convertValue(colName, value));
                    }
                }

                data.add(d);
            });

            return data;
        }

        List<AuditData> query() {
            String qstr = toQueryString();
            if (logger.isTraceEnabled()) {
                logger.trace(qstr);
            }
            
            // Can not be null
            QueryResult result = dbQuery(new Query(qstr, DB_NAME), "audits");
            
            if (result.hasError()) {
                throw new OperationFailureException(operr("%s", result.getError()));
            }

            List<AuditData> data = new ArrayList<>();
            result.getResults().forEach(r -> {
                if (r.hasError()) {
                    throw new OperationFailureException(operr("%s", r.getError()));
                }

                if (r.getSeries() != null) {
                    r.getSeries().forEach(s -> data.addAll(seriesToAuditData(s)));
                }
            });

            return data;
        }
    }

    class InfluxEventQueryObject extends EventQueryObject {
        public InfluxEventQueryObject(EventQueryObject other) {
            super(other);
        }

        private String labelToCondition(Label label, EventFamily ef) {
            boolean isEventFamilyField = ef != null && ef.getLabelNames().contains(label.getKey());

            if (!InfluxEventDataV2.ALL_TAG_NAMES.contains(label.getKey()) && !InfluxEventDataV2.ALL_FIELD_NAMES.contains(label.getKey()) && !isEventFamilyField) {
                List<String> names = new ArrayList<>();
                names.addAll(InfluxEventDataV2.ALL_TAG_NAMES);
                names.addAll(InfluxEventDataV2.ALL_FIELD_NAMES);
                names.addAll(ef != null ? ef.getLabelNames() : new ArrayList<>());
                throw new OperationFailureException(argerr("invalid query label[%s]. Allowed label names are %s", label.getKey(), names));
            }

            String key;
            if (isEventFamilyField) {
                key = InfluxEventDataV2.influxFieldName(ef.getNamespace(), label.getKey());
            } else {
                key = label.getKey();
            }

            if (label.isCompatible()) {
                return labelToConditionV1andV2(label);
            } else {
                return labelToConditionV2(label, key);
            }
        }

        private String labelToConditionV1andV2(Label label) {
            return CompatibleOldVersion.getCompatibleLabel(label);
        }

        private String labelToConditionV2(Label label, String key) {
            if (label.getOp() == Label.Operator.Equal) {
                return String.format("\"%s\"='%s'", key, label.getValue());
            } else if (label.getOp() == Label.Operator.Regex) {
                // the spaces between =~ are needed
                return String.format("\"%s\" =~ /%s/", key, label.getValue());
            } else if (label.getOp() == Label.Operator.NotEqual) {
                return String.format("\"%s\"!='%s'", key, label.getValue());
            } else if (label.getOp() == Label.Operator.RegexAgainst) {
                return String.format("\"%s\" !~ /%s/", key, label.getValue());
            } else {
                throw new CloudRuntimeException(String.format("unknown operator[%s] of the condition[%s]", label.getOp(), label.toString()));
            }
        }

        String toQueryString() {
            List<String> builder = new ArrayList<>();
            builder.add("SELECT");
            Optional<Label> opt = getLabels().stream().filter(l -> l.getKey().equals(InfluxEventDataV2.TAG_NAME) && l.getOp() == Label.Operator.Equal).findFirst();
            EventFamily ef = null;
            if (opt.isPresent()) {
                Label name = opt.get();
                if (isMoreThanOneEventFamilyWithName(name.getValue())) {
                    opt = getLabels().stream().filter(l -> l.getKey().equals(InfluxEventDataV2.FIELD_NAMESPACE) && l.getOp() == Label.Operator.Equal).findFirst();
                    if (!opt.isPresent()) {
                        throw new OperationFailureException(argerr("there are multiple EventFamily with the name[%s], you must specify" +
                                " the label[%s]", name.getValue(), InfluxEventDataV2.FIELD_NAMESPACE));
                    }

                    String namespace = opt.get().getValue();
                    ef = getEventFamilyByNameThenByNamespace(name.getValue(), namespace);
                } else {
                    ef = getEventFamilyByNameThenByNamespace(name.getValue(), null);
                }

                List<String> cols = new ArrayList<>();
                cols.add(String.format("\"%s\"", InfluxEventDataV2.TAG_NAME));

                cols.add(String.format("\"%s\"", InfluxEventDataV2.FIELD_RESOURCE_ID));
                cols.add(String.format("\"%s\"", InfluxEventDataV2.FIELD_RESOURCE_NAME));
                cols.add(String.format("\"%s\"", InfluxEventDataV2.FIELD_ERROR));
                cols.add(String.format("\"%s\"", InfluxEventDataV2.FIELD_NAMESPACE));
                cols.add(String.format("\"%s\"", InfluxEventDataV2.TAG_EMERGENCY_LEVEL));
                CompatibleOldVersion.addEventCol(cols);
                EventFamily finalEf = ef;
                ef.getLabelNames().forEach(l -> cols.add(String.format("\"%s\"", InfluxEventDataV2.influxFieldName(finalEf.getNamespace(), l))));
                builder.add(StringUtils.join(cols, ","));
            } else {
                builder.add("*");
            }

            builder.add(String.format("FROM %s.%s.events", DB_NAME, getRetentionName()));

            boolean needWhere = !getLabels().isEmpty() || getStartTime() != null || getEndTime() != null || getTime() != null || !getWhereConditions().isEmpty();
            if (needWhere) {
                builder.add("WHERE");
                List<String> conditions = new ArrayList<>();

                if (!getLabels().isEmpty()) {
                    EventFamily finalEf = ef;
                    getLabels().forEach(l -> {
                        List<Label> labels = CompatibleOldVersion.getEventConditionLabels(l);
                        labels.forEach(label -> conditions.add(labelToCondition(label, finalEf)));
                    });
                }

                if (getStartTime() != null) {
                    conditions.add(String.format("time >= %s", secToInfluxTimePrecisionString(getStartTime())));
                }
                if (getEndTime() != null) {
                    conditions.add(String.format("time <= %s", secToInfluxTimePrecisionString(getEndTime())));
                }
                if (getTime() != null) {
                    conditions.add(String.format("time = %s", secToInfluxTimePrecisionString(getTime())));
                }
                if (!getWhereConditions().isEmpty()) {
                    conditions.addAll(getWhereConditions());
                }

                builder.add(StringUtils.join(conditions, " and "));
            }

            builder.add("ORDER BY time DESC");
            if (getLimit() != -1) {
                builder.add(String.format("LIMIT %s", getLimit()));
            }

            builder.add(String.format("OFFSET %s", getOffset()));

            return StringUtils.join(builder, " ");
        }

        private String getNamespace(QueryResult.Series s, List<Object> values) {
            Object namespace = values.get(s.getColumns().indexOf(InfluxEventDataV2.FIELD_NAMESPACE));
            if (namespace == null) {
                namespace = values.get(s.getColumns().indexOf(InfluxEventDataV2.FIELD_NAMESPACE + IGNORE_FIELD_END_SYMBOL));
            }
            DebugUtils.Assert(namespace != null, "namespace is null!");
            return namespace.toString();
        }

        private String getEventName(QueryResult.Series s, List<Object> values) {
            Object eventName = values.get(s.getColumns().indexOf(InfluxEventDataV2.TAG_NAME));
            if (eventName == null) {
                eventName = values.get(s.getColumns().indexOf(InfluxEventDataV2.TAG_NAME + IGNORE_FIELD_END_SYMBOL));
            }
            DebugUtils.Assert(eventName != null, "eventName is null!");
            return eventName.toString();
        }

        private InfluxEventData createEmptyEventDataWithVersion(QueryResult.Series s, List<Object> values) {
            for (int i = 0; i < values.size(); i ++) {
                String colName = s.getColumns().get(i);
                if (colName.equals("namespace_1")) {
                    if (values.get(i) != null) {
                        return new InfluxEventDataV1();
                    }
                    break;
                }
            }
            return new InfluxEventDataV2();
        }

        private List<EventData> seriesToEventData(QueryResult.Series s) {
            List<EventData> data = new ArrayList<>();

            s.getValues().forEach(values -> {
                EventData d = createEmptyEventDataWithVersion(s, values);

                String namespaceName = getNamespace(s, values);
                String eventName = getEventName(s, values);
                EventFamily ef = getEventFamilyByNameThenByNamespace(eventName, namespaceName);

                for (int i=0; i<values.size(); i++) {
                    String value = (String) values.get(i);
                    String colName = s.getColumns().get(i);

                    if (InfluxEventDataV2.TAG_TIME.equals(colName)) {
                        String time = millisecondBitCompletion(value);
                        d.setTime(millsFromInfluxDBTimeFormat(time));
                    } else if (InfluxEventDataV2.ALL_TAG_NAMES.contains(colName)
                            || InfluxEventDataV2.ALL_FIELD_NAMES.contains(colName)) {
                        if (value != null) {
                            d.setProperty(colName, value);
                        }
                    } else if (colName.endsWith(IGNORE_FIELD_END_SYMBOL)) {
                        // fix ZSTAC-25194
                        // fix ZSTAC-25347
                        CompatibleOldVersion.compatibleOldVersionData(d, colName, value);
                    } else {
                        // the rest are all fields
                        String labelName = InfluxEventDataV2.fromInfluxFieldName(colName);
                        if (colName.equals(InfluxEventDataV2.influxFieldName(namespaceName, labelName))) {
                            if (ef.getLabelNames().contains(labelName)) {
                                // "select *" will return all columns, we only select
                                // those that the event family has
                                d.getLabels().put(labelName, value);
                            }
                        }
                    }
                }

                data.add(d);
            });

            return data;
        }

        List<EventData> query() {
            String qstr = toQueryString();

            if (logger.isTraceEnabled()) {
                logger.trace(qstr);
            }
            QueryResult result = dbQuery(new Query(qstr, DB_NAME), "events");

            if (result.hasError()) {
                throw new OperationFailureException(operr("%s", result.getError()));
            }

            List<EventData> data = new ArrayList<>();
            result.getResults().forEach(r -> {
                if (r.hasError()) {
                    throw new OperationFailureException(operr("%s", r.getError()));
                }

                if (r.getSeries() != null) {
                    r.getSeries().forEach(s -> data.addAll(seriesToEventData(s)));
                }
            });

            return data;
        }

        void query(PagedQueryResultHandler<PagedQueryResult<EventData>> handler) {
            String qstr = toQueryString();

            if (logger.isTraceEnabled()) {
                logger.trace(qstr);
            }
            dbQuery(new Query(qstr, DB_NAME), "events", 200, new Consumer<QueryResult>() {
                @Override
                public void accept(QueryResult result) {
                    PagedQueryResult<EventData> eventDataResult = new PagedQueryResult<>();

                    if (result.hasError()) {
                        if ("DONE".equals(result.getError())) {
                            handler.handle(eventDataResult);
                            return;
                        }
                        eventDataResult.setError(operr("%s", result.getError()));
                        handler.handle(eventDataResult);
                        return;
                    }

                    List<EventData> data = new ArrayList<>();
                    result.getResults().forEach(r -> {
                        if (r.hasError()) {
                            throw new OperationFailureException(operr("%s", r.getError()));
                        }

                        if (r.getSeries() != null) {
                            r.getSeries().forEach(s -> data.addAll(seriesToEventData(s)));
                        }
                    });
                    eventDataResult.setData(data);
                    if (!data.isEmpty()) {
                        handler.handle(eventDataResult);
                    }
                }
            });
        }

        String toQueryCountString() {
            List<String> builder = new ArrayList<>();
            builder.add(String.format("SELECT COUNT(\"%s\") FROM %s.%s.events", EventData.FIELD_NONE, DB_NAME, getRetentionName()));

            boolean needWhere = !getLabels().isEmpty() || getStartTime() != null || getEndTime() != null || getTime() != null || !getWhereConditions().isEmpty();
            if (needWhere) {
                builder.add("WHERE");
                List<String> conditions = new ArrayList<>();

                if (!getLabels().isEmpty()) {
                    getLabels().forEach(l -> {
                        List<Label> labels = CompatibleOldVersion.getEventConditionLabels(l);
                        labels.forEach(label -> conditions.add(labelToCondition(label, null)));
                    });
                }

                if (getStartTime() != null) {
                    conditions.add(String.format("time >= %s", secToInfluxTimePrecisionString(getStartTime())));
                }
                if (getEndTime() != null) {
                    conditions.add(String.format("time <= %s", secToInfluxTimePrecisionString(getEndTime())));
                }

                if (getTime() != null) {
                    conditions.add(String.format("time = %s", secToInfluxTimePrecisionString(getTime())));
                }
                if (!getWhereConditions().isEmpty()) {
                    conditions.addAll(getWhereConditions());
                }

                builder.add(StringUtils.join(conditions, " and "));
            }

            return StringUtils.join(builder, " ");
        }

        Long count() {
            String qstr = toQueryCountString();
            if (logger.isTraceEnabled()) {
                logger.trace(qstr);
            }
            QueryResult result = dbQuery(new Query(qstr, DB_NAME), "events");

            if (result.hasError()) {
                throw new OperationFailureException(operr("%s", result.getError()));
            }

            if (result.getResults() == null || result.getResults().isEmpty()) {
                return 0L;
            }

            QueryResult.Result r = result.getResults().get(0);
            if (r.hasError()) {
                throw new OperationFailureException(operr("%s", r.getError()));
            }
            if (r.getSeries() == null || r.getSeries().isEmpty()) {
                return 0L;
            }

            Double total = (Double) r.getSeries().get(0).getValues().get(0).get(1);
            return total.longValue();
        }
    }

    @Override
    public List<EventData> query(EventQueryObject obj) {
        return new InfluxEventQueryObject(obj).query();
    }

    @Override
    public void query(EventQueryObject obj, PagedQueryResultHandler<PagedQueryResult<EventData>> handler) {
        new InfluxEventQueryObject(obj).query(handler);
    }

    @Override
    public void query(AlarmQueryObject obj, PagedQueryResultHandler<PagedQueryResult<AlarmData>> handler) {
        new InfluxAlarmQueryObject(obj).query(handler);
    }

    @Override
    public Long getQueryCount(EventQueryObject obj) {
        return new InfluxEventQueryObject(obj).count();
    }

    @Override
    public List<AuditData> query(AuditQueryObject obj) {
        return new InfluxAuditQueryObject(obj).query();
    }

    @Override
    public List<AlarmData> query(AlarmQueryObject obj) {
        return new InfluxAlarmQueryObject(obj).query();
    }

    @Override
    public Long getQueryCount(AlarmQueryObject obj) {
        return new InfluxAlarmQueryObject(obj).count();
    }

    @Override
    public void update(EventData data) {
        InfluxEventData influxEventData = (InfluxEventData)data;
        writeToInflux(influxEventData.toUpdatePoint(), EVENT_MEASUREMENT);
    }

    @Override
    public void update(AlarmData data) {
        Point.Builder b = Point.measurement(ALARM_MEASUREMENT)
                .time(data.getTime(), TimeUnit.MILLISECONDS)
                .tag(data.asTags())
                .fields(data.asFields());
        writeToInflux(b.build(), ALARM_MEASUREMENT);
    }

    @Override
    public void subscribeEvent(EventSubscriber subscriber, Consumer<EventData> consumer) {
        String key = String.format("%s.%s", subscriber.namespace, subscriber.eventName);
        ConcurrentLinkedDeque<EventConsumer> consumers = eventSubscribers.computeIfAbsent(key, k->new ConcurrentLinkedDeque<>());
        consumers.add(new EventConsumer() {
            @Override
            public void accept(EventData data) {
                consumer.accept(data);
            }

            @Override
            public EventSubscriber getEventSubscriber() {
                return subscriber;
            }
        });
    }

    @Override
    public void unsubscribeEvent(String subscriberUuid) {
        for (ConcurrentLinkedDeque<EventConsumer> queue : eventSubscribers.values()) {
            Iterator<EventConsumer> it = queue.iterator();
            while (it.hasNext()) {
                EventConsumer c = it.next();
                if (c.getEventSubscriber().uuid.equals(subscriberUuid)) {
                    it.remove();
                    return;
                }
            }
        }
    }

    private void loadExtention() {
        driverExts = pluginRgty.getExtensionList(InfluxdbDriverExtensionPoint.class);
    }

    @Override
    public boolean start() {
        if (!InfluxDBGlobalProperty.INFLUXDB_ENABLE) {
            factory.getInfluxDB().stop();
            return true;
        }

        loadExtention();
        Namespace.namespaces.forEach((k, namespaces) -> {
            namespaces.forEach(ns -> {
                if (ns.getEvents() != null) {
                    ns.getEvents().forEach(ef -> {
                        List<EventFamily> efs = eventFamilies.computeIfAbsent(ef.getName(), x->new ArrayList<>());
                        efs.add(ef);
                    });
                }
            });
        });

        startServer();

        if (CoreGlobalProperty.UNIT_TEST_ON) {
            // for unit tests that don't mack InfluxDB,
            // make a fake instance to prevent nullpointer exception
            db = new InfluxDB() {
                @Override
                public InfluxDB setLogLevel(LogLevel logLevel) {
                    return this;
                }

                @Override
                public InfluxDB enableGzip() {
                    return this;
                }

                @Override
                public InfluxDB disableGzip() {
                    return this;
                }

                @Override
                public boolean isGzipEnabled() {
                    return false;
                }

                @Override
                public InfluxDB enableBatch() {
                    return null;
                }

                @Override
                public InfluxDB enableBatch(BatchOptions batchOptions) {
                    return null;
                }

                @Override
                public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit) {
                    return this;
                }

                @Override
                public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit, ThreadFactory threadFactory) {
                    return this;
                }

                @Override
                public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit, ThreadFactory threadFactory, BiConsumer<Iterable<Point>, Throwable> biConsumer, ConsistencyLevel consistencyLevel) {
                    return null;
                }

                @Override
                public InfluxDB enableBatch(int i, int i1, TimeUnit timeUnit, ThreadFactory threadFactory, BiConsumer<Iterable<Point>, Throwable> biConsumer) {
                    return this;
                }

                @Override
                public void disableBatch() {}

                @Override
                public boolean isBatchEnabled() {
                    return false;
                }

                @Override
                public Pong ping() {
                    Pong pong = new Pong();
                    pong.setVersion(version());
                    return pong;
                }

                @Override
                public String version() {
                    return "Simulator:" + getClass().getName();
                }

                @Override
                public void write(Point point) {}

                @Override
                public void write(String s) {}

                @Override
                public void write(List<String> list) {}

                @Override
                public void write(String s, String s1, Point point) {}

                @Override
                public void write(int i, Point point) {}

                @Override
                public void write(BatchPoints batchPoints) {}

                @Override
                public void writeWithRetry(BatchPoints batchPoints) {}

                @Override
                public void write(String s, String s1, ConsistencyLevel consistencyLevel, String s2) {}

                @Override
                public void write(String s, String s1, ConsistencyLevel consistencyLevel, TimeUnit timeUnit, String s2) {}

                @Override
                public void write(String s, String s1, ConsistencyLevel consistencyLevel, List<String> list) {}

                @Override
                public void write(String s, String s1, ConsistencyLevel consistencyLevel, TimeUnit timeUnit, List<String> list) {}

                @Override
                public void write(int i, String s) {}

                @Override
                public void write(int i, List<String> list) {}

                @Override
                public QueryResult query(Query query) {
                    logger.debug(String.format("The fake influxDB instance ignored the query request : query=%s",
                        query == null ? null : query.getCommand() + ", in " + query.getDatabase()));
                    
                    QueryResult r = new QueryResult();
                    r.setResults(Collections.emptyList());
                    return r;
                }

                @Override
                public void query(Query query, Consumer<QueryResult> consumer, Consumer<Throwable> consumer1) {}

                @Override
                public void query(Query query, int i, Consumer<QueryResult> consumer) {
                    logger.debug(String.format("The fake influxDB instance ignored the query request : " +
                            "i=%d, query=%s", i,
                            query == null ? null : query.getCommand() + ", in " + query.getDatabase()));
    
                    QueryResult r = new QueryResult();
                    r.setResults(Collections.emptyList());
                    consumer.accept(r);
                }

                @Override
                public void query(Query query, int i, BiConsumer<Cancellable, QueryResult> biConsumer) {}

                @Override
                public void query(Query query, int i, Consumer<QueryResult> consumer, Runnable runnable) {}

                @Override
                public void query(Query query, int i, BiConsumer<Cancellable, QueryResult> biConsumer, Runnable runnable) {}

                @Override
                public void query(Query query, int i, BiConsumer<Cancellable, QueryResult> biConsumer, Runnable runnable, Consumer<Throwable> consumer) {}

                @Override
                public QueryResult query(Query query, TimeUnit timeUnit) {
                    logger.debug(String.format("The fake influxDB instance ignored the query request : " +
                            "query=%s, timeUnit=%s", query == null ? null : query.getCommand() + ", in " + query.getDatabase(),
                            timeUnit));
    
                    QueryResult r = new QueryResult();
                    r.setResults(Collections.emptyList());
                    return r;
                }

                @Override
                public void createDatabase(String s) {}

                @Override
                public void deleteDatabase(String s) {}

                @Override
                public List<String> describeDatabases() {
                    return Collections.emptyList();
                }

                @Override
                public boolean databaseExists(String s) {
                    return false;
                }

                @Override
                public void flush() {
                    // do-nothing
                }

                @Override
                public void close() {
                    // do-nothing
                }

                @Override
                public InfluxDB setConsistency(ConsistencyLevel consistencyLevel) {
                    return this;
                }

                @Override
                public InfluxDB setDatabase(String s) {
                    return this;
                }

                @Override
                public InfluxDB setRetentionPolicy(String s) {
                    return this;
                }

                @Override
                public void createRetentionPolicy(String s, String s1, String s2, String s3, int i, boolean b) {}

                @Override
                public void createRetentionPolicy(String s, String s1, String s2, int i, boolean b) {}

                @Override
                public void createRetentionPolicy(String s, String s1, String s2, String s3, int i) {}

                @Override
                public void dropRetentionPolicy(String s, String s1) {}
            };
        }

        thdf.submitPeriodicTask(new PeriodicTask() {
            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.HOURS;
            }

            @Override
            public long getInterval() {
                return 1;
            }

            @Override
            public String getName() {
                return "influx-db-retention";
            }

            @Override
            public void run() {
                doAlarmDataRetention();
                doEventDataRetention();
            }

            private void doAlarmDataRetention() {
                if (InfluxDBGlobalProperty.ALARM_DATA_RETENTION_THRESHOLD <= 0) {
                    return;
                }

                AlarmQueryObject aqo = AlarmQueryObject.New()
                        .endTime(System.currentTimeMillis())
                        .build();

                Long totalAlarmNumber = new InfluxAlarmQueryObject(aqo).count();
                if (totalAlarmNumber < InfluxDBGlobalProperty.ALARM_DATA_RETENTION_THRESHOLD) {
                    logger.debug(String.format("current alarms number is %s, skip retention", totalAlarmNumber));
                } else {
                    Long time = getAlarmRetentionTime(aqo, totalAlarmNumber);
                    if (time == null) {
                        logger.debug(String.format("failed to get time for %s retention", ALARM_MEASUREMENT));
                        return;
                    }

                    executeRetention(ALARM_MEASUREMENT, ALARM_RETENTION_MEASUREMENT, time);
                }
            }

            private void doEventDataRetention() {
                if (InfluxDBGlobalProperty.EVENT_DATA_RETENTION_THRESHOLD <= 0) {
                    return;
                }

                EventQueryObject eqo = EventQueryObject.New()
                        .endTime(System.currentTimeMillis())
                        .build();

                Long totalEventNumber = new InfluxEventQueryObject(eqo).count();
                if (totalEventNumber < InfluxDBGlobalProperty.EVENT_DATA_RETENTION_THRESHOLD) {
                    logger.debug(String.format("current events number is %s, skip retention", totalEventNumber));
                } else {
                    Long time = getEventRetentionTime(eqo, totalEventNumber);
                    if (time == null) {
                        logger.debug(String.format("failed to get time for %s retention", EVENT_MEASUREMENT));
                        return;
                    }

                    executeRetention(EVENT_MEASUREMENT, EVENT_RETENTION_MEASUREMENT, time);
                }
            }

            private Long getAlarmRetentionTime(AlarmQueryObject aqo, Long totalAlarmNumber) {
                long limit = totalAlarmNumber / InfluxDBGlobalProperty.ALARM_DATA_RETENTION_THRESHOLD * InfluxDBGlobalProperty.ALARM_DATA_RETENTION_THRESHOLD;

                QueryResult result = dbQuery(new Query(String.format("SELECT last(alarmName) FROM " +
                                "(SELECT alarmName FROM %s.%s.alarms ORDER BY time ASC LIMIT %s)",
                        DB_NAME, getRetentionName(), limit), DB_NAME), "alarms");

                if (result.hasError()) {
                    throw new OperationFailureException(operr("%s", result.getError()));
                }

                Long time = null;
                List<Object> values = result.getResults().get(0).getSeries().get(0).getValues().get(0);
                for (int i = 0; i < values.size(); i++) {
                    Object value = values.get(i);
                    String colName = result.getResults().get(0).getSeries().get(0).getColumns().get(i);

                    if (!"time".equals(colName)) {
                        continue;
                    }

                    time = aqo.millsFromInfluxDBTimeFormat(aqo.millisecondBitCompletion((String) value));
                }

                return time;
            }

            private Long getEventRetentionTime(EventQueryObject eqo, Long totalEventNumber) {
                long limit = totalEventNumber / InfluxDBGlobalProperty.EVENT_DATA_RETENTION_THRESHOLD * InfluxDBGlobalProperty.EVENT_DATA_RETENTION_THRESHOLD;

                QueryResult result = dbQuery(new Query(String.format("SELECT last(\"%s\") FROM " +
                                "(SELECT %s FROM %s.%s.events ORDER BY time ASC LIMIT %s)",
                        EventData.FIELD_NONE, EventData.FIELD_NONE, DB_NAME, getRetentionName(), limit), DB_NAME), "events");

                if (result.hasError()) {
                    throw new OperationFailureException(operr("%s", result.getError()));
                }

                Long time = null;
                List<Object> values = result.getResults().get(0).getSeries().get(0).getValues().get(0);
                for (int i = 0; i < values.size(); i++) {
                    Object value = values.get(i);
                    String colName = result.getResults().get(0).getSeries().get(0).getColumns().get(i);

                    if (!"time".equals(colName)) {
                        continue;
                    }

                    time = eqo.millsFromInfluxDBTimeFormat(eqo.millisecondBitCompletion((String) value));
                }

                return time;
            }

            private void executeRetention(String measurement, String retentionMeasurement, long time) {
                logger.debug(String.format("move %s retention part to %s", measurement, retentionMeasurement));
                dbQuery(new Query(String.format("SELECT * into %s.%s FROM %s.%s.%s WHERE time <= %s GROUP BY *",
                        DB_NAME, retentionMeasurement, DB_NAME, RETENTION_NAME, measurement, TimeUnit.MILLISECONDS.toNanos(time)), DB_NAME), measurement);
                logger.debug(String.format("start influxDB measurement %s data retention", measurement));

                try {
                    dbQuery(new Query(String.format("DELETE FROM %s WHERE time <= %s",
                            measurement, TimeUnit.MILLISECONDS.toNanos(time)), DB_NAME), measurement);
                } catch (InfluxDBIOException exception) {
                    logger.debug(String.format("delete operation cost long time, exception may occurs when socket timeout" +
                            " in this situation the delete operation still finished. reason: %s", exception.getMessage()));
                }

                logger.debug(String.format("influxDB measurement %s retention finished", measurement));
            }

        });

        return true;
    }

    private void startServer() {
        // ZSTAC-28179 : Server needs to connect when simulator on,
        // so I remove '@BypassWhenUnitTest' annotation
        if (CoreGlobalProperty.UNIT_TEST_ON && !Platform.isSimulatorOn()) {
            // Raise fake InfluxDB :
            // for unit tests that don't mack InfluxDB,
            // make a fake instance to prevent nullpointer exception
            this.db = new FakeInfluxDB();
            return;
        }
        
        server = factory.getInfluxDB();
        server.start();
        try {
            prepareDB(RETENTION_NAME, 7, InfluxDBServiceGlobalProperty.RETENTION_DURATION_IN_DAYS);
            if (needDeployMessageRetention()) {
                prepareDB(MESSAGE_RETENTION_NAME, 1, InfluxDBServiceGlobalProperty.MESSAGE_RETENTION_DURATION_IN_DAYS);
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
            mngtNodeMgr.quit(String.format("failed to prepare influxDB database, %s", e.getMessage()));
        }
    }
    
    private void createDB(String retentionName, int minRetentionDay, int retentionDay) {
        createDB(retentionName, minRetentionDay, retentionDay, false);
    }

    private void createDB(String retentionName, int minRetentionDay, int retentionDay, boolean needRedeploy) {
        InfluxDatabaseCreator creator = new InfluxDatabaseCreator();
        creator.dbName = DB_NAME;
        creator.retentionName = retentionName;

        if (retentionDay < minRetentionDay) {
            throw new CloudRuntimeException(String.format("Check your InfluxDB.retention.duration in properties. InfluxDB retention duration is expected larger than %d, current is %d", minRetentionDay, retentionDay));
        }

        creator.retentionInDays = retentionDay;
        creator.ip = InfluxDBServiceGlobalProperty.IP;
        creator.port = InfluxDBServiceGlobalProperty.PORT;
        creator.username = InfluxDBServiceGlobalProperty.USER;
        creator.password = InfluxDBServiceGlobalProperty.PASSWORD;
        creator.needRedeploy = needRedeploy;
        
        creator.create();
    }

    private void prepareDB(String retentionName, int minRetentionDay, int retentionDay) {
        createDB(retentionName, minRetentionDay, retentionDay);

        OkHttpClient.Builder ob = new OkHttpClient.Builder();
        ob.connectTimeout(InfluxDBGlobalProperty.PROXY_CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(InfluxDBGlobalProperty.PROXY_READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(InfluxDBGlobalProperty.PROXY_WRITE_TIMEOUT, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true);

        if ("".equals(InfluxDBServiceGlobalProperty.USER)) {
            db = Platform.New(()-> InfluxDBFactory.connect(String.format("http://%s:%s", InfluxDBServiceGlobalProperty.IP, InfluxDBServiceGlobalProperty.PORT), ob));
        } else {
            db = Platform.New(()-> InfluxDBFactory.connect(String.format("http://%s:%s", InfluxDBServiceGlobalProperty.IP, InfluxDBServiceGlobalProperty.PORT), InfluxDBServiceGlobalProperty.USER, InfluxDBServiceGlobalProperty.PASSWORD, ob));
        }

        db.setDatabase(DB_NAME);
        db.setRetentionPolicy(retentionName);
        db.enableBatch(InfluxDBGlobalProperty.BATCH_POINTS_NUM, InfluxDBGlobalProperty.BATCH_FLUSH_INTERVAL, TimeUnit.MILLISECONDS);
    }
    
    public void cleanDB() {
        if (!Platform.isSimulatorOn() || CoreGlobalProperty.UNIT_TEST_ON) {
            // use fake influxDB
            return;
        }
        
        String retentionName = RETENTION_NAME;
        createDB(retentionName, 7, InfluxDBServiceGlobalProperty.RETENTION_DURATION_IN_DAYS, true);
        
        if (needDeployMessageRetention()) {
            retentionName = MESSAGE_RETENTION_NAME;
            createDB(retentionName, 1, InfluxDBServiceGlobalProperty.MESSAGE_RETENTION_DURATION_IN_DAYS, false);
        }
        
        db.setDatabase(DB_NAME);
        db.setRetentionPolicy(retentionName);
    }

    @Override
    public boolean stop() {
        if (db != null) {
            db.close();
        }

        if (server != null) {
            server.stop();
        }
        return true;
    }

    private static boolean needDeployMessageRetention() {
        if (InfluxDBServiceGlobalProperty.ENABLE_MESSAGE_RETENTION) {
            return true;
        }
        return false;
    }

    private static String getRetentionName() {
        if (InfluxDBServiceGlobalProperty.ENABLE_MESSAGE_RETENTION) {
            return MESSAGE_RETENTION_NAME;
        } else {
            return RETENTION_NAME;
        }
    }

    private static String getRetentionName(String measureMentName) {
        if (measureMentName.equalsIgnoreCase(AUDIT_MEASUREMENT)) {
            return RETENTION_NAME;
        } else {
            return getRetentionName();
        }
    }

    private QueryResult dbQuery(Query q, String table) {
        driverExts.forEach(ext -> {
            ext.beforeInfluxdbQueryCall(table, "query");
        });
        try {
            return db.query(q);
        } catch (Throwable e) {
            logger.debug(String.format("influxdb query error: %s", q.getCommand()));
            throw e;
        }
    }

    private void dbQuery(Query q, String table, int var2, Consumer<QueryResult> var3) {
        driverExts.forEach(ext -> {
            ext.beforeInfluxdbQueryCall(table, "query");
        });
        try {
            db.query(q, var2, var3);
        } catch (Throwable e) {
            logger.debug(String.format("influxdb query error: %s", q.getCommand()));
            throw e;
        }
    }

    private void proxyWrite(String retentionPolicy, Point point, String table) {
        driverExts.forEach(ext -> {
            ext.beforeInfluxdbQueryCall(table, "update");
        });
        influxProxy.write(DB_NAME, retentionPolicy, point);
    }

    @Override
    public void update(EventQueryObject obj) {

    }

    @Override
    public void update(AlarmQueryObject obj) {

    }
}
