package org.zstack.zwatch.influxdb;

import org.zstack.core.GlobalProperty;
import org.zstack.core.GlobalPropertyDefinition;

@GlobalPropertyDefinition
public class InfluxDBGlobalProperty {
    @GlobalProperty(name = "InfluxDB.query.alarmData.count.maxThreadNum", defaultValue = "1")
    public static int QUERY_ALARM_DATA_COUNT_MAX_THREAD_NUM;

    @GlobalProperty(name = "InfluxDB.query.eventData.count.maxThreadNum", defaultValue = "1")
    public static int QUERY_EVENT_DATA_COUNT_MAX_THREAD_NUM;

    @GlobalProperty(name = "InfluxDB.alarmData.retention.threshold", defaultValue = "-1")
    public static int ALARM_DATA_RETENTION_THRESHOLD;

    @GlobalProperty(name = "InfluxDB.eventData.retention.threshold", defaultValue = "-1")
    public static int EVENT_DATA_RETENTION_THRESHOLD;

    @GlobalProperty(name = "InfluxDB.batch.pointsToFlush", defaultValue = "2000")
    public static int BATCH_POINTS_NUM;
    @GlobalProperty(name = "InfluxDB.batch.flushInterval", defaultValue = "100")
    public static int BATCH_FLUSH_INTERVAL;
    @GlobalProperty(name = "InfluxDB.writeProxy.on", defaultValue = "false")
    public static boolean USE_PROXY;
    @GlobalProperty(name = "InfluxDB.writeProxy.connectTimeout", defaultValue = "20")
    public static int PROXY_CONNECT_TIMEOUT;
    @GlobalProperty(name = "InfluxDB.writeProxy.readTimeout", defaultValue = "60")
    public static int PROXY_READ_TIMEOUT;
    @GlobalProperty(name = "InfluxDB.writeProxy.writeTimeout", defaultValue = "10")
    public static int PROXY_WRITE_TIMEOUT;
    // indicate the influxdb metadata version which could be existed in
    @GlobalProperty(name = "InfluxDB.metadata.version", defaultValue = "v1")
    public static String INFLUXDB_META_VERSION_SINCE;
    @GlobalProperty(name = "InfluxDB.tag.wo.quotes", defaultValue = "false")
    public static boolean INFLUXDB_TAG_WO_QUOTES;
    @GlobalProperty(name = "InfluxDB.write.normalevents", defaultValue = "false")
    public static boolean INFLUXDB_WRITE_NORMAL_EVENTS;
    @GlobalProperty(name = "InfluxDB.enable", defaultValue = "false")
    public static boolean INFLUXDB_ENABLE;
}
