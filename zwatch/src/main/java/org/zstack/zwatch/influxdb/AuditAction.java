package org.zstack.zwatch.influxdb;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.identity.AccountManager;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.zwatch.ZWatchConstants;
import org.zstack.zwatch.alarm.*;
import org.zstack.zwatch.datatype.*;
import org.zstack.zwatch.utils.ParserUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class AuditAction implements AlarmAction {
    private static final CLogger logger = Utils.getLogger(AuditAction.class);

    @Autowired
    private InfluxDBEventDatabaseDriver driver;
    @Autowired
    private AccountManager acntMgr;
    @Autowired
    private DatabaseFacade dbf;

    @Override
    public void takeAction(TakeAlarmActionParam param) {
        AlarmDataV2 data = new AlarmDataV2();
        AlarmInventory inv = param.alarm;
        data.setDataUuid(param.alarmDataUuid);
        data.setTime(System.currentTimeMillis());
        data.setAlarmName(inv.getName());
        data.setAlarmUuid(inv.getUuid());
        data.setAlarmStatus(param.currentStatus.toString());
        data.setMetricName(inv.getMetricName());
        data.setNamespace(inv.getNamespace());
        data.setPeriod(inv.getPeriod());
        data.setThreshold(inv.getThreshold());
        data.setMetricValue(param.currentValue);
        data.setLabels(StringUtils.join(inv.getLabels().stream().map(l -> String.format("%s %s %s",
                l.getKey(), Label.Operator.valueOf(l.getOperator()).name(), l.getValue())).collect(Collectors.toList()), ", "));
        data.setComparisonOperator(inv.getComparisonOperator().toString());

        String accountUuid = acntMgr.getOwnerAccountUuidOfResource(inv.getUuid());
        data.setAccountUuid(accountUuid);

        data.setReadStatus(ZWatchConstants.DATA_READ_STATUS_UNREAD);
        data.setEmergencyLevel(inv.getEmergencyLevel());

        Namespace ns = Namespace.getMetricNameSpace(inv.getNamespace(), inv.getMetricName());
        if (ns != null) {
            if (ns.getResourceType() != null) {
                data.setResourceType(ns.getResourceType());
            }

            String identityLabelOfNamespace = ns.getIdentityLabelName();
            if (identityLabelOfNamespace != null) {
                Optional<AlarmLabelInventory> labelOfAlarm = inv.getLabels().stream().filter(l -> l.getKey().equals(identityLabelOfNamespace)).findFirst();
                labelOfAlarm.ifPresent(alarmLabelInventory -> data.setResourceUuid(alarmLabelInventory.getValue()));

                if (param.identifyLabel != null) {
                    Map<String, String> identifyLabel = ParserUtils.parseIdentifyLabel(param.identifyLabel);
                    String resourceUuid = identifyLabel.get(identityLabelOfNamespace);
                    if (resourceUuid != null) {
                        data.setResourceUuid(resourceUuid);
                    }

                    identifyLabel.remove(identityLabelOfNamespace);
                    data.setContext(JSONObjectUtil.toJsonString(identifyLabel));
                }
            } else {
                if (param.identifyLabel != null) {
                    Map<String, String> identifyLabel = ParserUtils.parseIdentifyLabel(param.identifyLabel);
                    data.setContext(JSONObjectUtil.toJsonString(identifyLabel));
                }
            }
        }

        driver.alarm(data);
    }

    @Override
    public void takeAction(TakeEventSubscriptionActionParam param) {
        EventSubscriptionVO subscriptionVO = dbf.findByUuid(param.subscriptionUuid, EventSubscriptionVO.class);

        EventData eventData = param.event;
        eventData.setDataUuid(param.dataUuid);
        eventData.setAccountUuid(param.subscriptionAccountUuid);
        eventData.setSubscriptionUuid(subscriptionVO.getUuid());
        eventData.setReadStatus(ZWatchConstants.DATA_READ_STATUS_UNREAD);
        if (subscriptionVO.getEmergencyLevel() != null) {
            eventData.setEmergencyLevel(subscriptionVO.getEmergencyLevel().name());
        }
        driver.persist(Arrays.asList(eventData));
    }

    @Override
    public void takeActionForThirdpartyAlert(TakeEventSubscriptionActionParam param) {
        return;
    }
}
