package org.zstack.zwatch.influxdb;

import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.zstack.core.Platform;
import org.zstack.core.defer.Defer;
import org.zstack.core.defer.Deferred;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.gson.JSONObjectUtil;
import static org.zstack.core.Platform.operr;

public class InfluxDatabaseCreator {
    public String ip;
    public int port = -1;
    public String username;
    public String password;
    public String dbName;
    public String retentionName;
    public int retentionInDays = -1;
    public boolean needRedeploy = false;

    @Deferred
    public void create() {
        DebugUtils.Assert(ip != null, "ip cannot be null");
        DebugUtils.Assert( port != -1, "port must be set");
        DebugUtils.Assert(dbName != null, "DBName cannot be null");
        DebugUtils.Assert(retentionName != null, "retentionName cannot be null");
        DebugUtils.Assert(retentionInDays != -1, "retentionInDays must be set");
        if (username != null && username.isEmpty()) {
            username = null;
        }

        if (username != null && password == null) {
            throw new CloudRuntimeException("influxDB username is set but password not set");
        }

        InfluxDB pdb;
        if (username == null) {
            pdb = Platform.New(()-> org.influxdb.InfluxDBFactory.connect(String.format("http://%s:%s", ip, port)));
        } else {
            pdb = Platform.New(()-> InfluxDBFactory.connect(String.format("http://%s:%s", ip, port), username, password));
        }

        Defer.guard(pdb::close);
        
        if (needRedeploy) {
            pdb.deleteDatabase(dbName);
            pdb.createDatabase(dbName);
        } else if (!pdb.databaseExists(dbName)) {
            pdb.createDatabase(dbName);
        }

        pdb.setDatabase(dbName);

        Query query = new Query("SHOW RETENTION POLICIES", dbName);
        QueryResult ret = pdb.query(query);
        if (ret.hasError()) {
            throw new OperationFailureException(operr("unable to query influxdb, %s", ret.getError()));
        }

        boolean hasRetention = ret.getResults().stream().anyMatch(r -> {
            if (r.hasError()) {
                return false;
            }

            return r.getSeries().stream().anyMatch(s -> {
                int nameIdx = s.getColumns().indexOf("name");
                if (nameIdx == -1) {
                    throw new OperationFailureException(operr("invalid influxdb response: %s, no name found in columns", JSONObjectUtil.toJsonString(ret)));
                }

                return s.getValues().stream().anyMatch(vs -> vs.get(nameIdx).equals(retentionName));
            });
        });

        if (hasRetention) {
            String retention = String.format("ALTER RETENTION POLICY \"%s\" ON \"%s\" DURATION %sd REPLICATION 2", retentionName, dbName, retentionInDays);
            query = new Query(retention, dbName);
            QueryResult res = pdb.query(query);
            if (res.hasError()) {
                throw new OperationFailureException(operr("failed to alter influxdb retention '%s', %s", retention, res.getError()));
            }
        } else {
            String retention = String.format("CREATE RETENTION POLICY \"%s\" ON \"%s\" DURATION %sd REPLICATION 2", retentionName, dbName, retentionInDays);
            query = new Query(retention, dbName);
            QueryResult res = pdb.query(query);
            if (res.hasError()) {
                throw new OperationFailureException(operr("failed to create influxdb retention '%s', %s", retention, res.getError()));
            }
        }

        String defaultUserName = "zstack";
        String showUser = "SHOW USERS";
        query = new Query(showUser, dbName);
        QueryResult res = pdb.query(query);

        boolean noDefaultUser;
        if (res.getResults() != null && !res.getResults().isEmpty()) {
            noDefaultUser = res.getResults().stream().noneMatch(r -> r.getSeries().stream().anyMatch(s -> {
                int nameIdx = s.getColumns().indexOf("user");
                if (nameIdx == -1) {
                    throw new OperationFailureException(operr("invalid influxdb response: %s, no name found in columns", JSONObjectUtil.toJsonString(ret)));
                }

                if (s.getValues() == null) {
                    return false;
                }

                return s.getValues().stream().noneMatch(vs -> vs != null && vs.get(nameIdx) != null && vs.get(nameIdx).equals(defaultUserName));
            }));
        } else {
            noDefaultUser = true;
        }

        if (noDefaultUser) {
            String createDefaultUser = "CREATE USER zstack WITH PASSWORD 'zstack.influxdb.password' WITH ALL PRIVILEGES";
            query = new Query(createDefaultUser, dbName);
            res = pdb.query(query);
            if (res.hasError()) {
                throw new OperationFailureException(operr("failed to create influxdb default user '%s', %s", defaultUserName, res.getError()));
            }
        }
    }
}
