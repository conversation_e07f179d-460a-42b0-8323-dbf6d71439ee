package org.zstack.zwatch.influxdb;

import org.influxdb.dto.Point;
import org.zstack.header.rest.NoSDK;
import org.zstack.zwatch.ZWatchConstants;
import org.zstack.zwatch.datatype.EventData;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class InfluxEventDataV2 extends InfluxEventData {
    public static final String TAG_NAME = "name";
    public static final String TAG_ACCOUNT_UUID = ZWatchConstants.DATA_ACCOUNT_UUID;
    public static final String TAG_TIME = "time";  // not real tag
    public static final String TAG_SUBSCRIPTION_UUID = "subscriptionUuid";
    public static final String TAG_EMERGENCY_LEVEL = "emergencyLevel";

    public static final String FIELD_RESOURCE_ID = "resourceId";
    public static final String FIELD_DATA_UUID = "dataUuid";
    public static final String FIELD_ERROR = "error";
    public static final String FIELD_RESOURCE_NAME = "resourceName";
    public static final String FIELD_NAMESPACE = "namespace";
    public static final String FIELD_READ_STATUS = ZWatchConstants.DATA_READ_STATUS;


    public static final Set<String> ALL_TAG_NAMES = new HashSet<>();
    public static final Set<String> ALL_FIELD_NAMES = new HashSet<>();

    static {
        ALL_TAG_NAMES.add(TAG_NAME);
        ALL_TAG_NAMES.add(TAG_ACCOUNT_UUID);
        ALL_TAG_NAMES.add(TAG_SUBSCRIPTION_UUID);
        ALL_TAG_NAMES.add(TAG_EMERGENCY_LEVEL);

        ALL_FIELD_NAMES.add(FIELD_RESOURCE_ID);
        ALL_FIELD_NAMES.add(FIELD_DATA_UUID);
        ALL_FIELD_NAMES.add(FIELD_ERROR);
        ALL_FIELD_NAMES.add(FIELD_RESOURCE_NAME);
        ALL_FIELD_NAMES.add(FIELD_NAMESPACE);
        ALL_FIELD_NAMES.add(FIELD_READ_STATUS);
        ALL_FIELD_NAMES.add(FIELD_NONE);
    }

    public InfluxEventDataV2() {
        super();
    }

    public InfluxEventDataV2(EventData other) {
        super(other);
    }

    private Map<String, Object> asFields1() {
        Map<String, Object> fields = asFields();
        if (error != null) {
            fields.put(FIELD_ERROR, error);
        }
        return fields;
    }

    public Map<String, Object> asFields() {
        Map<String, Object> ret = new HashMap<>();
        ret.put(FIELD_NONE, "none");
        ret.put(FIELD_NAMESPACE, namespace);
        ret.put(FIELD_DATA_UUID, dataUuid);
        ret.put(FIELD_READ_STATUS, readStatus);
        ret.put(FIELD_RESOURCE_ID, resourceId);

        if (resourceName != null) {
            ret.put(FIELD_RESOURCE_NAME, escapeString(resourceName));
        }

        if (error != null) {
            ret.put(FIELD_ERROR, escapeString(error));
        }

        labels.forEach((k, v) -> {
                ret.put(influxFieldName(namespace, k), v);
        });

        return ret;
    }

    public Map<String, String> asTags() {
        Map<String, String> ls = new HashMap<>();

        ls.put(TAG_NAME, name);

        if (accountUuid != null) {
            ls.put(TAG_ACCOUNT_UUID, accountUuid);
        }

        if (subscriptionUuid != null) {
            ls.put(TAG_SUBSCRIPTION_UUID, subscriptionUuid);
        }

        if (emergencyLevel != null) {
            ls.put(TAG_EMERGENCY_LEVEL, emergencyLevel.name());
        }

        return ls;
    }

    @Override
    public Point toUpdatePoint() {
        Point.Builder b = Point.measurement(InfluxDBEventDatabaseDriver.EVENT_MEASUREMENT)
                .time(time, TimeUnit.MILLISECONDS)
                .tag(asTags())
                .fields(asFields1());
        return b.build();
    }

    private static Set<String> tagsFromV1 = new HashSet<>();
    static {
        tagsFromV1.add(FIELD_RESOURCE_NAME);
        tagsFromV1.add(FIELD_NAMESPACE);
        tagsFromV1.add(FIELD_DATA_UUID);
        tagsFromV1.add(FIELD_RESOURCE_ID);
    }
    public static Set<String> tagToFieldFromV1() {
        return tagsFromV1;
    }
}
