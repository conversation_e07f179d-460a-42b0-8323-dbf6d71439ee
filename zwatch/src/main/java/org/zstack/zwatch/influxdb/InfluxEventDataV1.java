package org.zstack.zwatch.influxdb;

import org.influxdb.dto.Point;
import org.zstack.header.rest.NoSDK;
import org.zstack.zwatch.ZWatchConstants;
import org.zstack.zwatch.datatype.EventData;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class InfluxEventDataV1 extends InfluxEventData {
    public static final String TAG_NAME = "name";
    public static final String TAG_RESOURCE_ID = "resourceId";
    public static final String TAG_RESOURCE_NAME = "resourceName";
    public static final String TAG_ERROR = "error";
    public static final String TAG_NAMESPACE = "namespace";
    public static final String TAG_EMERGENCY_LEVEL = "emergencyLevel";
    public static final String TAG_TIME = "time";
    public static final String TAG_DATA_UUID = "dataUuid";
    public static final String TAG_ACCOUNT_UUID = ZWatchConstants.DATA_ACCOUNT_UUID;
    
    public static final String FIELD_READ_STATUS = ZWatchConstants.DATA_READ_STATUS;

    public static final Set<String> ALL_TAG_NAMES = new HashSet<>();

    public static final Set<String> ALL_FIELD_NAMES = new HashSet<>();

    static {
        ALL_TAG_NAMES.add(TAG_NAME);
        ALL_TAG_NAMES.add(TAG_RESOURCE_ID);
        ALL_TAG_NAMES.add(TAG_RESOURCE_NAME);
        ALL_TAG_NAMES.add(TAG_ERROR);
        ALL_TAG_NAMES.add(TAG_NAMESPACE);
        ALL_TAG_NAMES.add(TAG_EMERGENCY_LEVEL);
        ALL_TAG_NAMES.add(TAG_DATA_UUID);
        ALL_TAG_NAMES.add(TAG_ACCOUNT_UUID);

        ALL_FIELD_NAMES.add(FIELD_READ_STATUS);
        ALL_FIELD_NAMES.add(FIELD_NONE);
    }

    public InfluxEventDataV1() {
        super();
    }

    public InfluxEventDataV1(EventData other) {
        super(other);
    }

    @Override
    public Map<String, Object> asFields() {
        Map<String, Object> ret = new HashMap<>();
        ret.put(FIELD_NONE, "none");

        labels.forEach((k, v) -> {
            if (ALL_FIELD_NAMES.contains(k)) {
                ret.put(k, v);
            }else {
                ret.put(influxFieldName(namespace, k), v);
            }
        });
        ret.put(FIELD_READ_STATUS, readStatus);

        return ret;
    }

    @Override
    public Map<String, String> asTags() {
        Map<String, String> ls = new HashMap<>();

        ls.put(TAG_NAME, name);
        ls.put(TAG_NAMESPACE, namespace);
        ls.put(TAG_EMERGENCY_LEVEL, emergencyLevel.toString());

        if (resourceId != null) {
            ls.put(TAG_RESOURCE_ID, resourceId);
        }

        if (resourceName != null) {
            ls.put(TAG_RESOURCE_NAME, escapeString(resourceName));
        }

        if (error != null) {
            ls.put(TAG_ERROR, escapeString(error));
        }

        if (dataUuid != null) {
            ls.put(TAG_DATA_UUID, dataUuid);
        }

        if (accountUuid != null) {
            ls.put(TAG_ACCOUNT_UUID, accountUuid);
        }

        return ls;
    }

    private Map<String, String> asTags1() {
        Map<String, String> tags = asTags();
        if (error != null) {
            tags.put(TAG_ERROR, error);
        }
        return tags;
    }

    @Override
    public Point toUpdatePoint() {
        Point.Builder b = Point.measurement(InfluxDBEventDatabaseDriver.EVENT_MEASUREMENT)
                .time(time, TimeUnit.MILLISECONDS)
                .tag(asTags1())
                .fields(asFields());
        return b.build();
    }
}
