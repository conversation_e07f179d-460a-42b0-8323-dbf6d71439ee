package org.zstack.zwatch.influxdb;

import org.apache.commons.lang.StringEscapeUtils;
import org.influxdb.dto.Point;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.zwatch.datatype.EventData;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by mingjian.deng on 2020/1/9.
 */
public abstract class InfluxEventData extends EventData {
    public static final String SPLITTER = ":::";

    public abstract Map<String, String> asTags();
    public abstract Map<String, Object> asFields();

    public InfluxEventData() {
        super();
    }

    public InfluxEventData(EventData other) {
        super(other);
    }

    protected String escapeString(String value) {
        // escape string and add double quote around it
        // avoid 'InfluxDBException' caused by unable to parse tag value
        // refer to https://github.com/influxdata/docs.influxdata.com/issues/1295
        char doubleQuotation = '"';
        if (value.startsWith(Character.toString(doubleQuotation)) && value.endsWith(Character.toString(doubleQuotation)) && value.length() >= 2) {
            String resourceNameContent = value.substring(1, value.length() - 1);
            return doubleQuotation + StringEscapeUtils.escapeJava(resourceNameContent) + doubleQuotation;
        } else {
            return doubleQuotation + StringEscapeUtils.escapeJava(value) + doubleQuotation;
        }
    }

    protected String unescapeString(String value) {
        char doubleQuotation = '"';
        if (value.startsWith(Character.toString(doubleQuotation)) && value.endsWith(Character.toString(doubleQuotation)) && value.length() >= 2) {
            return StringEscapeUtils.unescapeJava(StringEscapeUtils.unescapeJava(value));
        } else {
            return StringEscapeUtils.unescapeJava(value);
        }
    }

    public static String fromInfluxFieldName(String fieldName) {
        String[] names = fieldName.split(SPLITTER, 2);
        if (names.length != 2) {
            throw new CloudRuntimeException(String.format("unknown field name[%s]", fieldName));
        }
        return names[1];
    }

    public static String influxFieldName(String namespace, String tagName) {
        return String.format("%s%s%s", namespace.replaceAll("/", "::"), SPLITTER, tagName);
    }

    public Point toPoint() {
        Point.Builder b = Point.measurement(InfluxDBEventDatabaseDriver.EVENT_MEASUREMENT)
                .time(time, TimeUnit.MILLISECONDS)
                .tag(asTags())
                .fields(asFields());
        return b.build();
    }

    @Override
    public void setProperty(String propertyName, Object newValue) {
        if (propertyName.startsWith("emergencyLevel")) {
            if (newValue != null) {
                setEmergencyLevel(String.valueOf(newValue));
            }
        } else if (propertyName.equals(FIELD_NONE)) {
            // donothing
        } else if (propertyName.equals("resourceName")) {
            if (newValue != null) {
                setResourceName(unescapeString(String.valueOf(newValue)));
            }
        } else if (propertyName.equals("error")) {
            if (newValue != null) {
                setError(unescapeString(String.valueOf(newValue)));
            }
        } else {
            super.setProperty(propertyName, newValue);
        }
    }

    public abstract Point toUpdatePoint();
}
