package org.zstack.zwatch.prometheus;

import org.zstack.header.core.StaticInit;
import org.zstack.zwatch.datatype.Namespace;
import org.zstack.zwatch.namespace.VRouterNamespace;

public class VRouterPrometheusNamespace extends VmPrometheusNamespace{
    @StaticInit
    static void staticInit() {
        PrometheusNamespace.namespacesClasses.put(VRouterNamespace.class, VRouterPrometheusNamespace.class);
    }

    public VRouterPrometheusNamespace(Namespace namespace) {
        super(namespace);
    }
}
