package org.zstack.zwatch.prometheus;

import com.vmware.vim25.*;
import io.prometheus.client.Collector;
import io.prometheus.client.GaugeMetricFamily;
import org.apache.commons.lang.StringUtils;
import org.zstack.header.core.StaticInit;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.vmware.*;
import org.zstack.zwatch.datatype.metric.Metric;
import org.zstack.zwatch.datatype.Namespace;
import org.zstack.zwatch.namespace.VCenterNamespace;

import java.util.*;

import static java.util.Arrays.asList;

public class VCenterPrometheusNamespace extends AbstractPrometheusNamespace {
    private static final CLogger logger = Utils.getLogger(VCenterPrometheusNamespace.class);

    public static class VCenterCollector implements MetricCollector {

        private String seriesName(String metricName) {
            return PrometheusNamespace.makeSeriesName(Namespace.zstackNamespaceName(VCenterNamespace.NAME), metricName);
        }

        private GaugeMetricFamily createMetric(Metric m) {
            return new GaugeMetricFamily(seriesName(m.getName()), String.format("help for %s", m.getName()), m.getLabelNames());
        }

        @Override
        public List<Collector.MetricFamilySamples> collect() {
            List<Collector.MetricFamilySamples> samples = new ArrayList<>();
            samples.addAll(vcenterVmMetrics());
            return samples;
        }

        @Override
        public boolean skipManagementNodeCheck() {
            return true;
        }

        @Override
        public String getCollectorName() {
            return VCenterCollector.class.getName();
        }

        private double average (long[] value){
            //avoid negative value in case of vm restart
            OptionalDouble optional = Arrays.stream(value).filter(v -> v >= 0).average();
            return optional.isPresent() ? optional.getAsDouble() : 0.0;
        }

        private String diskLetterChange(String diskLetter) {
            if (StringUtils.isEmpty(diskLetter)) {
                return "None";
            } else {
                return diskLetter;
            }
        }

        private List<Collector.MetricFamilySamples> vcenterVmMetrics() {
            List<Collector.MetricFamilySamples> samples = new ArrayList<>();

            GaugeMetricFamily VmCPUUsage = createMetric(VCenterNamespace.VmCPUUsage);
            samples.add(VmCPUUsage);
            GaugeMetricFamily VmCPUUsageMHZ = createMetric(VCenterNamespace.VmCPUUsageMHZ);
            samples.add(VmCPUUsageMHZ);
            GaugeMetricFamily VmCPUIdle = createMetric(VCenterNamespace.VmCPUIdle);
            samples.add(VmCPUIdle);
            GaugeMetricFamily VmCPUUsed = createMetric(VCenterNamespace.VmCPUUsed);
            samples.add(VmCPUUsed);

            GaugeMetricFamily VmMemoryUsage = createMetric(VCenterNamespace.VmMemoryUsage);
            samples.add(VmMemoryUsage);
            GaugeMetricFamily VmMemoryGranted = createMetric(VCenterNamespace.VmMemoryGranted);
            samples.add(VmMemoryGranted);
            GaugeMetricFamily VmMemoryActive = createMetric(VCenterNamespace.VmMemoryActive);
            samples.add(VmMemoryActive);
            GaugeMetricFamily VmMemoryVmMemCtl = createMetric(VCenterNamespace.VmMemoryVmMemCtl);
            samples.add(VmMemoryVmMemCtl);
            GaugeMetricFamily VmMemoryConsumed = createMetric(VCenterNamespace.VmMemoryConsumed);
            samples.add(VmMemoryConsumed);
            GaugeMetricFamily VmMemoryEntitlement = createMetric(VCenterNamespace.VmMemoryEntitlement);
            samples.add(VmMemoryEntitlement);

            GaugeMetricFamily VmDiskUsage = createMetric(VCenterNamespace.VmDiskUsage);
            samples.add(VmDiskUsage);
            GaugeMetricFamily VmDiskRead = createMetric(VCenterNamespace.VmDiskRead);
            samples.add(VmDiskRead);
            GaugeMetricFamily VmDiskWrite = createMetric(VCenterNamespace.VmDiskWrite);
            samples.add(VmDiskWrite);
            GaugeMetricFamily VmDiskMaxTotalLatency = createMetric(VCenterNamespace.VmDiskMaxTotalLatency);
            samples.add(VmDiskMaxTotalLatency);

            GaugeMetricFamily VmVirtualDiskNumberReadAveraged = createMetric(VCenterNamespace.VmVirtualDiskNumberReadAveraged);
            samples.add(VmVirtualDiskNumberReadAveraged);
            GaugeMetricFamily VmVirtualDiskNumberWriteAveraged = createMetric(VCenterNamespace.VmVirtualDiskNumberWriteAveraged);
            samples.add(VmVirtualDiskNumberWriteAveraged);
            GaugeMetricFamily VmVirtualDiskRead = createMetric(VCenterNamespace.VmVirtualDiskRead);
            samples.add(VmVirtualDiskRead);
            GaugeMetricFamily VmVirtualDiskWrite = createMetric(VCenterNamespace.VmVirtualDiskWrite);
            samples.add(VmVirtualDiskWrite);
            GaugeMetricFamily VmVirtualDiskTotalReadLatency = createMetric(VCenterNamespace.VmVirtualDiskTotalReadLatency);
            samples.add(VmVirtualDiskTotalReadLatency);
            GaugeMetricFamily VmVirtualDiskTotalWriteLatency = createMetric(VCenterNamespace.VmVirtualDiskTotalWriteLatency);
            samples.add(VmVirtualDiskTotalWriteLatency);

            GaugeMetricFamily VmNetworkUsage = createMetric(VCenterNamespace.VmNetworkUsage);
            samples.add(VmNetworkUsage);
            GaugeMetricFamily VmNetworkPacketRx = createMetric(VCenterNamespace.VmNetworkPacketRx);
            samples.add(VmNetworkPacketRx);
            GaugeMetricFamily VmNetworkPacketTx = createMetric(VCenterNamespace.VmNetworkPacketTx);
            samples.add(VmNetworkPacketTx);
            GaugeMetricFamily VmNetworkReceived = createMetric(VCenterNamespace.VmNetworkReceived);
            samples.add(VmNetworkReceived);
            GaugeMetricFamily VmNetworkTransmitted = createMetric(VCenterNamespace.VmNetworkTransmitted);
            samples.add(VmNetworkTransmitted);
            GaugeMetricFamily VmNetworkByteRx = createMetric(VCenterNamespace.VmNetworkByteRx);
            samples.add(VmNetworkByteRx);
            GaugeMetricFamily VmNetworkByteTx = createMetric(VCenterNamespace.VmNetworkByteTx);
            samples.add(VmNetworkByteTx);

            GaugeMetricFamily HostCPUUsage = createMetric(VCenterNamespace.HostCPUUsage);
            samples.add(HostCPUUsage);
            GaugeMetricFamily HostCPUUsageMHZ = createMetric(VCenterNamespace.HostCPUUsageMHZ);
            samples.add(HostCPUUsageMHZ);
            GaugeMetricFamily HostCPUIdle = createMetric(VCenterNamespace.HostCPUIdle);
            samples.add(HostCPUIdle);
            GaugeMetricFamily HostCPUUsed = createMetric(VCenterNamespace.HostCPUUsed);
            samples.add(HostCPUUsed);

            GaugeMetricFamily HostMemoryUsage = createMetric(VCenterNamespace.HostMemoryUsage);
            samples.add(HostMemoryUsage);
            GaugeMetricFamily HostMemoryGranted = createMetric(VCenterNamespace.HostMemoryGranted);
            samples.add(HostMemoryGranted);
            GaugeMetricFamily HostMemoryActive = createMetric(VCenterNamespace.HostMemoryActive);
            samples.add(HostMemoryActive);
            GaugeMetricFamily HostMemoryHostMemCtl = createMetric(VCenterNamespace.HostMemoryHostMemCtl);
            samples.add(HostMemoryHostMemCtl);
            GaugeMetricFamily HostMemoryConsumed = createMetric(VCenterNamespace.HostMemoryConsumed);
            samples.add(HostMemoryConsumed);
            GaugeMetricFamily HostMemoryEntitlement = createMetric(VCenterNamespace.HostMemoryEntitlement);
            samples.add(HostMemoryEntitlement);

            GaugeMetricFamily HostDiskUsage = createMetric(VCenterNamespace.HostDiskUsage);
            samples.add(HostDiskUsage);
            GaugeMetricFamily HostDiskRead = createMetric(VCenterNamespace.HostDiskRead);
            samples.add(HostDiskRead);
            GaugeMetricFamily HostDiskWrite = createMetric(VCenterNamespace.HostDiskWrite);
            samples.add(HostDiskWrite);
            GaugeMetricFamily HostDiskMaxTotalLatency = createMetric(VCenterNamespace.HostDiskMaxTotalLatency);
            samples.add(HostDiskMaxTotalLatency);

            GaugeMetricFamily HostVirtualDiskNumberReadAveraged = createMetric(VCenterNamespace.HostVirtualDiskNumberReadAveraged);
            samples.add(HostVirtualDiskNumberReadAveraged);
            GaugeMetricFamily HostVirtualDiskNumberWriteAveraged = createMetric(VCenterNamespace.HostVirtualDiskNumberWriteAveraged);
            samples.add(HostVirtualDiskNumberWriteAveraged);
            GaugeMetricFamily HostVirtualDiskRead = createMetric(VCenterNamespace.HostVirtualDiskRead);
            samples.add(HostVirtualDiskRead);
            GaugeMetricFamily HostVirtualDiskWrite = createMetric(VCenterNamespace.HostVirtualDiskWrite);
            samples.add(HostVirtualDiskWrite);
            GaugeMetricFamily HostVirtualDiskTotalReadLatency = createMetric(VCenterNamespace.HostVirtualDiskTotalReadLatency);
            samples.add(HostVirtualDiskTotalReadLatency);
            GaugeMetricFamily HostVirtualDiskTotalWriteLatency = createMetric(VCenterNamespace.HostVirtualDiskTotalWriteLatency);
            samples.add(HostVirtualDiskTotalWriteLatency);

            GaugeMetricFamily HostNetworkUsage = createMetric(VCenterNamespace.HostNetworkUsage);
            samples.add(HostNetworkUsage);
            GaugeMetricFamily HostNetworkPacketRx = createMetric(VCenterNamespace.HostNetworkPacketRx);
            samples.add(HostNetworkPacketRx);
            GaugeMetricFamily HostNetworkPacketTx = createMetric(VCenterNamespace.HostNetworkPacketTx);
            samples.add(HostNetworkPacketTx);
            GaugeMetricFamily HostNetworkReceived = createMetric(VCenterNamespace.HostNetworkReceived);
            samples.add(HostNetworkReceived);
            GaugeMetricFamily HostNetworkTransmitted = createMetric(VCenterNamespace.HostNetworkTransmitted);
            samples.add(HostNetworkTransmitted);
            GaugeMetricFamily HostNetworkByteRx = createMetric(VCenterNamespace.HostNetworkByteRx);
            samples.add(HostNetworkByteRx);
            GaugeMetricFamily HostNetworkByteTx = createMetric(VCenterNamespace.HostNetworkByteTx);
            samples.add(HostNetworkByteTx);

            Map<String, PerfEntityMetricBase> metricBases = VCenterMetricsReader.getVmMetrics();
            for (Map.Entry<String, PerfEntityMetricBase> e : metricBases.entrySet()) {
                String vmUuid = e.getKey();
                PerfEntityMetricBase metricBase = e.getValue();

                PerfEntityMetric metric = (PerfEntityMetric)metricBase;
                for (int i = 0; i < metric.value.length; i++) {
                    PerfMetricIntSeries value = (PerfMetricIntSeries) metric.value[i];
                    /* in some case, vm is down, but vcenter return -1 */
                    if (Arrays.stream(value.value).noneMatch(v -> v >= 0)) {
                        continue;
                    }

                    switch (value.id.counterId) {
                        case VCenterMetricIDs.VM_CPU_USAGE_ID:
                            VmCPUUsage.addMetric(asList(vmUuid), average(value.value)/100.0);
                            break;

                        case VCenterMetricIDs.VM_CPU_USAGEMHZ_ID:
                            VmCPUUsageMHZ.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_CPU_USED_ID:
                            VmCPUUsed.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_CPU_IDLE_ID:
                            VmCPUIdle.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_GRANTED_ID:
                            VmMemoryGranted.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_ACTIVE_ID:
                            VmMemoryActive.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_VMMEMCTL_ID:
                            VmMemoryVmMemCtl.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_USAGE_ID:
                            VmMemoryUsage.addMetric(asList(vmUuid), average(value.value)/100.0);
                            break;

                        case VCenterMetricIDs.VM_MEMORY_CONSUME_ID:
                            VmMemoryConsumed.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_ENTITLEMENT_ID:
                            VmMemoryEntitlement.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_USAGE_ID:
                            VmDiskUsage.addMetric(asList(vmUuid, diskLetterChange(value.id.instance)), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_READ_ID:
                            VmDiskRead.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_WRITE_ID:
                            VmDiskWrite.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_MAX_TOTALLATENCY_ID:
                            VmDiskMaxTotalLatency.addMetric(asList(vmUuid, diskLetterChange(value.id.instance)), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_NUMBERREADAVERAGED_ID:
                            VmVirtualDiskNumberReadAveraged.addMetric(asList(vmUuid, value.id.instance), value.value[0]);
                            break;

                        case VCenterMetricIDs.VM_VDISK_NUMBERWRITEAVERAGED_ID:
                            VmVirtualDiskNumberWriteAveraged.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_READ_ID:
                            VmVirtualDiskRead.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_WRITE_ID:
                            VmVirtualDiskWrite.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_TOTALREADLATENCY_ID:
                            VmVirtualDiskTotalReadLatency.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_TOTALWRITELATENCY_ID:
                            VmVirtualDiskTotalWriteLatency.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_USAGE_ID:
                            VmNetworkUsage.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_PACKETRX_ID:
                            VmNetworkPacketRx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_PACKETTX_ID:
                            VmNetworkPacketTx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_BYTERX_ID:
                            VmNetworkByteRx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_BYTETX_ID:
                            VmNetworkByteTx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_RECEIVED_ID:
                            VmNetworkReceived.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_TRANSMITTED_ID:
                            VmNetworkTransmitted.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        default :
                            break;
                    }
                }
            }

            //do host metric
            Map<String, PerfEntityMetricBase> metricBasesHost = VCenterMetricsReader.getHostMetrics();
            for (Map.Entry<String, PerfEntityMetricBase> e : metricBasesHost.entrySet()) {
                String vmUuid = e.getKey();
                PerfEntityMetricBase metricBase = e.getValue();

                PerfEntityMetric metric = (PerfEntityMetric)metricBase;
                for (int i = 0; i < metric.value.length; i++) {
                    PerfMetricIntSeries value = (PerfMetricIntSeries) metric.value[i];
                    /* in some case, vm is down, but vcenter return -1 */
                    if (value.value[0] < 0) {
                        continue;
                    }

                    switch (value.id.counterId) {
                        case VCenterMetricIDs.VM_CPU_USAGE_ID:
                            // jira：ZSTAC-55930
                            if ("".equals(value.id.instance)){
                                HostCPUUsage.addMetric(asList(vmUuid), average(value.value)/100.0);
                            }
                            break;

                        case VCenterMetricIDs.VM_CPU_USAGEMHZ_ID:
                            HostCPUUsageMHZ.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_CPU_USED_ID:
                            HostCPUUsed.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_CPU_IDLE_ID:
                            HostCPUIdle.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_GRANTED_ID:
                            HostMemoryGranted.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_ACTIVE_ID:
                            HostMemoryActive.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_VMMEMCTL_ID:
                            HostMemoryHostMemCtl.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_USAGE_ID:
                            HostMemoryUsage.addMetric(asList(vmUuid), average(value.value)/100.0);
                            break;

                        case VCenterMetricIDs.VM_MEMORY_CONSUME_ID:
                            HostMemoryConsumed.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_MEMORY_ENTITLEMENT_ID:
                            HostMemoryEntitlement.addMetric(asList(vmUuid), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_USAGE_ID:
                            HostDiskUsage.addMetric(asList(vmUuid, diskLetterChange(value.id.instance)), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_READ_ID:
                            HostDiskRead.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_WRITE_ID:
                            HostDiskWrite.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_DISK_MAX_TOTALLATENCY_ID:
                            HostDiskMaxTotalLatency.addMetric(asList(vmUuid, diskLetterChange(value.id.instance)), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_NUMBERREADAVERAGED_ID:
                            HostVirtualDiskNumberReadAveraged.addMetric(asList(vmUuid, value.id.instance), value.value[0]);
                            break;

                        case VCenterMetricIDs.VM_VDISK_NUMBERWRITEAVERAGED_ID:
                            HostVirtualDiskNumberWriteAveraged.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_READ_ID:
                            HostVirtualDiskRead.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_WRITE_ID:
                            HostVirtualDiskWrite.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_TOTALREADLATENCY_ID:
                            HostVirtualDiskTotalReadLatency.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_VDISK_TOTALWRITELATENCY_ID:
                            HostVirtualDiskTotalWriteLatency.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_USAGE_ID:
                            HostNetworkUsage.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_PACKETRX_ID:
                            HostNetworkPacketRx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_PACKETTX_ID:
                            HostNetworkPacketTx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_BYTERX_ID:
                            HostNetworkByteRx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_BYTETX_ID:
                            HostNetworkByteTx.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_RECEIVED_ID:
                            HostNetworkReceived.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        case VCenterMetricIDs.VM_NETWORK_TRANSMITTED_ID:
                            HostNetworkTransmitted.addMetric(asList(vmUuid, value.id.instance), average(value.value));
                            break;

                        default :
                            break;
                    }
                }
            }

            return samples;
        }
    }

    @StaticInit
    static void staticInit() {
        PrometheusNamespace.namespacesClasses.put(VCenterNamespace.class, VCenterPrometheusNamespace.class);
        PrometheusCollector.registerMetricCollector(new VCenterCollector());
    }

    public VCenterPrometheusNamespace(Namespace namespace) {
        super(namespace);
    }

    @Override
    protected RecordingRule createRecordingRule(Metric metric) {
        RecordingRule r = new RecordingRule(true);
        metric.getLabelNames().forEach(l->r.labelMapping(l, l));
        return r;
    }
}
