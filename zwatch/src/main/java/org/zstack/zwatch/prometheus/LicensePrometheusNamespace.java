package org.zstack.zwatch.prometheus;

import io.prometheus.client.Collector;
import io.prometheus.client.GaugeMetricFamily;
import org.zstack.header.core.StaticInit;
import org.zstack.license.GetLicenseInfoTask;
import org.zstack.license.LicenseExpiredTimeInfo;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.zwatch.datatype.*;
import org.zstack.zwatch.datatype.metric.Metric;
import org.zstack.zwatch.namespace.LicenseNamespace;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;

public class LicensePrometheusNamespace extends AbstractPrometheusNamespace {
    private static final CLogger logger = Utils.getLogger(LicensePrometheusNamespace.class);

    public LicensePrometheusNamespace(Namespace namespace) {
        super(namespace);
    }

    @StaticInit
    static void staticInit() {
        PrometheusNamespace.namespacesClasses.put(LicenseNamespace.class, LicensePrometheusNamespace.class);
        PrometheusCollector.registerMetricCollector(new LicenseCollector());
    }

    public static class LicenseCollector implements MetricCollector {
        @Override
        public boolean skipManagementNodeCheck() {
            return false;
        }

        private String seriesName(String metricName) {
            return PrometheusNamespace.makeSeriesName(Namespace.zstackNamespaceName(LicenseNamespace.NAME), metricName);
        }

        private GaugeMetricFamily createMetric(Metric m) {
            return new GaugeMetricFamily(seriesName(m.getName()), String.format("help for %s", m.getName()), m.getLabelNames());
        }

        @Override
        public List<Collector.MetricFamilySamples> collect() {
            List<Collector.MetricFamilySamples> samples = new ArrayList<>();
            GaugeMetricFamily LicenseEnabledDays = createMetric(LicenseNamespace.LicenseEnabledDays);
            samples.add(LicenseEnabledDays);
            Map<String, LicenseExpiredTimeInfo> licenseAlarmInfoMap = GetLicenseInfoTask.getLicenseExpiredTimeInfoMap();

            licenseAlarmInfoMap.entrySet().forEach(v -> {
                double enabledDays = (double) v.getValue().getEnabledDays();
                LicenseEnabledDays.addMetric(asList(v.getKey(), v.getValue().getLicenseType()), enabledDays);
            });

            return samples;
        }

        @Override
        public String getCollectorName() {
            return LicenseCollector.class.getName();
        }
    }

    @Override
    protected RecordingRule createRecordingRule(Metric metric) {
        RecordingRule r = new RecordingRule(true);
        metric.getLabelNames().forEach(l -> r.labelMapping(l, l));
        return r;
    }
}
