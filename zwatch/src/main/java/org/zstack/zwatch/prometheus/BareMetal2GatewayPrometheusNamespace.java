package org.zstack.zwatch.prometheus;

import io.prometheus.client.Collector;
import io.prometheus.client.GaugeMetricFamily;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatchWithReturn;
import org.zstack.header.core.StaticInit;
import org.zstack.header.host.HostStatus;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.zwatch.datatype.Namespace;
import org.zstack.zwatch.namespace.*;
import java.util.ArrayList;
import java.util.List;

import static org.zstack.baremetal2.gateway.BareMetal2GatewayConstant.BM2_HYPERVISOR_TYPE;
/**
 * <AUTHOR>
*/
public class BareMetal2GatewayPrometheusNamespace extends HostPrometheusNamespace {
    public static class BareMetal2GatewayCollector implements MetricCollector {
        private String seriesName(String metricName) {
            return PrometheusNamespace.makeSeriesName(Namespace.zstackNamespaceName(BareMetal2GatewayNamespace.NAME), metricName);
        }

        @Override
        public boolean skipManagementNodeCheck() {
            return false;
        }

        @Override
        public List<Collector.MetricFamilySamples> collect() {
            return new SQLBatchWithReturn<List<Collector.MetricFamilySamples>>() {
                @Override
                protected List<Collector.MetricFamilySamples> scripts() {
                    List<Collector.MetricFamilySamples> samples = new ArrayList<>();
                    createBaremetal2GateWayMetrics(samples);
                    return samples;
                }
                private void createBaremetal2GateWayMetrics(List<Collector.MetricFamilySamples> samples) {
                    Long total = Q.New(HostVO.class).eq(HostVO_.hypervisorType, BM2_HYPERVISOR_TYPE).count();

                    Long connected = Q.New(HostVO.class).eq(HostVO_.hypervisorType, BM2_HYPERVISOR_TYPE)
                            .eq(HostVO_.status, HostStatus.Connected).count();
                    Long disconnected = Q.New(HostVO.class).eq(HostVO_.hypervisorType, BM2_HYPERVISOR_TYPE)
                            .eq(HostVO_.status, HostStatus.Disconnected).count();

                    samples.add(new GaugeMetricFamily(
                            seriesName(BareMetal2GatewayNamespace.Baremetal2GateWayTotal.getName()),
                            "help for Baremetal2GateWayTotal",
                            total.doubleValue()));
                    samples.add(new GaugeMetricFamily(
                            seriesName(BareMetal2GatewayNamespace.Baremetal2ConnectedGateWayCount.getName()),
                            "help for Baremetal2ConnectedGateWayCount",
                            connected.doubleValue()));
                    samples.add(new GaugeMetricFamily(
                            seriesName(BareMetal2GatewayNamespace.Baremetal2DisconnectedGateWayCount.getName()),
                            "help for Baremetal2DisconnectedGateWayCount",
                            disconnected.doubleValue()));
                    samples.add(new GaugeMetricFamily(
                            seriesName(BareMetal2GatewayNamespace.Baremetal2ConnectedGateWayInPercent.getName()),
                            "help for Baremetal2ConnectedGateWayInPercent",
                            total == 0 ? 0 : connected.doubleValue() / total.doubleValue() * 100));
                    samples.add(new GaugeMetricFamily(
                            seriesName(BareMetal2GatewayNamespace.Baremetal2DisconnectedGateWayInPercent.getName()),
                            "help for Baremetal2DisconnectedGateWayInPercent",
                            total == 0 ? 0 : disconnected.doubleValue() / total.doubleValue() * 100));
                }
            }.execute();
        }

        @Override
        public String getCollectorName() {
            return BareMetal2GatewayCollector.class.getName();
        }

    }

    @StaticInit
    static void staticInit() {
        PrometheusNamespace.namespacesClasses.put(BareMetal2GatewayNamespace.class, BareMetal2GatewayPrometheusNamespace.class);
        PrometheusCollector.registerMetricCollector(new BareMetal2GatewayCollector());
    }

    public BareMetal2GatewayPrometheusNamespace(Namespace namespace) {
        super(namespace);
    }
}
