package org.zstack.zwatch.prometheus;

import org.zstack.header.core.StaticInit;
import org.zstack.zwatch.ZWatchConstants;
import org.zstack.zwatch.datatype.metric.Metric;
import org.zstack.zwatch.datatype.Namespace;
import org.zstack.zwatch.namespace.Baremetal2VmNamespace;

public class Baremetal2VmPrometheusNamespace extends AbstractPrometheusNamespace {

    @StaticInit
    static void staticInit() {
        PrometheusNamespace.namespacesClasses.put(Baremetal2VmNamespace.class, Baremetal2VmPrometheusNamespace.class);
    }

    protected RecordingRule createRecordingRule(Metric metric) {
        RecordingRule rule = new RecordingRule(makeSeriesName(metric.getName()));
        rule.labelMapping("virt", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());

        if (metric == Baremetal2VmNamespace.OperatingSystemCPUSystemUtilization) {
            rule.setExpression("bm2_collectd_cpu_percent{type=\"system\", vmUuid!=\"\"}");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("cpu", Baremetal2VmNamespace.LabelNames.CPUNum.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUUserUtilization) {
            rule.setExpression("bm2_collectd_cpu_percent{type=\"user\", vmUuid!=\"\"}");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("cpu", Baremetal2VmNamespace.LabelNames.CPUNum.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUWaitUtilization) {
            rule.setExpression("bm2_collectd_cpu_percent{type=\"wait\", vmUuid!=\"\"}");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("cpu", Baremetal2VmNamespace.LabelNames.CPUNum.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUIdleUtilization) {
            rule.setExpression("(sum(bm2_collectd_cpu_percent{type=\"idle\", vmUuid!=\"\"}) by(vmUuid) / sum(bm2_collectd_cpu_percent) by(vmUuid)) * 100");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("cpu", Baremetal2VmNamespace.LabelNames.CPUNum.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUUsedUtilization) {
            rule.setExpression("100 - bm2_collectd_cpu_percent{type=\"idle\", vmUuid!=\"\"}");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("cpu", Baremetal2VmNamespace.LabelNames.CPUNum.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUAverageSystemUtilization) {
            rule.setExpression("avg(bm2_collectd_cpu_percent{type=\"system\", vmUuid!=\"\"}) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUAverageUserUtilization) {
            rule.setExpression("avg(bm2_collectd_cpu_percent{type=\"user\", vmUuid!=\"\"}) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUAverageWaitUtilization) {
            rule.setExpression("avg(bm2_collectd_cpu_percent{type=\"wait\", vmUuid!=\"\"}) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUAverageIdleUtilization) {
            rule.setExpression("avg(bm2_collectd_cpu_percent{type=\"idle\", vmUuid!=\"\"}) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemCPUAverageUsedUtilization) {
            rule.setExpression("avg(100 - bm2_collectd_cpu_percent{type=\"idle\", vmUuid!=\"\"}) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemMemoryTotalBytes) {
            rule.setExpression("sum(bm2_collectd_memory{vmUuid!=\"\"}) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemMemoryFreeBytes) {
            rule.setExpression("bm2_collectd_memory{vmUuid!=\"\",  memory=\"free\"}");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemMemoryUsedBytes) {
            rule.setExpression("bm2_collectd_memory{vmUuid!=\"\",  memory=\"used\"}");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemMemoryAvailableBytes) {
            rule.setExpression("bm2_node_memory_available {vmUuid!=\"\"}");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemMemoryFreePercent) {
            rule.setExpression("100 * (sum(bm2_collectd_memory{memory=\"free\", vmUuid!=\"\"}) by (vmUuid))/ (sum(bm2_collectd_memory{vmUuid!=\"\"}) by (vmUuid))");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemMemoryUsedPercent) {
            rule.setExpression("100 * (sum(bm2_collectd_memory{memory=\"used\", vmUuid!=\"\"}) by (vmUuid))/ (sum(bm2_collectd_memory{vmUuid!=\"\"}) by (vmUuid))");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.DiskFreeCapacityInBytes) {
            rule.setExpression(String.format("bm2_node_filesystem_avail{vmUuid!=\"\", mountpoint!~\"%s\"}", ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskFreeCapacityInPercent) {
            // +1 to handle the case where node_filesystem_total is 0
            // one byte won't effect the accuracy of disk usage too much
            rule.setExpression(String.format("((bm2_node_filesystem_avail{vmUuid!=\"\", mountpoint!~\"%s\"}  + 1) / (bm2_node_filesystem_size{vmUuid!=\"\", mountpoint!~\"%s\"} + 1)) * 100", ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskUsedCapacityInBytes) {
            rule.setExpression(String.format("bm2_node_filesystem_size{vmUuid!=\"\", mountpoint!~\"%s\"} - bm2_node_filesystem_avail{vmUuid!=\"\", mountpoint!~\"%s\"}", ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskUsedCapacityInPercent) {
            rule.setExpression(String.format("(((bm2_node_filesystem_size{vmUuid!=\"\", mountpoint!~\"%s\"} - bm2_node_filesystem_avail{vmUuid!=\"\", mountpoint!~\"%s\"}) + 1) / (bm2_node_filesystem_size{vmUuid!=\"\", mountpoint!~\"%s\"}  + 1)) * 100", ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskTotalCapacityInBytes) {
            rule.setExpression(String.format("bm2_node_filesystem_size{vmUuid!=\"\", mountpoint!~\"%s\"}", ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskReadBytesPerSecond) {
            rule.setExpression(String.format("irate(bm2_node_disk_bytes_read{vmUuid!=\"\", mountpoint!~\"%s\"}[10m])", ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskReadRequestPerSecond) {
            rule.setExpression(String.format("irate(bm2_node_disk_reads_completed{vmUuid!=\"\", mountpoint!~\"%s\"}[10m])", ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskWriteBytesPerSecond) {
            rule.setExpression(String.format("irate(bm2_node_disk_bytes_written{vmUuid!=\"\", mountpoint!~\"%s\"}[10m])", ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskWriteRequestPerSecond) {
            rule.setExpression(String.format("irate(bm2_node_disk_writes_completed{vmUuid!=\"\", mountpoint!~\"%s\"}[10m])", ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.DiskDeviceLetter.toString());
            rule.labelMapping("mountpoint", Baremetal2VmNamespace.LabelNames.MountPoint.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
            rule.labelMapping("fstype", Baremetal2VmNamespace.LabelNames.FSType.toString());
        } else if (metric == Baremetal2VmNamespace.DiskAllFreeCapacityInBytes) {
            rule.setExpression(String.format("sum(bm2_node_filesystem_avail{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"}) by(vmUuid)", ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.DiskAllFreeCapacityInPercent) {
            rule.setExpression(String.format("(sum(bm2_node_filesystem_avail{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"}) by(vmUuid) / sum(bm2_node_filesystem_size{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"}) by(vmUuid)) * 100", ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.DiskAllUsedCapacityInBytes) {
            rule.setExpression(String.format("sum(bm2_node_filesystem_size{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"} - bm2_node_filesystem_avail{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"}) by(vmUuid)", ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.DiskAllUsedCapacityInPercent) {
            rule.setExpression(String.format("(sum(bm2_node_filesystem_size{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"} - bm2_node_filesystem_avail{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"}) by(vmUuid) / sum(bm2_node_filesystem_size{vmUuid!=\"\", fstype!=\"rootfs\", mountpoint!~\"%s\"}) by(vmUuid)) * 100", ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX, ZWatchConstants.WINDOWS_HARDDISK_PREFIX));
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkInBytes) {
            rule.setExpression("irate(bm2_node_network_receive_bytes{vmUuid!=\"\"}[10m])");
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.NetworkDeviceLetter.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkAllInBytes) {
            rule.setExpression("sum(irate(bm2_node_network_receive_bytes{vmUuid!=\"\"}[10m])) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkInPackets) {
            rule.setExpression("irate(bm2_node_network_receive_packets{vmUuid!=\"\"}[10m])");
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.NetworkDeviceLetter.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkAllInPackets) {
            rule.setExpression("sum(irate(bm2_node_network_receive_packets{vmUuid!=\"\"}[10m])) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkInErrors) {
            rule.setExpression("irate(bm2_node_network_receive_errs{vmUuid!=\"\"}[10m])");
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.NetworkDeviceLetter.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkAllInErrors) {
            rule.setExpression("sum(irate(bm2_node_network_receive_errs{vmUuid!=\"\"}[10m])) by (vmUuid)");
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.NetworkDeviceLetter.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkOutBytes) {
            rule.setExpression("irate(bm2_node_network_transmit_bytes{vmUuid!=\"\"}[10m])");
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.NetworkDeviceLetter.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkAllOutBytes) {
            rule.setExpression("sum(irate(bm2_node_network_transmit_bytes{vmUuid!=\"\"}[10m])) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkOutPackets) {
            rule.setExpression("irate(bm2_node_network_transmit_packets{vmUuid!=\"\"}[10m])");
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.NetworkDeviceLetter.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkAllOutPackets) {
            rule.setExpression("sum(irate(bm2_node_network_transmit_packets{vmUuid!=\"\"}[10m])) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkOutErrors) {
            rule.setExpression("irate(bm2_node_network_transmit_errs{vmUuid!=\"\"}[10m])");
            rule.labelMapping("device", Baremetal2VmNamespace.LabelNames.NetworkDeviceLetter.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.OperatingSystemNetworkAllOutErrors) {
            rule.setExpression("sum(irate(bm2_node_network_transmit_errs{vmUuid!=\"\"}[10m])) by (vmUuid)");
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.GpuPowerDraw) {
            rule.setExpression("bm2_gpu_power_draw");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.GpuTemperature) {
            rule.setExpression("bm2_gpu_temperature");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.GpuFanSpeed) {
            rule.setExpression("bm2_gpu_fan_speed");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.GpuUtilization) {
            rule.setExpression("bm2_gpu_utilization");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.GpuMemoryUtilization) {
            rule.setExpression("bm2_gpu_memory_utilization");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.GpuPciRxThroughputInBytes) {
            rule.setExpression("irate(bm2_gpu_rxpci_in_bytes[10m])");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else if (metric == Baremetal2VmNamespace.GpuPciTxThroughputInBytes) {
            rule.setExpression("irate(bm2_gpu_txpci_in_bytes[10m])");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        }  else if (metric == Baremetal2VmNamespace.GpuStatus) {
            rule.setExpression("bm2_gpu_status");
            rule.labelMapping("pci_device_address", Baremetal2VmNamespace.LabelNames.PciDeviceAddress.toString());
            rule.labelMapping("gpuStatus", Baremetal2VmNamespace.LabelNames.GpuStatus.toString());
            rule.labelMapping("gpu_serial", Baremetal2VmNamespace.LabelNames.GpuSerialNumber.toString());
            rule.labelMapping("vmUuid", Baremetal2VmNamespace.LabelNames.Baremetal2VMUuid.toString());
        } else {
            rule.setForLabelMappingOnly(true);
            metric.getLabelNames().forEach(l->rule.labelMapping(l, l));
            return rule;
        }

        return rule;
    }

    public Baremetal2VmPrometheusNamespace(Namespace namespace) {
        super(namespace);
    }
}
