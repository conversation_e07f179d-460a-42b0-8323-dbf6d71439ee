package org.zstack.zwatch.prometheus;

import io.prometheus.client.Collector;
import io.prometheus.client.GaugeMetricFamily;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatchWithReturn;
import org.zstack.header.core.StaticInit;
import org.zstack.header.host.HostStatus;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.zwatch.datatype.Namespace;
import org.zstack.zwatch.namespace.ESXHostNamespace;

import java.util.ArrayList;
import java.util.List;

import static org.zstack.vmware.ESXConstant.VMWARE_HYPERVISOR_TYPE;

/**
 * <AUTHOR>
 * @date 2022/8/26 11:20
 */
public class ESXHostPrometheusNamespace extends HostPrometheusNamespace {
    public static class ESXHostCollector implements MetricCollector {
        private String seriesName(String metricName) {
            return PrometheusNamespace.makeSeriesName(Namespace.zstackNamespaceName(ESXHostNamespace.NAME), metricName);
        }

        @Override
        public boolean skipManagementNodeCheck() {
            return false;
        }

        @Override
        public List<Collector.MetricFamilySamples> collect() {
            return new SQLBatchWithReturn<List<Collector.MetricFamilySamples>>() {
                @Override
                protected List<Collector.MetricFamilySamples> scripts() {
                    List<Collector.MetricFamilySamples> samples = new ArrayList<>();
                    createESXHostMetrics(samples);
                    return samples;
                }

                private void createESXHostMetrics(List< Collector.MetricFamilySamples > samples) {
                    Long total = Q.New(HostVO.class).eq(HostVO_.hypervisorType, VMWARE_HYPERVISOR_TYPE).count();

                    Long connected = Q.New(HostVO.class).eq(HostVO_.hypervisorType, VMWARE_HYPERVISOR_TYPE)
                            .eq(HostVO_.status, HostStatus.Connected).count();
                    Long disconnected = Q.New(HostVO.class).eq(HostVO_.hypervisorType, VMWARE_HYPERVISOR_TYPE)
                            .eq(HostVO_.status, HostStatus.Disconnected).count();

                    samples.add(new GaugeMetricFamily(
                            seriesName(ESXHostNamespace.ESXHostTotal.getName()),
                            "help for ESXHostTotal", total.doubleValue()));
                    samples.add(new GaugeMetricFamily(
                            seriesName(ESXHostNamespace.ESXConnectedHostCount.getName()),
                            "help for ESXConnectedHostCount",
                            connected.doubleValue()));
                    samples.add(new GaugeMetricFamily(seriesName(ESXHostNamespace.ESXDisconnectedHostCount.getName()),
                            "help for ESXDisconnectedHostCount",
                            disconnected.doubleValue()));
                    samples.add(new GaugeMetricFamily(seriesName(ESXHostNamespace.ESXConnectedHostInPercent.getName()),
                            "help for ESXConnectedHostInPercent",
                            total == 0 ? 0 : connected.doubleValue() / total.doubleValue() * 100));
                    samples.add(new GaugeMetricFamily(seriesName(ESXHostNamespace.ESXDisconnectedHostInPercent.getName()),
                            "help for ESXDisconnectedHostInPercent",
                            total == 0 ? 0 : disconnected.doubleValue() / total.doubleValue() * 100));
                }

            }.execute();
        }

        @Override
        public String getCollectorName() {
            return ESXHostCollector.class.getName();
        }

    }

    @StaticInit
    static void staticInit() {
        PrometheusNamespace.namespacesClasses.put(ESXHostNamespace.class, ESXHostPrometheusNamespace.class);
        PrometheusCollector.registerMetricCollector(new ESXHostCollector());
    }

    public ESXHostPrometheusNamespace(Namespace namespace) {
        super(namespace);
    }
}
