package org.zstack.zwatch.prometheus;

import io.prometheus.client.Collector;
import io.prometheus.client.GaugeMetricFamily;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatchWithReturn;
import org.zstack.header.core.StaticInit;
import org.zstack.header.storage.cdp.CdpTaskStatus;
import org.zstack.header.storage.cdp.CdpTaskVO;
import org.zstack.header.storage.cdp.CdpTaskVO_;
import org.zstack.zwatch.datatype.metric.Metric;
import org.zstack.zwatch.datatype.Namespace;
import org.zstack.zwatch.namespace.CdpTaskNamespace;

import java.util.ArrayList;
import java.util.List;

import static java.util.Arrays.asList;

public class CdpTaskPrometheusNamespace extends AbstractPrometheusNamespace {
    public static class CdpTaskCollector implements MetricCollector {
        private String seriesName(String metricName) {
            return PrometheusNamespace.makeSeriesName(Namespace.zstackNamespaceName(CdpTaskNamespace.NAME), metricName);
        }

        private GaugeMetricFamily createMetric(Metric m) {
            return new GaugeMetricFamily(seriesName(m.getName()), String.format("help for %s", m.getName()), m.getLabelNames());
        }

        @Override
        public boolean skipManagementNodeCheck() {
            return false;
        }

        @Override
        public List<Collector.MetricFamilySamples> collect() {
            return new SQLBatchWithReturn<List<Collector.MetricFamilySamples>>() {
                private List<Collector.MetricFamilySamples> createCapacitySamples() {
                    List<Collector.MetricFamilySamples> samples = new ArrayList<>();

                    List<CdpTaskVO> cdpTasks = Q.New(CdpTaskVO.class)
                            .eq(CdpTaskVO_.status, CdpTaskStatus.Running)
                            .list();

                    GaugeMetricFamily CdpTaskUsedCapacityInPercent = createMetric(CdpTaskNamespace.CdpTaskUsedCapacityInPercent);
                    GaugeMetricFamily CdpTaskLatency = createMetric(CdpTaskNamespace.CdpTaskLatency);
                    samples.add(CdpTaskUsedCapacityInPercent);
                    samples.add(CdpTaskLatency);

                    cdpTasks.forEach(cdpTask -> {
                        CdpTaskUsedCapacityInPercent.addMetric(asList(cdpTask.getUuid(), cdpTask.getStatus().toString()), ((double) cdpTask.getUsedCapacity() / cdpTask.getMaxCapacity()) * 100);
                        CdpTaskLatency.addMetric(asList(cdpTask.getUuid(), cdpTask.getStatus().toString()), ((double) cdpTask.getLastLatency()));
                    });

                    return samples;
                }

                @Override
                protected List<Collector.MetricFamilySamples> scripts() {
                    List<Collector.MetricFamilySamples> samples = new ArrayList<>();

                    samples.addAll(createCapacitySamples());

                    return samples;
                }
            }.execute();
        }

        @Override
        public String getCollectorName() {
            return CdpTaskCollector.class.getName();
        }
    }

    public CdpTaskPrometheusNamespace(Namespace namespace) {
        super(namespace);
    }

    @StaticInit
    static void staticInit() {
        namespacesClasses.put(CdpTaskNamespace.class, CdpTaskPrometheusNamespace.class);
        PrometheusCollector.registerMetricCollector(new CdpTaskCollector());
    }

    @Override
    protected RecordingRule createRecordingRule(Metric metric) {
        RecordingRule r = new RecordingRule(true);
        metric.getLabelNames().forEach(l -> r.labelMapping(l, l));
        return r;
    }
}
