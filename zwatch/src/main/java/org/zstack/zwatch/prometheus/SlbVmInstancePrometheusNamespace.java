package org.zstack.zwatch.prometheus;

import org.zstack.header.core.StaticInit;
import org.zstack.zwatch.datatype.Namespace;
import org.zstack.zwatch.namespace.SlbVmInstanceNamespace;

public class SlbVmInstancePrometheusNamespace extends VmPrometheusNamespace{
    @StaticInit
    static void staticInit() {
        PrometheusNamespace.namespacesClasses.put(SlbVmInstanceNamespace.class, org.zstack.zwatch.prometheus.SlbVmInstancePrometheusNamespace.class);
    }

    public SlbVmInstancePrometheusNamespace(Namespace namespace) {
        super(namespace);
    }
}